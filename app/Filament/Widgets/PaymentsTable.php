<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\Payments\Schemas\PaymentForm;
use App\Models\Payment;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\EditAction;
use Filament\Schemas\Schema;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class PaymentsTable extends TableWidget
{
    public ?Model $payable = null;

    public ?Model $party = null;

    public function table(Table $table): Table
    {
        return $table
            ->query(function (): Builder {
                return Payment::query()
                    ->when($this->payable, function (Builder $query) {
                        $query->whereMorphedTo('payable', $this->payable);
                    })
                    ->when($this->party, function (Builder $query) {
                        $query->whereMorphedTo('party', $this->party);
                    });
            })
            ->columns(\App\Filament\Resources\Payments\Tables\PaymentsTable::configure($table)->getColumns())
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->relationship('paymentMethod', 'name')
                    ->preload(),
            ])
            ->headerActions([
                //
            ])
            ->recordActions([
                EditAction::make()
                    ->schema(PaymentForm::configure(new Schema())->getComponents()),
//                Action::make('edit')
//                    ->label(__('Edit'))
//                    ->icon(Heroicon::PencilSquare)
//                    ->schema(PaymentForm::configure(new Schema())->getComponents()),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    //
                ]),
            ]);
    }
}

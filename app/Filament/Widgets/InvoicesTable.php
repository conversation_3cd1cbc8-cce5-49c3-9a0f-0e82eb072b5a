<?php

namespace App\Filament\Widgets;

use App\Enums\PaymentPayoutStatus;
use App\Filament\Tables\Columns\EnumColumn;
use App\Models\Invoice;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class InvoicesTable extends TableWidget
{
    public ?Model $invoiceable = null;

    public ?Model $party = null;

    public function table(Table $table): Table
    {
        return $table
            ->query(function (): Builder {
                return Invoice::query()
                    ->with('invoiceable')
                    ->when($this->invoiceable, function (Builder $query) {
                        $query->whereMorphedTo('invoiceable', $this->invoiceable);
                    })
                    ->when($this->party, function (Builder $query) {
                        $query->whereMorphedTo('party', $this->party);
                    });
            })
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('invoiceable.raw_title')
                    ->label(__('Invoiceable'))
                    ->color('primary')
                    ->url(function (Invoice $record) {
                        return $record->invoiceable->getInvoiceableFilamentUrl();
                    }),

                EnumColumn::make('status'),

                TextColumn::make('total_amount')
                    ->label(__('Total Amount'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (Invoice $record) {
                        return $record->getPriceLabels('total_amount');
                    }),

                TextColumn::make('paid')
                    ->label(__('Paid'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (Invoice $record) {
                        return $record->invoiceable?->getPriceLabels('paid');
                    }),

                TextColumn::make('remaining')
                    ->label(__('Remaining'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (Invoice $record) {
                        return $record->invoiceable?->getPriceLabels('remaining');
                    }),

            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(fn() => PaymentPayoutStatus::getSelectOptions())
                    ->query(function ($query, $data) {
                        $value = $data['value'];
                        if ($value) {
                            match ($value) {
                                PaymentPayoutStatus::FULLY_PAID->value => $query->whereHas('invoiceable', function ($query) {
                                    $query->fullyPaid();
                                }),
                                PaymentPayoutStatus::PARTIALLY_PAID->value => $query->whereHas('invoiceable', function ($query) {
                                    $query->partiallyPaid();
                                }),
                                PaymentPayoutStatus::NO_PAYMENT->value => $query->whereHas('invoiceable', function ($query) {
                                    $query->noPayments();
                                }),
                            };
                        }
                    }),
            ])
            ->headerActions([
            ])
            ->recordActions([
                Action::make('view_invoice')
                    ->label(__('View Invoice'))
                    ->icon(Heroicon::Eye)
                    ->url(function (Invoice $record) {
                        return \App\Filament\Pages\Invoice::getUrl([
                            'invoice_id' => $record->id,
                        ]);
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    //
                ]),
            ]);
    }
}

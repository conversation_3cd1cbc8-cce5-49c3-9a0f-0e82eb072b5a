<?php

namespace App\Filament\Widgets;

use App\Enums\StockMovementType;
use App\Filament\Tables\Columns\EnumColumn;
use App\Models\StockMovement;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class StockMovementsTable extends TableWidget
{
    public ?Model $product = null;

    public ?Model $related = null;

    public function table(Table $table): Table
    {
        return $table
            ->query(function (): Builder {
                return StockMovement::query()
                    ->when($this->product, function (Builder $query) {
                        $query->where('product_id', $this->product->id);
                    })
                    ->when($this->related, function (Builder $query) {
                        $query->where('related_id', $this->related->id)
                            ->where('related_type', $this->related->getMorphClass());
                    });
            })
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                EnumColumn::make('type')
                    ->label(__('Type')),

                TextColumn::make('quantity')
                    ->label(__('Quantity'))
                    ->sortable(),

                TextColumn::make('description')
                    ->label(__('Description'))
                    ->searchable(),

                TextColumn::make('notes')
                    ->label(__('Notes'))
                    ->searchable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([])
            ->headerActions([
                Action::make('update_stock')
                    ->label(__('Update Stock'))
                    ->icon(Heroicon::RectangleStack)
                    ->model(StockMovement::class)
                    ->schema(function () {
                        return [
                            Select::make('type')
                                ->options(StockMovementType::getSelectOptions())
                                ->reactive()
                                ->afterStateUpdated(function ($state, $set) {
                                    $set('quantity', null);
                                })
                                ->required(),

                            TextInput::make('quantity')
                                ->label(__('Quantity'))
                                ->numeric()
                                ->required()
                                ->minValue(1),

                            Textarea::make('notes')
                                ->label(__('Notes'))
                                ->required(),
                        ];
                    })
                    ->action(function ($data) {
                        StockMovement::query()
                            ->create(array_merge($data, [
                                'product_id' => $this->product->id,
                            ]));

                        Notification::make('stock_movement_created')
                            ->title(__('Stock updated'))
                            ->success()
                            ->send();
                    })
            ])
            ->recordActions([])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}

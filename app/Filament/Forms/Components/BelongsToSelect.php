<?php

namespace App\Filament\Forms\Components;

use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BelongsToSelect extends Select
{
    protected bool | \Closure $showCreateAction = true;

    protected \Closure | null $afterCreateAction = null;

    protected \Closure | null | string $position = 'belowContent';

    protected function setUp(): void
    {
        parent::setUp();

        $this->belowContent(
            function (BelongsToSelect $component) {
                return $component->getCreateAction();
            }
        );
    }

    public function getCreateAction()
    {
        $component = $this;

        if (!$component->isShowCreateAction()) {
            return null;
        }


        $relationName = $component->getRelationshipName();
        $relatedModel = app($component->getModel())->{$relationName}()->getRelated();
        $resource = Filament::getModelResource(get_class($relatedModel));

        if (!$resource) {
            return null;
        }

        return Action::make("create_{$relationName}")
            ->label(__('Create new'))
            ->model(get_class($relatedModel))
            ->icon(Heroicon::OutlinedPlus)
            ->schema($resource::form(new Schema())->getComponents())
            ->action(function (array $data, $set, Schema $form) use ($relationName, $relatedModel, $component) {
                try {
                    DB::beginTransaction();

                    $record = $relatedModel->fill($data);
                    $record->save();

                    $form->saveRelationships();

                    $set($component->getName(), $record->id);

                    if ($component->getAfterCreateAction()) {
                        $component->evaluate($component->getAfterCreateAction(), [
                            'createdRecord' => $record,
                        ]);
                    }

                    DB::commit();
                }catch (\Exception $e) {
                    DB::rollBack();

                    Notification::make('create_failed')
                        ->title(__('Failed to create'))
                        ->danger()
                        ->send();

                    Log::error($e->getMessage(), [
                        'exception' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                }
            });
    }

    public function showCreateAction(bool | \Closure $showCreateAction = true): static
    {
        $this->showCreateAction = $showCreateAction;

        return $this;
    }

    public function afterCreateAction(\Closure $afterCreateAction): static
    {
        $this->afterCreateAction = $afterCreateAction;

        return $this;
    }

    public function position(\Closure | string | null $position): static
    {
        $this->position = $position;

        return $this;
    }

    public function getCreateActionPosition(): string
    {
        return $this->evaluate($this->position);
    }

    public function getAfterCreateAction(): \Closure | null
    {
        return $this->afterCreateAction;
    }

    public function isShowCreateAction(): bool
    {
        return $this->evaluate($this->showCreateAction);
    }
}

<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class Invoice extends Page
{
    public $invoiceId = null;

    public $invoice = null;

    protected string $view = 'filament.pages.invoice';

    protected static bool $shouldRegisterNavigation = false;

    public function mount()
    {
        $this->invoiceId = request()->get('invoice_id');
        $this->invoice = \App\Models\Invoice::findOrFail($this->invoiceId);
    }
}

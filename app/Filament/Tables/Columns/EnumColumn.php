<?php

namespace App\Filament\Tables\Columns;

use Filament\Support\Enums\TextSize;
use Filament\Tables\Columns\Column;
use Filament\Tables\Columns\TextColumn;

class EnumColumn extends TextColumn
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->badge()
            ->size(TextSize::Large)
            ->color(function ($record, $column) {
                $name = $column->getName();
                return $record->{$name}?->color();
            })
            ->getStateUsing(function ($record, $column) {
                $name = $column->getName();
                return $record->{$name}?->label();
            });
    }
}

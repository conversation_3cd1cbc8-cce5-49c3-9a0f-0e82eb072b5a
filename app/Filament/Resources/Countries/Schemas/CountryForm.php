<?php

namespace App\Filament\Resources\Countries\Schemas;

use App\Filament\Schemas\Components\Translateable;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class CountryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Translateable::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('code')
                    ->label(__('Code')),
            ])
            ->columns(1);
    }
}

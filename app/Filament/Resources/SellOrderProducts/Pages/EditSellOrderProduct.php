<?php

namespace App\Filament\Resources\SellOrderProducts\Pages;

use App\Filament\Resources\SellOrderProducts\SellOrderProductResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditSellOrderProduct extends EditRecord
{
    protected static string $resource = SellOrderProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

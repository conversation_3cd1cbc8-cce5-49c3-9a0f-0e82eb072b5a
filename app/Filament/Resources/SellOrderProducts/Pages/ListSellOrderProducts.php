<?php

namespace App\Filament\Resources\SellOrderProducts\Pages;

use App\Filament\Resources\SellOrderProducts\SellOrderProductResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListSellOrderProducts extends ListRecords
{
    protected static string $resource = SellOrderProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

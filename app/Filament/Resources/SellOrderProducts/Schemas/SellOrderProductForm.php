<?php

namespace App\Filament\Resources\SellOrderProducts\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Schema;

class SellOrderProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                BelongsToSelect::make('sell_order_id')
                    ->label(__('Sell Order'))
                    ->relationship('sellOrder', 'id')
                    ->required(),

                BelongsToSelect::make('product_id')
                    ->label(__('Product'))
                    ->relationship('product', 'name')
                    ->required(),

                BelongsToSelect::make('package_type_id')
                    ->label(__('Package Type'))
                    ->relationship('packageType', 'name')
                    ->required(),

                Grid::make(3)
                    ->schema([
                        TextInput::make('width')
                            ->label(__('Width'))
                            ->numeric()
                            ->minValue(0),

                        TextInput::make('height')
                            ->label(__('Height'))
                            ->numeric()
                            ->minValue(0),

                        TextInput::make('length')
                            ->label(__('Length'))
                            ->numeric()
                            ->minValue(0),
                    ]),

                Grid::make(2)
                    ->schema([
                        TextInput::make('net_weight')
                            ->label(__('Net Weight'))
                            ->numeric()
                            ->minValue(0),

                        TextInput::make('gross_weight')
                            ->label(__('Gross Weight'))
                            ->numeric()
                            ->required()
                            ->minValue(0),
                    ]),

                TextInput::make('quantity')
                    ->label(__('Quantity'))
                    ->numeric()
                    ->required(),
            ])
            ->columns(1);
    }
}

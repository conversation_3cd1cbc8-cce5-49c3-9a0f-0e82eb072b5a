<?php

namespace App\Filament\Resources\SellOrderProducts;

use App\Filament\Resources\SellOrderProducts\Pages\CreateSellOrderProduct;
use App\Filament\Resources\SellOrderProducts\Pages\EditSellOrderProduct;
use App\Filament\Resources\SellOrderProducts\Pages\ListSellOrderProducts;
use App\Filament\Resources\SellOrderProducts\Schemas\SellOrderProductForm;
use App\Filament\Resources\SellOrderProducts\Tables\SellOrderProductsTable;
use App\Models\SellOrderProduct;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class SellOrderProductResource extends Resource
{
    protected static ?string $model = SellOrderProduct::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static bool $shouldRegisterNavigation = false;

    public static function getLabel(): ?string
    {
        return __('Sell Order Product');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Sell Order Products');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Sales');
    }

    public static function form(Schema $schema): Schema
    {
        return SellOrderProductForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return SellOrderProductsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSellOrderProducts::route('/'),
            'create' => CreateSellOrderProduct::route('/create'),
            'edit' => EditSellOrderProduct::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

<?php

namespace App\Filament\Resources\RawProducts;

use App\Enums\ProductType;
use App\Filament\Resources\RawProducts\Pages\CreateRawProduct;
use App\Filament\Resources\RawProducts\Pages\EditRawProduct;
use App\Filament\Resources\RawProducts\Pages\ListRawProducts;
use App\Filament\Resources\RawProducts\Schemas\RawProductForm;
use App\Filament\Resources\RawProducts\Tables\RawProductsTable;
use App\Models\Product;
use App\Models\RawProduct;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class RawProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'name';

    public static function getLabel(): ?string
    {
        return __('Raw Product');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Raw Products');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Inventory');
    }

    public static function form(Schema $schema): Schema
    {
        return RawProductForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return RawProductsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListRawProducts::route('/'),
            'create' => CreateRawProduct::route('/create'),
            'edit' => EditRawProduct::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])->where('type', ProductType::RAW->value);
    }
}

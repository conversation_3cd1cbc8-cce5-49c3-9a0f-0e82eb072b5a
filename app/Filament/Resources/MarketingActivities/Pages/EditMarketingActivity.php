<?php

namespace App\Filament\Resources\MarketingActivities\Pages;

use App\Filament\Resources\MarketingActivities\MarketingActivityResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditMarketingActivity extends EditRecord
{
    protected static string $resource = MarketingActivityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

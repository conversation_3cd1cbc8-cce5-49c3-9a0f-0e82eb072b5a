<?php

namespace App\Filament\Resources\MarketingActivities\Pages;

use App\Filament\Resources\MarketingActivities\MarketingActivityResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListMarketingActivities extends ListRecords
{
    protected static string $resource = MarketingActivityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

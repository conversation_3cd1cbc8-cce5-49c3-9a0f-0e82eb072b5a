<?php

namespace App\Filament\Resources\MarketingActivities\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\PriceField;
use App\Filament\Schemas\Components\Translateable;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class MarketingActivityForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                Translateable::make('content')
                    ->label(__('Content'))
                    ->required(),

                PriceField::make()
                    ->priceFieldName('budget')
                    ->currencyFieldName('budget_currency')
                    ->priceFieldLabel(__('Budget'))
                    ->currencyFieldLabel(__('Budget Currency'))
                    ->required(),

                FileUpload::make('image')
                    ->label(__('Image'))
                    ->image(),

                BelongsToSelect::make('marketing_platform_id')
                    ->label(__('Marketing Platform'))
                    ->relationship('marketingPlatform', 'name')
                    ->required(),

                Select::make('marketingActions')
                    ->label(__('Marketing Actions'))
                    ->relationship('marketingActions', 'name')
                    ->multiple()
                    ->preload(),
            ])
            ->columns(1);
    }
}

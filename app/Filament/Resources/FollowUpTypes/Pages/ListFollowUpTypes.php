<?php

namespace App\Filament\Resources\FollowUpTypes\Pages;

use App\Filament\Resources\FollowUpTypes\FollowUpTypeResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListFollowUpTypes extends ListRecords
{
    protected static string $resource = FollowUpTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

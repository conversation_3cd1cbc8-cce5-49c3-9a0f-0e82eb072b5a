<?php

namespace App\Filament\Resources\FollowUpTypes\Pages;

use App\Filament\Resources\FollowUpTypes\FollowUpTypeResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditFollowUpType extends EditRecord
{
    protected static string $resource = FollowUpTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

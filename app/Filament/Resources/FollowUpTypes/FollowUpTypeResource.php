<?php

namespace App\Filament\Resources\FollowUpTypes;

use App\Filament\Resources\FollowUps\FollowUpResource;
use App\Filament\Resources\FollowUpTypes\Pages\CreateFollowUpType;
use App\Filament\Resources\FollowUpTypes\Pages\EditFollowUpType;
use App\Filament\Resources\FollowUpTypes\Pages\ListFollowUpTypes;
use App\Filament\Resources\FollowUpTypes\Schemas\FollowUpTypeForm;
use App\Filament\Resources\FollowUpTypes\Tables\FollowUpTypesTable;
use App\Models\FollowUpType;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class FollowUpTypeResource extends Resource
{
    protected static ?string $model = FollowUpType::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function getLabel(): ?string
    {
        return __('Follow Up Type');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Follow Up Types');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('CRM');
    }

    public static function getNavigationParentItem(): ?string
    {
        return FollowUpResource::class;
    }

    public static function form(Schema $schema): Schema
    {
        return FollowUpTypeForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return FollowUpTypesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListFollowUpTypes::route('/'),
            'create' => CreateFollowUpType::route('/create'),
            'edit' => EditFollowUpType::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

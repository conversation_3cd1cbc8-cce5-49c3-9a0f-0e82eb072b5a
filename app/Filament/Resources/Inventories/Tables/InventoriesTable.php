<?php

namespace App\Filament\Resources\Inventories\Tables;

use App\Filament\Tables\Columns\EnumColumn;
use App\Filament\Widgets\StockMovementsTable;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Schemas\Components\Livewire;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class InventoriesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),

                //type
                EnumColumn::make('type')
                    ->label(__('Type')),

                // quantity
                TextColumn::make('quantity')
                    ->label(__('Quantity'))
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                //
            ])
            ->recordActions([
                Action::make('stock_history')
                    ->label(__('Stock History'))
                    ->icon(Heroicon::Clock)
                    ->schema(function ($record) {
                        return [
                            Livewire::make(StockMovementsTable::class, [
                                'product' => $record,
                            ])
                        ];
                    })
                    ->action(fn() => null),

                EditAction::make()
                    ->visible(false),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ])->visible(false),
            ]);
    }
}

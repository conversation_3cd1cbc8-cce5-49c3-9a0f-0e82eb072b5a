<?php

namespace App\Filament\Resources\Products\Schemas;

use App\Filament\Schemas\Components\PriceField;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('hs_code')
                    ->label(__('HS Code')),

                TextInput::make('commercial_name')
                    ->label(__('Commercial Name')),

                PriceField::make()
                    ->priceFieldName('buy_price')
                    ->currencyFieldName('buy_price_currency')
                    ->priceFieldLabel(__('Buy Price'))
                    ->currencyFieldLabel(__('Buy Price Currency'))
                    ->required(),

                PriceField::make()
                    ->priceFieldName('sell_price')
                    ->currencyFieldName('sell_price_currency')
                    ->priceFieldLabel(__('Sell Price'))
                    ->currencyFieldLabel(__('Sell Price Currency'))
                    ->required(),

                FileUpload::make('image')
                    ->label(__('Image'))
                    ->image(),
            ])
            ->columns(1);
    }
}

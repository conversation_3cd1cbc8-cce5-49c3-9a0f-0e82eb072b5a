<?php

namespace App\Filament\Resources\Stores\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class StoreForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                BelongsToSelect::make('store_location_id')
                    ->label(__('Store Location'))
                    ->relationship('storeLocation', 'name')
                    ->required(),

                Repeater::make('storeProducts')
                    ->label(__('Store Products'))
                    ->relationship('storeProducts')
                    ->schema([
                        BelongsToSelect::make('product_id')
                            ->label(__('Product'))
                            ->relationship('product', 'name')
                            ->required(),
                    ])
                    ->columns(1),
            ])
            ->columns(1);
    }
}

<?php

namespace App\Filament\Resources\Cities\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\Translateable;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class CityForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Translateable::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('code')
                    ->label(__('Code')),

                BelongsToSelect::make('country_id')
                    ->label(__('Country'))
                    ->relationship('country', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
            ])
            ->columns(1);
    }
}

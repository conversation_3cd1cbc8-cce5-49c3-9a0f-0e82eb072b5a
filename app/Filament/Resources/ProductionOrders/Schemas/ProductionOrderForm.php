<?php

namespace App\Filament\Resources\ProductionOrders\Schemas;

use App\Enums\OrderStatus;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ProductionOrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Select::make('product_id')
                    ->label(__('Product'))
                    ->relationship('product', 'name')
                    ->preload()
                    ->searchable()
                    ->required(),

                // quantity
                TextInput::make('quantity')
                    ->label(__('Quantity'))
                    ->numeric()
                    ->required()
                    ->minValue(1),

                // status
                Select::make('status')
                    ->label(__('Status'))
                    ->options(OrderStatus::getSelectOptions())
                    ->visible(function ($record) {
                        return !!$record;
                    })
                    ->required(),
            ]);
    }
}

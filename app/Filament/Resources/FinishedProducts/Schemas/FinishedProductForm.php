<?php

namespace App\Filament\Resources\FinishedProducts\Schemas;

use App\Enums\ProductType;
use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Resources\Products\Schemas\ProductForm;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

class FinishedProductForm extends ProductForm
{
    public static function getType()
    {
        return ProductType::FINISHED->value;
    }

    public static function getMoreFields(): array
    {
        return [
            Repeater::make('productComponents')
                ->label(__('Components'))
                ->relationship('productComponents')
                ->deletable(function ($state) {
                    return count($state) > 1;
                })
                ->table([
                    Repeater\TableColumn::make(__('Component')),

                    Repeater\TableColumn::make(__('Quantity')),
                ])
                ->schema([

                    Select::make('component_id')
                        ->relationship('component', 'name')
                        ->preload()
                        ->searchable()
                        ->required(),

                    TextInput::make('quantity')
                        ->label(__('Quantity'))
                        ->numeric()
                        ->required()
                        ->minValue(1),
                ])
        ];
    }
}

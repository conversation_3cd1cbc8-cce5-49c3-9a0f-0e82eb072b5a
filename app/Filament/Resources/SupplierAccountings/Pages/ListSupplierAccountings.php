<?php

namespace App\Filament\Resources\SupplierAccountings\Pages;

use App\Filament\Resources\SupplierAccountings\SupplierAccountingResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListSupplierAccountings extends ListRecords
{
    protected static string $resource = SupplierAccountingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->visible(false),
        ];
    }
}

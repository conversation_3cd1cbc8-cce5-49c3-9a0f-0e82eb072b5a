<?php

namespace App\Filament\Resources\PackageTypes\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class PackageTypeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('handle')
                    ->label(__('Handle')),
            ])
            ->columns(1);
    }
}

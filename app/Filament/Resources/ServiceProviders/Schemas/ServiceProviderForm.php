<?php

namespace App\Filament\Resources\ServiceProviders\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Resources\Services\RelationManagers\ServiceProvidersRelationManager;
use App\Filament\Schemas\Components\CountryCitySelect;
use App\Filament\Schemas\Components\PhoneField;
use App\Filament\Schemas\Components\PriceField;
use App\Models\ServiceItem;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Schemas\Components\Component;
use Filament\Schemas\Components\Text;
use Filament\Schemas\Schema;

class ServiceProviderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                ToggleButtons::make('service_id')
                    ->label(__('Service'))
                    ->aboveContent(__('Select the service provided by this service provider'))
                    ->options(function () {
                        return \App\Models\Service::query()->pluck('name', 'id')->toArray();
                    })
                    ->inline()
                    ->reactive()
                    ->required(),

                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('email')
                    ->label(__('Email'))
                    ->required(),

                PhoneField::make()
                    ->phoneFieldLabel(__('Phone'))
                    ->dialCodeFieldLabel(__('Dial Code')),

                TextInput::make('company_name')
                    ->label(__('Company Name'))
                    ->required(),

                CountryCitySelect::make()
                    ->countryFieldName('country_id')
                    ->cityFieldName('city_id')
                    ->countryFieldLabel(__('Country'))
                    ->cityFieldLabel(__('City'))
                    ->countryRelationshipName('country')
                    ->cityRelationshipName('city')
                    ->required(),

                Textarea::make('address')
                    ->label(__('Address')),

                BelongsToSelect::make('payment_term_id')
                    ->label(__('Payment Term'))
                    ->relationship('paymentTerm', 'name')
                    ->preload()
                    ->searchable(),

                // bank details
                TextInput::make('bank_name')
                    ->label(__('Bank Name')),

                TextInput::make('bank_account_number')
                    ->label(__('Bank Account Number')),

                TextInput::make('iban_number')
                    ->label(__('IBAN Number')),

                TextInput::make('swift_code')
                    ->label(__('SWIFT Code')),

                Repeater::make('serviceProviderItems')
                    ->label(__('Service Items'))
                    ->relationship('serviceProviderItems')
                    ->columns(2)
                    ->required()
                    ->schema([
                        BelongsToSelect::make('service_item_id')
                            ->disabled(function ($get, Component $component) {
                                $basePath = $component->getBasePath();

                                return !$get($basePath . '.service_id', true);
                            })
                            ->afterLabel(function ($get, Component $component) {
                                $basePath = $component->getBasePath();

                                if (!$get($basePath . '.service_id', true)) {
                                    return Text::make(__('Please select a service first.'))
                                        ->color('danger');
                                }

                                return null;
                            })
                            ->relationship('serviceItem', 'name', function ($query, $get, Component $component) {
                                $basePath = $component->getBasePath();

                                return $query->where('service_id', $get($basePath . '.service_id', true));
                            })
                            ->label(__('Service Item'))
                            ->searchable()
                            ->preload()
                            ->required(),

                        PriceField::make()
                            ->disabled(function ($get, Component $component) {
                                $basePath = $component->getBasePath();

                                return !$get($basePath . '.service_id', true);
                            })
                            ->priceFieldName('price')
                            ->currencyFieldName('price_currency')
                            ->priceFieldLabel(__('Price'))
                            ->currencyFieldLabel(__('Price Currency'))
                            ->required(),
                    ]),
            ])
            ->columns(1);
    }
}

<?php

namespace App\Filament\Resources\FollowUps\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class FollowUpsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('notes')
                    ->label(__('Notes'))
                    ->searchable()
                    ->limit(50),

                TextColumn::make('follow_up_date')
                    ->label(__('Follow Up Date'))
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('followable_type')
                    ->label(__('Followable Type')),

                TextColumn::make('followable.name')
                    ->label(__('Followable')),

                TextColumn::make('followUpType.name')
                    ->label(__('Follow Up Type')),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                TrashedFilter::make(),
                SelectFilter::make('follow_up_type_id')
                    ->label(__('Follow Up Type'))
                    ->relationship('followUpType', 'name'),
                Filter::make('follow_up_date')
                    ->schema([
                        DatePicker::make('follow_up_from'),
                        DatePicker::make('follow_up_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['follow_up_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('follow_up_date', '>=', $date),
                            )
                            ->when(
                                $data['follow_up_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('follow_up_date', '<=', $date),
                            );
                    }),
                Filter::make('created_at')
                    ->schema([
                        DatePicker::make('created_from'),
                        DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}

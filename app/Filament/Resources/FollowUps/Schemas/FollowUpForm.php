<?php

namespace App\Filament\Resources\FollowUps\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Models\FollowUp;
use App\Models\Lead;
use App\Models\Client;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;
use Filament\Support\Enums\TextSize;

class FollowUpForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Textarea::make('notes')
                    ->label(__('Notes'))
                    ->required(),

                DateTimePicker::make('follow_up_date')
                    ->label(__('Follow Up Date'))
                    ->required(),

                MorphToSelect::make('followable')
                    ->label(__('Followable'))
                    ->types([
                        MorphToSelect\Type::make(Lead::class)
                            ->titleAttribute('name'),
                        MorphToSelect\Type::make(Client::class)
                            ->titleAttribute('name'),
                    ])
                    ->required(),

                BelongsToSelect::make('follow_up_type_id')
                    ->label(__('Follow Up Type'))
                    ->relationship('followUpType', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
            ])
            ->columns(1);
    }
}

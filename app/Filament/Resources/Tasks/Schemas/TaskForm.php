<?php

namespace App\Filament\Resources\Tasks\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Models\User;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>baum\Commentions\Filament\Infolists\Components\CommentsEntry;

class TaskForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Grid::make(2)
                    ->schema([
                        Section::make()
                            ->schema([
                                TextInput::make('title')
                                    ->label(__('Title'))
                                    ->maxLength(255)
                                    ->required(),

                                Textarea::make('description')
                                    ->label(__('Description'))
                                    ->required(),

                                DateTimePicker::make('due_date')
                                    ->label(__('Due Date'))
                                    ->required(),

                                BelongsToSelect::make('assignee_id')
                                    ->label(__('Assignee'))
                                    ->relationship('assignee', 'name')
                                    ->default(fn() => auth()->id())
                                    ->preload()
                                    ->searchable()
                                    ->required(),
                            ]),

                        Section::make()
                            ->visible(function ($record) {
                                return !! $record;
                            })
                            ->schema([
                                CommentsEntry::make('comments')
                                    ->mentionables(fn (Model $record) => User::all()),
                            ])
                    ])
            ])
            ->disabled(function () {
                return ! is_super() && ! is_manager();
            })
            ->columns(1);
    }
}

<?php

namespace App\Filament\Resources\Tasks\Tables;

use App\Enums\TaskStatus;
use App\Filament\Tables\Columns\EnumColumn;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TasksTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('title')
                    ->label(__('Title'))
                    ->searchable(),

                TextColumn::make('due_date')
                    ->label(__('Due Date'))
                    ->dateTimeFormatted()
                    ->sortable(),

                TextColumn::make('creator.name')
                    ->label(__('Creator')),

                TextColumn::make('assignee.name')
                    ->label(__('Assignee')),

                EnumColumn::make('status')
                    ->label(__('Status')),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTimeFormatted()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('assignee_id')
                    ->label(__('Assignee'))
                    ->preload()
                    ->searchable()
                    ->multiple()
                    ->relationship('assignee', 'name')
                    ->visible(function () {
                        return is_super() || is_manager();
                    }),

                Filter::make('due_date')
                    ->label(__('Due Date'))
                    ->schema([
                        DatePicker::make('due_from')
                            ->label(__('Due From')),
                        DatePicker::make('due_until')
                            ->label(__('Due Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['due_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '>=', $date),
                            )
                            ->when(
                                $data['due_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '<=', $date),
                            );
                    }),

                Filter::make('created_at')
                    ->label(__('Created At'))
                    ->schema([
                        DatePicker::make('created_from')
                            ->label(__('Created From')),
                        DatePicker::make('created_until')
                            ->label(__('Created Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),

                TrashedFilter::make(),
            ])
            ->recordActions([
                EditAction::make(),

                Action::make('change_status')
                    ->label(__('Change Status'))
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->schema([
                        Select::make('status')
                            ->options(TaskStatus::getSelectOptions())
                            ->required(),
                    ])
                    ->action(function (array $data, $record) {
                        $record->update($data);
                    }),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}

<?php

namespace App\Filament\Resources\Tasks\Pages;

use App\Filament\Resources\Tasks\TaskResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Tabs\Tab;

class ListTasks extends ListRecords
{
    protected static string $resource = TaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->visible(function () {
                    return is_super() || is_manager();
                }),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(__('All')),

            'pending' => Tab::make(__('Pending'))
                ->modifyQueryUsing(fn ($query) => $query->where('status', 'pending')),

            'in_progress' => Tab::make(__('In Progress'))
                ->modifyQueryUsing(fn ($query) => $query->where('status', 'in_progress')),

            'completed' => Tab::make(__('Completed'))
                ->modifyQueryUsing(fn ($query) => $query->where('status', 'completed')),

            'cancelled' => Tab::make(__('Cancelled'))
                ->modifyQueryUsing(fn ($query) => $query->where('status', 'cancelled')),

            'overdue' => Tab::make(__('Overdue'))
                ->modifyQueryUsing(fn ($query) => $query->due()),
        ];
    }
}

<?php

namespace App\Filament\Resources\ServiceProviderItems\Pages;

use App\Filament\Resources\ServiceProviderItems\ServiceProviderItemResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditServiceProviderItem extends EditRecord
{
    protected static string $resource = ServiceProviderItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

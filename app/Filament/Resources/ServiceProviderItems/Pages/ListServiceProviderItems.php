<?php

namespace App\Filament\Resources\ServiceProviderItems\Pages;

use App\Filament\Resources\ServiceProviderItems\ServiceProviderItemResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListServiceProviderItems extends ListRecords
{
    protected static string $resource = ServiceProviderItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

<?php

namespace App\Filament\Resources\ServiceProviderItems;

use App\Filament\Resources\ServiceProviderItems\Pages\CreateServiceProviderItem;
use App\Filament\Resources\ServiceProviderItems\Pages\EditServiceProviderItem;
use App\Filament\Resources\ServiceProviderItems\Pages\ListServiceProviderItems;
use App\Filament\Resources\ServiceProviderItems\Schemas\ServiceProviderItemForm;
use App\Filament\Resources\ServiceProviderItems\Tables\ServiceProviderItemsTable;
use App\Models\ServiceProviderItem;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ServiceProviderItemResource extends Resource
{
    protected static ?string $model = ServiceProviderItem::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Schema $schema): Schema
    {
        return ServiceProviderItemForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ServiceProviderItemsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListServiceProviderItems::route('/'),
            'create' => CreateServiceProviderItem::route('/create'),
            'edit' => EditServiceProviderItem::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

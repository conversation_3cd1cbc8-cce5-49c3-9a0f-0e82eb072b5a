<?php

namespace App\Filament\Resources\ModelPorts\Pages;

use App\Filament\Resources\ModelPorts\ModelPortResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditModelPort extends EditRecord
{
    protected static string $resource = ModelPortResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

<?php

namespace App\Filament\Resources\ModelPorts;

use App\Filament\Resources\ModelPorts\Pages\CreateModelPort;
use App\Filament\Resources\ModelPorts\Pages\EditModelPort;
use App\Filament\Resources\ModelPorts\Pages\ListModelPorts;
use App\Filament\Resources\ModelPorts\Schemas\ModelPortForm;
use App\Filament\Resources\ModelPorts\Tables\ModelPortsTable;
use App\Models\ModelPort;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class ModelPortResource extends Resource
{
    protected static ?string $model = ModelPort::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static bool $shouldRegisterNavigation = false;

    public static function getLabel(): ?string
    {
        return __('Model Port');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Model Ports');
    }

    public static function form(Schema $schema): Schema
    {
        return ModelPortForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ModelPortsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListModelPorts::route('/'),
            'create' => CreateModelPort::route('/create'),
            'edit' => EditModelPort::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

<?php

namespace App\Filament\Resources\ModelPorts\Schemas;

use App\Models\Airport;
use App\Models\Client;
use App\Models\Seaport;
use Filament\Forms\Components\MorphToSelect;
use Filament\Schemas\Schema;

class ModelPortForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                MorphToSelect::make('model')
                    ->label(__('Model'))
                    ->types([
                        MorphToSelect\Type::make(Client::class)
                            ->titleAttribute('name'),
                    ])
                    ->required(),

                MorphToSelect::make('port')
                    ->label(__('Port'))
                    ->types([
                        MorphToSelect\Type::make(Airport::class)
                            ->titleAttribute('name'),
                        MorphToSelect\Type::make(Seaport::class)
                            ->titleAttribute('name'),
                    ])
                    ->required(),
            ])
            ->columns(1);
    }
}

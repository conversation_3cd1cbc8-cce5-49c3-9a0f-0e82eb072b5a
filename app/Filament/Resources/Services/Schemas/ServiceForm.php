<?php

namespace App\Filament\Resources\Services\Schemas;

use App\Filament\Schemas\Components\Translateable;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ServiceForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make()
                    ->schema([
                        Translateable::make('name')
                            ->label(__('Name'))
                            ->required(),
                    ]),

                Repeater::make('serviceItems')
                    ->label(__('Service Items'))
                    ->relationship('serviceItems')
                    ->simple(
                        TextInput::make('name')
                            ->label(__('Name'))
                            ->required(),
                    ),
            ])
            ->columns(1);
    }
}

<?php

namespace App\Filament\Resources\MarketingPlatforms\RelationManagers;

use App\Filament\Resources\MarketingActivities\Schemas\MarketingActivityForm;
use App\Filament\Resources\MarketingActivities\Tables\MarketingActivitiesTable;
use Filament\Actions\AssociateAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\DissociateAction;
use Filament\Actions\DissociateBulkAction;
use Filament\Actions\EditAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class MarketingActivitiesRelationManager extends RelationManager
{
    protected static string $relationship = 'marketingActivities';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Marketing Activities');
    }

    public function form(Schema $schema): Schema
    {
        return MarketingActivityForm::configure($schema);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns(MarketingActivitiesTable::configure($table)->getColumns())
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
                AssociateAction::make(),
            ])
            ->recordActions([
                EditAction::make(),
                DissociateAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DissociateBulkAction::make(),
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}

<?php

namespace App\Filament\Resources\MarketingPlatforms\Pages;

use App\Filament\Resources\MarketingPlatforms\MarketingPlatformResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditMarketingPlatform extends EditRecord
{
    protected static string $resource = MarketingPlatformResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

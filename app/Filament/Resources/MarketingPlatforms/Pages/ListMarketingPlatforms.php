<?php

namespace App\Filament\Resources\MarketingPlatforms\Pages;

use App\Filament\Resources\MarketingPlatforms\MarketingPlatformResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListMarketingPlatforms extends ListRecords
{
    protected static string $resource = MarketingPlatformResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

<?php

namespace App\Filament\Resources\Payments\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\PriceField;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class PaymentForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                PriceField::make()
                    ->priceFieldName('amount')
                    ->currencyFieldName('amount_currency')
                    ->priceFieldLabel(__('Amount'))
                    ->currencyFieldLabel(__('Amount Currency'))
                    ->required(),

                BelongsToSelect::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->relationship('paymentMethod', 'name')
                    ->preload(),

                DatePicker::make('due_date')
                    ->label(__('Due Date'))
                    ->default(now())
                    ->required(),

                DatePicker::make('paid_at')
                    ->label(__('Paid At')),

                FileUpload::make('attachments')
                    ->label(__('Attachments'))
                    ->multiple(),

                Textarea::make('notes')
                    ->label(__('Notes')),
            ]);
    }
}

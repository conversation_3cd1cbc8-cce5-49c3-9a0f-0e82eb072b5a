<?php

namespace App\Filament\Resources\Payments\Tables;

use App\Filament\Tables\Columns\EnumColumn;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class PaymentsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('amount')
                    ->label(__('Amount'))
                    ->money(fn ($record) => $record->amount_currency ?? 'EGP')
                    ->sortable(),

                TextColumn::make('paymentMethod.name')
                    ->label(__('Payment Method')),

                EnumColumn::make('due_status')
                    ->label(__('Due Status')),

                TextColumn::make('due_date')
                    ->label(__('Due Date'))
                    ->date()
                    ->sortable(),

                TextColumn::make('paid_at')
                    ->label(__('Paid At'))
                    ->date()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTimeFormatted()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->relationship('paymentMethod', 'name')
                    ->preload(),

                TrashedFilter::make(),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}

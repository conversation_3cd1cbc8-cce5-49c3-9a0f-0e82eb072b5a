<?php

namespace App\Filament\Resources\StockMovements\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\Radio;
use Filament\Schemas\Schema;

class StockMovementForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Radio::make('type')
                    ->label(__('Type'))
                    ->options([
                        'in' => __('In'),
                        'out' => __('Out'),
                    ])
                    ->required(),

                BelongsToSelect::make('store_id')
                    ->label(__('Store'))
                    ->relationship('store', 'name')
                    ->required(),

                BelongsToSelect::make('product_id')
                    ->label(__('Product'))
                    ->relationship('product', 'name')
                    ->required(),
            ])
            ->columns(1);
    }
}

<?php

namespace App\Filament\Resources\Seaports\Pages;

use App\Filament\Resources\Seaports\SeaportResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditSeaport extends EditRecord
{
    protected static string $resource = SeaportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

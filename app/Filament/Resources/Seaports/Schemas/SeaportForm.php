<?php

namespace App\Filament\Resources\Seaports\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class SeaportForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('un_locode')
                    ->label(__('UN Locode')),

                BelongsToSelect::make('country_id')
                    ->label(__('Country'))
                    ->relationship('country', 'name')
                    ->required(),

                BelongsToSelect::make('city_id')
                    ->label(__('City'))
                    ->relationship('city', 'name')
                    ->required(),
            ])
            ->columns(1);
    }
}

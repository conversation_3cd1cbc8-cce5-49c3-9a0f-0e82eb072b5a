<?php

namespace App\Filament\Resources\SellOrders;

use App\Filament\Resources\SellOrders\Pages\CreateSellOrder;
use App\Filament\Resources\SellOrders\Pages\EditSellOrder;
use App\Filament\Resources\SellOrders\Pages\ListSellOrders;
use App\Filament\Resources\SellOrders\Schemas\SellOrderForm;
use App\Filament\Resources\SellOrders\Tables\SellOrdersTable;
use App\Models\SellOrder;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class SellOrderResource extends Resource
{
    protected static ?string $model = SellOrder::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function getLabel(): ?string
    {
        return __('Sell Order');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Sell Orders');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Sales');
    }

    public static function form(Schema $schema): Schema
    {
        return SellOrderForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return SellOrdersTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSellOrders::route('/'),
            'create' => CreateSellOrder::route('/create'),
            'edit' => EditSellOrder::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

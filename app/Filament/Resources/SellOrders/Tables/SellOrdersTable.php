<?php

namespace App\Filament\Resources\SellOrders\Tables;

use App\Enums\OrderStatus;
use App\Filament\Tables\Columns\EnumColumn;
use App\Models\SellOrder;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SellOrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('incremental_id')
                    ->label('#')
                    ->sortable(),

                TextColumn::make('client.name')
                    ->label(__('Client')),

                SelectColumn::make('status')
                    ->label(__('Status'))
                    ->options(OrderStatus::getSelectOptions()),

                TextColumn::make('sellOrderProducts.product.name')
                    ->label(__('Products'))
                    ->listWithLineBreaks()
                    ->bulleted(),

                TextColumn::make('price')
                    ->label(__('Products Price'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (SellOrder $record) {
                        return $record->getPriceLabels('price');
                    }),

                TextColumn::make('service_cost')
                    ->label(__('Service Cost'))
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(function (SellOrder $record) {
                        return $record->getPriceLabels('service_cost');
                    }),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('incremental_id', 'desc')
            ->filters([
                SelectFilter::make('client_id')
                    ->label(__('Client'))
                    ->relationship('client', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(OrderStatus::getSelectOptions()),

                SelectFilter::make('origin')
                    ->label(__('Origin'))
                    ->options([
                        'Local' => __('Local'),
                        'Foreign' => __('Foreign'),
                    ]),

                SelectFilter::make('model_of_shipping')
                    ->label(__('Model of Shipping'))
                    ->options([
                        'Air' => __('Air'),
                        'Sea' => __('Sea'),
                        'Courier' => __('Courier'),
                    ]),

                Filter::make('created_at')
                    ->label(__('Created At'))
                    ->schema([
                        DatePicker::make('created_from')
                            ->label(__('Created From')),
                        DatePicker::make('created_until')
                            ->label(__('Created Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),

                TrashedFilter::make(),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}

<?php

namespace App\Filament\Resources\SellOrders\Pages;

use App\Filament\Resources\SellOrders\SellOrderResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditSellOrder extends EditRecord
{
    protected static string $resource = SellOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}

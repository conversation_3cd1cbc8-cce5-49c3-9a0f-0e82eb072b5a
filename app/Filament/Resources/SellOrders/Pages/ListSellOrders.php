<?php

namespace App\Filament\Resources\SellOrders\Pages;

use App\Filament\Resources\SellOrders\SellOrderResource;
use App\Models\SellOrder;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListSellOrders extends ListRecords
{
    protected static string $resource = SellOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}

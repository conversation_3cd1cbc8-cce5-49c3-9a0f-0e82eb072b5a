<?php

namespace App\Filament\Resources\Leads\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class LeadForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('email')
                    ->label(__('Email'))
                    ->email()
                    ->required(),

                TextInput::make('phone')
                    ->label(__('Phone'))
                    ->required(),

                Select::make('dial_code')
                    ->label(__('Dial Code'))
                    ->options([
                        '+20' => '+20 (Egypt)',
                        '+1' => '+1 (USA)',
                        '+44' => '+44 (UK)',
                        '+971' => '+971 (UAE)',
                        '+966' => '+966 (Saudi Arabia)',
                    ]),

                TextInput::make('company_name')
                    ->label(__('Company Name'))
                    ->required(),

                BelongsToSelect::make('country_id')
                    ->label(__('Country'))
                    ->relationship('country', 'name')
                    ->required(),

                Textarea::make('address')
                    ->label(__('Address')),

                BelongsToSelect::make('lead_source_id')
                    ->label(__('Lead Source'))
                    ->relationship('leadSource', 'name')
                    ->required(),

                BelongsToSelect::make('assignee_id')
                    ->label(__('Assignee'))
                    ->relationship('assignee', 'name')
                    ->required(),

                Select::make('leadStatuses')
                    ->label(__('Lead Statuses'))
                    ->relationship('leadStatuses', 'name')
                    ->multiple()
                    ->preload(),
            ])
            ->columns(1);
    }
}

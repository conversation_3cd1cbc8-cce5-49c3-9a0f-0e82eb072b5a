<?php

namespace App\Filament\Resources\PaymentMethods\Schemas;

use App\Filament\Schemas\Components\Translateable;
use Filament\Schemas\Schema;

class PaymentMethodForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                Translateable::make('name')
                    ->label(__('Name'))
                    ->required(),
            ]);
    }
}

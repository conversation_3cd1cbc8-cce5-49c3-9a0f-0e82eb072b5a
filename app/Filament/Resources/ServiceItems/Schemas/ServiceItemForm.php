<?php

namespace App\Filament\Resources\ServiceItems\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ServiceItemForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([

                BelongsToSelect::make('service_id')
                    ->label(__('Service'))
                    ->relationship('service', 'name')
                    ->preload()
                    ->searchable()
                    ->required(),

                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

            ]);
    }
}

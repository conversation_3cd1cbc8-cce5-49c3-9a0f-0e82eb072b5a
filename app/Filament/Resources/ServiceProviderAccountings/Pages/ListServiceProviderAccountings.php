<?php

namespace App\Filament\Resources\ServiceProviderAccountings\Pages;

use App\Filament\Resources\ServiceProviderAccountings\ServiceProviderAccountingResource;
use App\Models\ServiceProvider;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListServiceProviderAccountings extends ListRecords
{
    protected static string $resource = ServiceProviderAccountingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->visible(false),
        ];
    }
}

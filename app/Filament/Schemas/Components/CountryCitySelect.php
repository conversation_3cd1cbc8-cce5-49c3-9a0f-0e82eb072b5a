<?php

namespace App\Filament\Schemas\Components;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Schemas\Components\Component;
use Filament\Schemas\Components\Text;

class CountryCitySelect extends Component
{
    protected string $view = 'filament.schemas.components.country-city-select';

    protected \Closure | string | null $countryFieldName = 'country_id';

    protected \Closure | string | null $cityFieldName = 'city_id';

    protected \Closure | string | null $countryRelationshipName = 'country';

    protected \Closure | string | null $cityRelationshipName = 'city';

    protected \Closure | string | null $countryFieldLabel = 'Country';

    protected \Closure | string | null $cityFieldLabel = 'City';

    protected \Closure | bool $required = false;

    protected \Closure | bool $showCityField = true;

    protected \Closure | bool $hideCityField = false;

    protected \Closure | null $afterCountryFieldUpdated = null;

    protected \Closure | null $afterCityFieldUpdated = null;

    public static function make(): static
    {
        return app(static::class)
            ->schema(function (Component|CountryCitySelect $component) {
                $layoutComponent = $component;

                return [
                    BelongsToSelect::make($layoutComponent->getCountryFieldName())
                        ->label($layoutComponent->getCountryFieldLabel())
                        ->relationship($layoutComponent->getCountryRelationshipName(), 'name')
                        ->preload()
                        ->searchable()
                        ->reactive()
                        ->afterStateUpdated(function ($set, Component $component) use ($layoutComponent) {
                            $set($layoutComponent->getCityFieldName(), null);

                            if ($layoutComponent->getAfterCountryFieldUpdated()) {
                                $component->evaluate($layoutComponent->getAfterCountryFieldUpdated());
                            }
                        })
                        ->required($layoutComponent->isRequired()),

                    BelongsToSelect::make($layoutComponent->getCityFieldName())
                        ->label($layoutComponent->getCityFieldLabel())
                        ->relationship($layoutComponent->getCityRelationshipName(), 'name', function ($query, $get) use ($layoutComponent) {
                            return $query->where('country_id', $get($layoutComponent->getCountryFieldName()));
                        })
                        ->disabled(function ($get) use ($layoutComponent) {
                            return !$get($layoutComponent->getCountryFieldName());
                        })
                        ->aboveContent(function ($get) use ($layoutComponent) {
                            if (!$get($layoutComponent->getCountryFieldName())) {
                                return Text::make(__('Please select a country first.'))
                                    ->color('danger');
                            }

                            return null;
                        })
                        ->preload()
                        ->searchable()
                        ->required($layoutComponent->isRequired())
                        ->reactive()
                        ->afterStateUpdated(function ($set, Component $component) use ($layoutComponent) {
                            if ($layoutComponent->getAfterCityFieldUpdated()) {
                                $component->evaluate($layoutComponent->getAfterCityFieldUpdated());
                            }
                        })
                        ->visible(function () use ($layoutComponent) {
                            return $layoutComponent->isShowCityField() && !$layoutComponent->isHideCityField();
                        }),
                ];
            });
    }

    public function countryFieldName(string | \Closure $countryFieldName): static
    {
        $this->countryFieldName = $countryFieldName;

        return $this;
    }

    public function cityFieldName(string | \Closure $cityFieldName): static
    {
        $this->cityFieldName = $cityFieldName;

        return $this;
    }

    public function countryFieldLabel(string | \Closure $countryFieldLabel): static
    {
        $this->countryFieldLabel = $countryFieldLabel;

        return $this;
    }

    public function cityFieldLabel(string | \Closure $cityFieldLabel): static
    {
        $this->cityFieldLabel = $cityFieldLabel;

        return $this;
    }

    public function countryRelationshipName(string | \Closure $countryRelationshipName): static
    {
        $this->countryRelationshipName = $countryRelationshipName;

        return $this;
    }

    public function cityRelationshipName(string | \Closure $cityRelationshipName): static
    {
        $this->cityRelationshipName = $cityRelationshipName;

        return $this;
    }

    public function afterCountryFieldUpdated(\Closure $afterCountryFieldUpdated): static
    {
        $this->afterCountryFieldUpdated = $afterCountryFieldUpdated;

        return $this;
    }

    public function afterCityFieldUpdated(\Closure $afterCityFieldUpdated): static
    {
        $this->afterCityFieldUpdated = $afterCityFieldUpdated;

        return $this;
    }

    public function required(bool | \Closure $required = true): static
    {
        $this->required = $required;

        return $this;
    }

    public function showCityField(bool | \Closure $showCityField = true): static
    {
        $this->showCityField = $showCityField;

        return $this;
    }

    public function hideCityField(bool | \Closure $hideCityField = true): static
    {
        $this->hideCityField = $hideCityField;

        return $this;
    }

    public function getCountryFieldName(): string
    {
        return $this->evaluate($this->countryFieldName);
    }

    public function getCityFieldName(): string
    {
        return $this->evaluate($this->cityFieldName);
    }

    public function getCountryFieldLabel(): string
    {
        return $this->evaluate($this->countryFieldLabel);
    }

    public function getCityFieldLabel(): string
    {
        return $this->evaluate($this->cityFieldLabel);
    }

    public function getCountryRelationshipName(): string
    {
        return $this->evaluate($this->countryRelationshipName);
    }

    public function getCityRelationshipName(): string
    {
        return $this->evaluate($this->cityRelationshipName);
    }

    public function getAfterCountryFieldUpdated(): \Closure | null
    {
        return $this->afterCountryFieldUpdated;
    }

    public function getAfterCityFieldUpdated(): \Closure | null
    {
        return $this->afterCityFieldUpdated;
    }

    public function isRequired(): bool
    {
        return $this->evaluate($this->required);
    }

    public function isShowCityField(): bool
    {
        return $this->evaluate($this->showCityField);
    }

    public function isHideCityField(): bool
    {
        return $this->evaluate($this->hideCityField);
    }
}

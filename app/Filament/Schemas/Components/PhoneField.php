<?php

namespace App\Filament\Schemas\Components;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Component;
use Filament\Schemas\Components\FusedGroup;

class PhoneField extends Component
{
    protected string $view = 'filament.schemas.components.phone-field';

    protected string | \Closure $phoneFieldName = 'phone';

    protected string | \Closure $dialCodeFieldName = 'dial_code';

    protected string | \Closure $phoneFieldLabel = 'Phone';

    protected string | \Closure $dialCodeFieldLabel = 'Dial Code';

    protected bool | \Closure $required = false;

    public static function make(): static
    {
        return app(static::class)
            ->schema(function (Component|PhoneField $component) {
                return [
                    FusedGroup::make([
                        Select::make($component->getDialCodeFieldName())
                            ->label($component->getDialCodeFieldLabel())
                            ->searchable()
                            ->options([
                                '+20' => '+20 (Egypt)',
                                '+1' => '+1 (USA)',
                                '+44' => '+44 (UK)',
                                '+971' => '+971 (UAE)',
                                '+966' => '+966 (Saudi Arabia)',
                            ])
                            ->default('+20')
                            ->required($component->isRequired()),

                        TextInput::make($component->getPhoneFieldName())
                            ->dehydrateStateUsing(function ($state) {
                                if (str($state)->startsWith('0')) {
                                    return str($state)->replaceFirst('0', '')->value();
                                }

                                if (str($state)->startsWith('+')) {
                                    return str($state)->replaceFirst('+', '')->value();
                                }

                                return $state;
                            })
                            ->label($component->getPhoneFieldLabel())
                            ->required($component->isRequired())
                            ->numeric()
                            ->minValue(0)
                            ->columnSpan(2),
                    ])
                        ->label($component->getPhoneFieldLabel())
                        ->columns(3)
                ];
            });
    }

    public function phoneFieldName(string | \Closure $phoneFieldName): static
    {
        $this->phoneFieldName = $phoneFieldName;

        return $this;
    }

    public function dialCodeFieldName(string | \Closure $dialCodeFieldName): static
    {
        $this->dialCodeFieldName = $dialCodeFieldName;

        return $this;
    }

    public function phoneFieldLabel(string | \Closure $phoneFieldLabel): static
    {
        $this->phoneFieldLabel = $phoneFieldLabel;

        return $this;
    }

    public function dialCodeFieldLabel(string | \Closure $dialCodeFieldLabel): static
    {
        $this->dialCodeFieldLabel = $dialCodeFieldLabel;

        return $this;
    }

    public function required(bool | \Closure $required = true): static
    {
        $this->required = $required;

        return $this;
    }

    public function getPhoneFieldName(): string
    {
        return $this->evaluate($this->phoneFieldName);
    }

    public function getDialCodeFieldName(): string
    {
        return $this->evaluate($this->dialCodeFieldName);
    }

    public function getPhoneFieldLabel(): string
    {
        return $this->evaluate($this->phoneFieldLabel);
    }

    public function getDialCodeFieldLabel(): string
    {
        return $this->evaluate($this->dialCodeFieldLabel);
    }

    public function isRequired(): bool
    {
        return $this->evaluate($this->required);
    }
}

<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum OrderIncoterm: string
{
    use Common;

    case EXW = 'exw';
    case DDP = 'ddp';
    case CIF = 'cif';
    case FOB = 'fob';

    public function label(): string
    {
        return match ($this) {
            self::EXW => __('From pickup address to delivery address (EX WORKS)'),
            self::DDP => __('From pickup address to delivery address (DDP)'),
            self::CIF => __('From pickup address to port of discharge (CIF)'),
            self::FOB => __('From pickup address to port of loading (FOB)'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::EXW => 'gray',
            self::DDP => 'warning',
            self::CIF => 'success',
            self::FOB => 'danger',
        };
    }

    public function code(): string
    {
        return match ($this) {
            self::EXW => 'EXW',
            self::DDP => 'DDP',
            self::CIF => 'CIF',
            self::FOB => 'FOB',
        };
    }
}

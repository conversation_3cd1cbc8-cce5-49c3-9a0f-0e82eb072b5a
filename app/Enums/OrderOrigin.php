<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum OrderOrigin: string
{
    use Common;

    case Local = 'local';
    case Foreign = 'foreign';

    public function label(): string
    {
        return match ($this) {
            self::Local => __('Local'),
            self::Foreign => __('Foreign'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::Local => 'gray',
            self::Foreign => 'warning',
        };
    }
}

<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum ProductType: string
{
    use Common;

    case RAW = 'raw';
    case FINISHED = 'finished';

    public function label(): string
    {
        return match ($this) {
            self::RAW => __('Raw'),
            self::FINISHED => __('Finished'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::RAW => 'gray',
            self::FINISHED => 'warning',
        };
    }
}

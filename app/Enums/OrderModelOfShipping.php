<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum OrderModelOfShipping: string
{
    use Common;

    case Sea = 'sea';
    case Air = 'air';

    case Courier = 'courier';

    public function label(): string
    {
        return match ($this) {
            self::Air => __('Air'),
            self::Sea => __('Sea'),
            self::Courier => __('Courier'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::Air => 'gray',
            self::Sea => 'warning',
            self::Courier => 'success',
        };
    }

    public function incotermSelectOptions(): array
    {
        return match ($this) {
            self::Sea, self::Air => OrderIncoterm::getSelectOptions(),
            self::Courier => OrderIncoterm::getSelectOptions(only: [
                OrderIncoterm::EXW->value,
                OrderIncoterm::DDP->value,
            ]),
        };
    }

    public function firstIncotermValue()
    {
        return match ($this) {
            self::Sea, self::Air => OrderIncoterm::EXW->value,
            self::Courier => OrderIncoterm::EXW->value,
        };
    }
}

<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum StockMovementType: string
{
    use Common;

    case In = 'in';
    case Out = 'out';

    public function label(): string
    {
        return match ($this) {
            self::In => __('In'),
            self::Out => __('Out'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::In => 'success',
            self::Out => 'danger',
        };
    }
}

<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum TaskStatus: string
{
    use Common;

    case Pending = 'pending';
    case InProgress = 'in_progress';
    case Completed = 'completed';

    case Cancelled = 'cancelled';

    public function color(): string
    {
        return match ($this) {
            self::Pending => 'gray',
            self::InProgress => 'warning',
            self::Completed => 'success',
            self::Cancelled => 'danger',
        };
    }

    // label translation
    public function label(): string
    {
        return match ($this) {
            self::Pending => __('Not Started'),
            self::InProgress => __('In Progress'),
            self::Completed => __('Completed'),
            self::Cancelled => __('Cancelled'),
        };
    }
}

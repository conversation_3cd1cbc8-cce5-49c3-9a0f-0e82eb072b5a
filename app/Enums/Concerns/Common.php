<?php

namespace App\Enums\Concerns;

trait Common
{
    public static function getSelectOptions($only = null, $except = null): array
    {
        return collect(self::cases())
            ->when($only, function ($collection) use ($only) {
                return $collection->filter(function ($status) use ($only) {
                    return in_array($status->value, $only);
                });
            })
            ->when($except, function ($collection) use ($except) {
                return $collection->filter(function ($status) use ($except) {
                    return !in_array($status->value, $except);
                });
            })
            ->mapWithKeys(function ($status) {
                return [$status->value => $status->label()];
            })
            ->toArray();
    }

    public static function findByHandle(string $handle): ?self
    {
        return collect(self::cases())->first(function ($status) use ($handle) {
            return $status->value == $handle;
        });
    }

    public static function only($values)
    {
        return collect(self::cases())->filter(function ($status) use ($values) {
            return in_array($status->value, $values);
        })->values()->toArray();
    }

    public static function except($values)
    {
        return collect(self::cases())->filter(function ($status) use ($values) {
            return !in_array($status->value, $values);
        })->values()->toArray();
    }
}

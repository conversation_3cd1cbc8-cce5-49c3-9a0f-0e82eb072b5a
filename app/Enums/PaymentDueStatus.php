<?php

namespace App\Enums;

use App\Enums\Concerns\Common;

enum PaymentDueStatus: string
{
    use Common;

    case FUTURE = 'future';

    case DUE = 'due';

    case PAID = 'paid';

    public function label(): string
    {
        return match ($this) {
            self::FUTURE => __('Future'),
            self::DUE => __('Due'),
            self::PAID => __('Paid'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::FUTURE => 'warning',
            self::DUE => 'danger',
            self::PAID => 'success',
        };
    }
}

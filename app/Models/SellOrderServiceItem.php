<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class SellOrderServiceItem extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels, \App\Models\Concerns\InvoiceItem;

    protected $fillable = [
        'sell_order_service_id',
        'service_item_id',
        'price',
        'price_currency',
        'company_id',
        'incremental_id',
    ];

    public function sellOrder(): BelongsTo
    {
        return $this->belongsTo(SellOrder::class);
    }

    public function sellOrderService(): BelongsTo
    {
        return $this->belongsTo(SellOrderService::class);
    }

    public function serviceItem(): BelongsTo
    {
        return $this->belongsTo(ServiceItem::class);
    }

    public function getInvoiceable(): ?Model
    {
        return $this->sellOrderService;
    }

    public function getInvoiceableAmount(): ?float
    {
        return $this->price;
    }

    public function getInvoiceableCurrency(): ?string
    {
        return $this->price_currency;
    }

    public function getInvoiceableQuantity(): ?float
    {
        return 1;
    }

    public function getInvoiceItemName(): ?string
    {
        return $this->serviceItem?->name;
    }
}

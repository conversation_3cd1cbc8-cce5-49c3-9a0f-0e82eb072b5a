<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\PaymentParty;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Client extends Model
{
    use SoftDeletes, BelongsToCompany, PaymentParty;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'dial_code',
        'company_name',
        'bank_name',
        'bank_account_number',
        'iban_number',
        'swift_code',
        'country_id',
        'city_id',
        'address',
        'payment_term_id',
        'company_id',
        'incremental_id',
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function modelPorts(): MorphMany
    {
        return $this->morphMany(ModelPort::class, 'model');
    }
}

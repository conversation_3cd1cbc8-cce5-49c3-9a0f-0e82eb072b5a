<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Enums\ProductType;
use App\Enums\StockMovementType;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasModelStates;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class ProductionOrder extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels, HasModelStates;

    protected $fillable = [
        'store_id',
        'product_id',
        'quantity',
        'status',
        'company_id',
        'incremental_id',
    ];

    protected function casts(): array
    {
        return [
            'status' => OrderStatus::class,
        ];
    }

    protected static function booted()
    {
        parent::booted();

        static::creating(function (ProductionOrder $model) {
            $model->status = OrderStatus::PENDING->value;
        });

        static::saved(function (ProductionOrder $productionOrder) {
            if ($productionOrder->wasChanged('status')) {
                if ($productionOrder->status == OrderStatus::COMPLETED) {
                    if (
                        StockMovement::query()
                        ->where('product_id', $productionOrder->product_id)
                        ->where('type', StockMovementType::In->value)
                        ->where('related_type', ProductionOrder::class)
                        ->where('related_id', $productionOrder->id)
                        ->exists()
                    ) {
                        return null;
                    }

                    StockMovement::move(
                        type: StockMovementType::In,
                        product: $productionOrder->product,
                        quantity: $productionOrder->quantity,
                        description: __('Production Order') . " (PDO-{$productionOrder->id})",
                        related: $productionOrder,
                    );

                    $finishedProduct = $productionOrder->product;
                    if (!$finishedProduct || $finishedProduct->productComponents->count() == 0) {
                        return null;
                    }

                    $finishedProduct->productComponents->each(function ($component) use ($productionOrder) {
                        if (
                            StockMovement::query()
                            ->where('product_id', $component->component_id)
                            ->where('type', StockMovementType::Out->value)
                            ->where('related_type', ProductionOrder::class)
                            ->where('related_id', $productionOrder->id)
                            ->exists()
                        ) {
                            return null;
                        }
                        StockMovement::move(
                            type: StockMovementType::Out,
                            product: $component->component,
                            quantity: $component->quantity,
                            description: __('Production Order') . " (PDO-{$productionOrder->id})",
                            related: $productionOrder,
                        );
                    });
                }
            }
        });
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class)
            ->where('type', ProductType::FINISHED->value);
    }

    function stateAttributes()
    {
        return [
            'status',
        ];
    }
}

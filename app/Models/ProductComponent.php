<?php

namespace App\Models;

use App\Enums\ProductType;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\CommonBetweenModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductComponent extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels;

    protected $fillable = [
        'product_id',
        'component_id',
        'quantity',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class)
            ->where('type', ProductType::FINISHED->value);
    }

    public function component()
    {
        return $this->belongsTo(Product::class, 'component_id')
            ->where('type', ProductType::RAW->value);
    }
}

<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class MarketingActivity extends Model
{
    use SoftDeletes, BelongsToCompany, HasTranslations;

    protected $fillable = [
        'name',
        'content',
        'budget',
        'budget_currency',
        'image',
        'marketing_platform_id',
        'company_id',
        'incremental_id',
    ];

    public $translatable = ['content'];

    public function marketingPlatform(): BelongsTo
    {
        return $this->belongsTo(MarketingPlatform::class);
    }
}

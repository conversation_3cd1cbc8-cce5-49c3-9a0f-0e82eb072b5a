<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class FollowUpType extends Model
{
    use SoftDeletes, BelongsToCompany;

    protected $fillable = [
        'name',
        'company_id',
        'incremental_id',
    ];

    public function followUps(): HasMany
    {
        return $this->hasMany(FollowUp::class);
    }
}

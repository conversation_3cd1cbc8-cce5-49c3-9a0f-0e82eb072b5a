<?php

namespace App\Models;

use App\Enums\ProductType;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\CommonBetweenModels;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels;

    protected $fillable = [
        'name',
        'hs_code',
        'commercial_name',
        'buy_price',
        'buy_price_currency',
        'sell_price',
        'sell_price_currency',
        'image',
        'type',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('additional_attributes', function ($query) {
            $query->addSelect('products.*');

            // get quantity based on stock movements sum of in - out
            $query->selectSub(function ($q) {
                $q->from('stock_movements')
                    ->selectRaw('COALESCE(SUM(CASE WHEN stock_movements.type = "in" THEN quantity ELSE -quantity END), 0)')
                    ->whereColumn('stock_movements.product_id', 'products.id')
                    ->whereNull('stock_movements.deleted_at');
            }, 'quantity');
        });
    }

    protected function casts(): array
    {
        return [
            'type' => ProductType::class,
        ];
    }

    public function displayName(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->name . ' - ' . "($this->commercial_name)",
        );
    }

    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(Supplier::class);
    }

    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class);
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    public function productComponents(): HasMany
    {
        return $this->hasMany(ProductComponent::class, 'product_id');
    }
}

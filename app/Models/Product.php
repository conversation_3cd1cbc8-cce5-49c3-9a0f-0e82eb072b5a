<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use SoftDeletes, BelongsToCompany;

    protected $fillable = [
        'name',
        'hs_code',
        'commercial_name',
        'buy_price',
        'buy_price_currency',
        'sell_price',
        'sell_price_currency',
        'image',
        'company_id',
        'incremental_id',
    ];

    public function displayName(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->name . ' - ' . "($this->commercial_name)",
        );
    }

    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(Supplier::class);
    }

    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class);
    }
}

<?php

namespace App\Models;

use App\Enums\PaymentPayoutStatus;
use App\Filament\Resources\SellOrders\SellOrderResource;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasPrices;
use App\Models\Concerns\Invoiceable;
use App\Models\Concerns\PaymentPayable;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;
use Illuminate\Support\Collection;

class SellOrderService extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels, PaymentPayable, HasPrices, Invoiceable;

    protected $fillable = [
        'sell_order_id',
        'service_id',
        'service_provider_id',
        'payment_term_id',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('additional_attributes', function ($query) {
            $query->addSelect('sell_order_services.*');

            $currencies = distinct_currencies();

            foreach ($currencies as $currency) {
                $servicePriceAlias = "price_{$currency}";
                $servicePaidAlias = "paid_{$currency}";
                $serviceRemainingAlias = "remaining_{$currency}";

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('sell_order_service_items')
                        ->selectRaw('COALESCE(SUM(price), 0)')
                        ->whereColumn('sell_order_service_items.sell_order_service_id', 'sell_order_services.id')
                        ->where('price_currency', $currency)            // bound safely
                        ->whereNull('sell_order_service_items.deleted_at');
                }, $servicePriceAlias);

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->where('payments.payable_type', SellOrderService::class)
                        ->where('payments.party_type', ServiceProvider::class)
                        ->whereColumn('sell_order_services.id', 'payments.payable_id')
                        ->where('amount_currency', $currency)
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, $servicePaidAlias);

                $query->selectSub(function ($q) use ($currency, $servicePriceAlias, $servicePaidAlias, $serviceRemainingAlias) {
                    $q->selectRaw("{$servicePriceAlias} - {$servicePaidAlias} AS {$serviceRemainingAlias}");
                }, $serviceRemainingAlias);
            }
        });
    }

    public function rawTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => __('Sell Order Service') . " (SO-{$this->id})",
        );
    }

    public function payoutStatus(): Attribute
    {
        return new Attribute(
            get: function () {
                $currencies = collect(distinct_currencies());

                $fullyPaid = $currencies->filter(function ($currency) {
                        return $this->{"remaining_{$currency}"} <= 0;
                    })->count() == $currencies->count();

                $hasAnyPayment = $currencies->filter(function ($currency) {
                        return $this->{"paid_{$currency}"} > 0;
                    })->count() > 0;

                $partiallyPaid = $currencies->filter(function ($currency) {
                        return $this->{"remaining_{$currency}"} > 0;
                    })->count() > 0 && $hasAnyPayment;

                if ($fullyPaid) {
                    return PaymentPayoutStatus::FULLY_PAID;
                } elseif ($partiallyPaid) {
                    return PaymentPayoutStatus::PARTIALLY_PAID;
                } else {
                    return PaymentPayoutStatus::NO_PAYMENT;
                }
            }
        );
    }

    public function sellOrder(): BelongsTo
    {
        return $this->belongsTo(SellOrder::class);
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function serviceProvider(): BelongsTo
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    public function sellOrderServiceItems(): HasMany
    {
        return $this->hasMany(SellOrderServiceItem::class);
    }

    public function getInvoiceParty(): ?Model
    {
        return $this->serviceProvider;
    }

    public function getInvoiceItems(): Collection
    {
        return $this->sellOrderServiceItems;
    }

    public function scopeFullyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("remaining_{$currency}", '<=', 0);
            }
        });
    }

    public function scopeHasAnyPayment($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->orHaving("paid_{$currency}", '>', 0);
            }
        });
    }

    public function scopePartiallyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->orHaving("remaining_{$currency}", '>', 0);
            }
        })->hasAnyPayment();
    }

    public function scopeNoPayments($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("paid_{$currency}", '<=', 0);
            }
        });
    }

    public function getInvoiceableFilamentUrl(): string
    {
        return SellOrderResource::getUrl('edit', [
            'record' => $this->sell_order_id,
        ]);
    }

    public function getInvoiceTitle()
    {
        return 'Invoice';
    }

    public function getInvoiceNumber()
    {
        return 'INV-' . $this->id;
    }

    public function getBillToInfo()
    {
        return [
            'company_name' => $this->serviceProvider?->company_name,
            'email' => $this->serviceProvider?->email,
            'phone' => $this->serviceProvider?->phone,
            'address' => $this->serviceProvider?->address,
            'country' => $this->serviceProvider?->country?->name,
            'city' => $this->serviceProvider?->city?->name,
        ];
    }

    public function getPaymentTerm()
    {
        return $this->paymentTerm?->name;
    }
}

<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceProvider extends Model
{
    use SoftDeletes, BelongsToCompany;

    protected $fillable = [
        'name',
        'email',
        'dial_code',
        'phone',
        'company_name',
        'address',
        'service_id',
        'country_id',
        'city_id',
        'payment_term_id',
        'bank_name',
        'bank_account_number',
        'iban_number',
        'swift_code',
        'company_id',
        'incremental_id',
    ];

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function serviceProviderItems(): HasMany
    {
        return $this->hasMany(ServiceProviderItem::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function sellOrderServiceItems(): HasMany
    {
        return $this->hasMany(SellOrderServiceItem::class);
    }
}

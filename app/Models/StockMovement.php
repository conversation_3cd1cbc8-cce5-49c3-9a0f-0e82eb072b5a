<?php

namespace App\Models;

use App\Enums\StockMovementType;
use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Concerns\CommonBetweenModels;

class StockMovement extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels;

    protected $fillable = [
        'type',
        'store_id',
        'product_id',
        'quantity',
        'related_id',
        'related_type',
        'creator_id',
        'creator_type',
        'notes',
        'description',
        'company_id',
        'incremental_id',
    ];

    protected static function booted()
    {
        parent::booted();

        static::creating(function (StockMovement $model) {
            $model->creator_id = auth()->id();
            $model->creator_type = auth()->user()->getMorphClass();
        });
    }

    protected function casts(): array
    {
        return [
            'type' => StockMovementType::class,
        ];
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function related(): MorphTo
    {
        return $this->morphTo();
    }

    public function creator(): MorphTo
    {
        return $this->morphTo();
    }

    public static function move(StockMovementType $type, Product $product, int $quantity, $description = null, Model $related = null): static
    {
        return static::create([
            'type' => $type,
            'product_id' => $product->id,
            'quantity' => $quantity,
            'description' => $description,
            'related_id' => $related->id,
            'related_type' => $related->getMorphClass(),
        ]);
    }
}

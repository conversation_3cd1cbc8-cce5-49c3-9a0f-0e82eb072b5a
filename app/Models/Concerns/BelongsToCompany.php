<?php

namespace App\Models\Concerns;

use App\Models\Company;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

trait BelongsToCompany
{
    public static function bootBelongsToCompany()
    {
        static::creating(function (Model $model) {
            if (Filament::getTenant()) {
                DB::beginTransaction();

                $maxId = DB::table($model->getTable())
                    ->where('company_id', Filament::getTenant()?->id)
                    ->lockForUpdate()
                    ->pluck('incremental_id')
                    ->max();

                $model->incremental_id = ($maxId ?? 0) + 1;

                DB::commit();

                $model->company_id = Filament::getTenant()?->id;
            }
        });

        static::addGlobalScope('company', function ($query) {
            $query->where('company_id', Filament::getTenant()?->id);
        });
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}

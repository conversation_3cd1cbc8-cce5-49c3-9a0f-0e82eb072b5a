<?php

namespace App\Models\Concerns;

use App\Models\Company;
use App\Models\ModelState;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use function Laravel\Prompts\form;

trait HasPrices
{
    public function getPriceLabels($attribute): array
    {
        $currencies = distinct_currencies();

        return collect($currencies)
            ->map(function ($currency) use ($attribute) {
                $attribute = $attribute . '_' . $currency;
                return $this->{$attribute} && $this->{$attribute} > 0 ? Number::currency($this->{$attribute}, $currency) : null;
            })->filter()->values()->toArray();
    }
}

<?php

namespace App\Models\Concerns;

use App\Models\Company;
use App\Models\ModelState;
use App\Models\Payment;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use function Laravel\Prompts\form;

trait PaymentParty
{
    public function payments()
    {
        return $this->morphMany(Payment::class, 'party');
    }
}

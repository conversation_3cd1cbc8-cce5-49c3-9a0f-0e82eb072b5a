<?php

namespace App\Models\Concerns;

use App\Models\Company;
use App\Models\ModelState;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use function Laravel\Prompts\form;

trait HasModelStates
{
    public static function bootHasModelStates()
    {
        static::saved(function (Model $model) {
            foreach ($model->stateAttributes() as $attribute) {
                $value = $model->{$attribute}?->value;

                if ($model->wasChanged($attribute) || !in_array($value, data_get($model->getStates(), $attribute, []))) {
                    $model->updateState($attribute, $value);
                }
            }
        });
    }

    abstract function stateAttributes();

    public function modelStates()
    {
        return $this->morphMany(ModelState::class, 'model');
    }

    public function updateState($stateType, $state): self
    {
        $this->modelStates()->create([
            'state_type' => $stateType,
            'state' => $state,
        ]);

        return $this;
    }

    public function getStates()
    {
        return $this->modelStates()->get()
            ->groupBy('state_type')
            ->map(function ($group) {
                return $group->pluck('state')->toArray();
            })
            ->toArray();
    }

    // scopeHasState
    public function scopeHasState($query, $stateType, $state)
    {
        return $query->whereIn('id', function ($query) use ($stateType, $state) {
            return $query->select('model_id')
                ->where('model_type', $this->getMorphClass())
                ->from('model_states')
                ->where('state_type', $stateType)
                ->where('state', $state);
        });
    }
}

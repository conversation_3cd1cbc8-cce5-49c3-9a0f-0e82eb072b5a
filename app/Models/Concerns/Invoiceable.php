<?php

namespace App\Models\Concerns;

use App\Models\Invoice;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

trait Invoiceable
{
    public static function bootInvoiceable()
    {
        static::saved(function (Model $model) {
            $party = $model->getInvoiceParty();

            if ($party) {
                $invoice = $model->invoice;

                if ($invoice) {
                    $invoice->update([
                        'party_id' => $party->id,
                        'party_type' => $party->getMorphClass(),
                    ]);
                }else{
                    $model->invoice()->create([
                        'party_id' => $party->id,
                        'party_type' => $party->getMorphClass(),
                    ]);
                }
            }

            $model->getInvoiceItems()->each(function ($item) use ($model) {
                $item->touch();
            });
        });

        static::deleted(function (Model $model) {
            $model->invoice->delete();
        });

        static::restored(function (Model $model) {
            $model->invoice()->withTrashed()->first()->restore();
        });
    }

    public function invoice()
    {
        return $this->morphOne(Invoice::class, 'invoiceable');
    }

    abstract public function getInvoiceParty(): ?Model;

    abstract public function getInvoiceItems(): Collection;

    abstract public function getInvoiceableFilamentUrl(): string;

    abstract public function getInvoiceTitle();

    abstract public function getInvoiceNumber();

    // getBillToInfo
    abstract public function getBillToInfo();

    // getPaymentTerm
    abstract public function getPaymentTerm();
}

<?php

namespace App\Models\Concerns;

use App\Models\Company;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;

trait MorphToCreator
{
    public static function bootMorphToCreator()
    {
        static::creating(function (Model $model) {
            $model->creator_id = auth()->id();
            $model->creator_type = auth()->user()->getMorphClass();
        });
    }

    public function creator(): MorphTo
    {
        return $this->morphTo('creator');
    }
}

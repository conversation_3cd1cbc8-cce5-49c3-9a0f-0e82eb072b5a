<?php

namespace App\Models\Concerns;

use App\Models\Company;
use App\Models\Invoice;
use App\Models\ModelState;
use App\Models\SellOrder;
use App\Models\SellOrderProduct;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use function Laravel\Prompts\form;

trait InvoiceItem
{
    public static function bootInvoiceItem()
    {
        static::saved(function (Model $model) {
            $fresh = $model->fresh();

            $invoice = $fresh->getInvoiceable()?->fresh()?->invoice;
            $invoiceItem = $fresh->invoiceItem;

            if ($invoice) {
                if ($invoiceItem) {
                    $fresh->invoiceItem->update([
                        'amount' => $fresh->getInvoiceableAmount(),
                        'amount_currency' => $fresh->getInvoiceableCurrency(),
                        'quantity' => $fresh->getInvoiceableQuantity(),
                        'invoice_id' => $invoice->id,
                    ]);
                }else{
                    $fresh->invoiceItem()->create([
                        'amount' => $fresh->getInvoiceableAmount(),
                        'amount_currency' => $fresh->getInvoiceableCurrency(),
                        'quantity' => $fresh->getInvoiceableQuantity(),
                        'invoice_id' => $invoice->id,
                    ]);
                }
            }
        });

        static::deleted(function (Model $model) {
            $model->invoiceItem->delete();
        });

        static::restored(function (Model $model) {
            $model->invoiceItem()->withTrashed()->first()->restore();
        });
    }

    public function invoiceItem()
    {
        return $this->morphOne(\App\Models\InvoiceItem::class, 'itemable');
    }

    abstract public function getInvoiceable(): ?Model;

    abstract public function getInvoiceableAmount(): ?float;

    abstract public function getInvoiceableCurrency(): ?string;

    abstract public function getInvoiceableQuantity(): ?float;

    abstract public function getInvoiceItemName(): ?string;
}

<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use App\Models\Concerns\CommonBetweenModels;

class SellOrderProduct extends Model
{
    use SoftDeletes, BelongsToCompany, CommonBetweenModels, \App\Models\Concerns\InvoiceItem;

    protected $fillable = [
        'sell_order_id',
        'product_id',
        'package_type_id',
        'quantity',
        'company_id',
        'incremental_id',
        'width',
        'height',
        'length',
        'net_weight',
        'gross_weight',
        'sell_price',
        'sell_price_currency',
    ];

    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('additional_attributes', function ($query) {
            $query->addSelect([
                'sell_order_products.*',
                'total_price' => DB::raw('ROUND(sell_price * quantity, 2) AS total_price')
            ]);
        });
    }

    public function sellOrder(): BelongsTo
    {
        return $this->belongsTo(SellOrder::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function packageType(): BelongsTo
    {
        return $this->belongsTo(PackageType::class);
    }

    public function getInvoiceable(): ?Model
    {
        return $this->sellOrder;
    }

    public function getInvoiceableAmount(): ?float
    {
        return $this->sell_price;
    }

    public function getInvoiceableCurrency(): ?string
    {
        return $this->sell_price_currency;
    }

    public function getInvoiceableQuantity(): ?float
    {
        return $this->quantity;
    }

    public function getInvoiceItemName(): ?string
    {
        return $this->product?->name;
    }
}

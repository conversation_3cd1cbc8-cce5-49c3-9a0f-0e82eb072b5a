<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class FollowUp extends Model
{
    use SoftDeletes, BelongsToCompany;

    protected $fillable = [
        'notes',
        'follow_up_date',
        'followable_id',
        'followable_type',
        'follow_up_type_id',
        'company_id',
        'incremental_id',
    ];

    protected function casts(): array
    {
        return [
            'follow_up_date' => 'datetime',
        ];
    }

    public function followable(): MorphTo
    {
        return $this->morphTo();
    }

    public function followUpType(): BelongsTo
    {
        return $this->belongsTo(FollowUpType::class);
    }
}

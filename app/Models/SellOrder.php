<?php

namespace App\Models;

use App\Enums\OrderIncoterm;
use App\Enums\OrderModelOfShipping;
use App\Enums\OrderOrigin;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasPrices;
use App\Models\Concerns\PaymentPayable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class SellOrder extends Model
{
    use SoftDeletes, BelongsToCompany, HasPrices, PaymentPayable;

    protected $fillable = [
        'client_id',
        'origin',
        'payment_term_id',
        'custom_payment_term',
        'incoterm',
        'model_of_shipping',
        'pickup_country_id',
        'pickup_city_id',
        'pickup_address',
        'port_of_loading_id',
        'port_of_loading_type',
        'delivery_country_id',
        'delivery_city_id',
        'delivery_address',
        'port_of_discharge_id',
        'port_of_discharge_type',
        'company_id',
        'incremental_id',
    ];

    protected static function booted(): void
    {
        static::addGlobalScope('additional_attributes', function (Builder $query) {
            $query->addSelect('sell_orders.*');

            $currencies = distinct_currencies();

            foreach ($currencies as $currency) {
                $productPriceAlias = "product_price_{$currency}";
                $productPaidAlias = "product_paid_{$currency}";
                $productRemainingAlias = "product_remaining_{$currency}";

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('sell_order_products')
                        ->selectRaw('COALESCE(SUM(sell_price * quantity), 0)')
                        ->whereColumn('sell_order_products.sell_order_id', 'sell_orders.id')
                        ->where('sell_price_currency', $currency)            // bound safely
                        ->whereNull('sell_order_products.deleted_at');
                }, $productPriceAlias);

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->whereColumn('payments.payable_id', 'sell_orders.id')
                        ->where('payments.payable_type', SellOrder::class)
                        ->where('payments.party_type', Client::class)
                        ->where('amount_currency', $currency)            // bound safely
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, $productPaidAlias);

                $query->selectSub(function ($q) use ($currency, $productPriceAlias, $productPaidAlias, $productRemainingAlias) {
                    $q->selectRaw("{$productPriceAlias} - {$productPaidAlias} AS {$productRemainingAlias}");
                }, $productRemainingAlias);
            }

            foreach ($currencies as $currency) {
                $serviceCostAlias = "service_cost_{$currency}";
                $servicePaidAlias = "service_paid_{$currency}";
                $serviceRemainingAlias = "service_remaining_{$currency}";

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('sell_order_services')
                        ->join('sell_order_service_items', 'sell_order_services.id', '=', 'sell_order_service_items.sell_order_service_id')
                        ->selectRaw('COALESCE(SUM(sell_order_service_items.price), 0)')
                        ->whereColumn('sell_order_services.sell_order_id', 'sell_orders.id')
                        ->whereNull('sell_order_services.deleted_at')
                        ->whereNull('sell_order_service_items.deleted_at')
                        ->where('sell_order_service_items.price_currency', $currency); // <-- remove if you don't track currency here
                }, $serviceCostAlias);

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->where('payments.payable_type', SellOrderService::class)
                        ->where('payments.party_type', ServiceProvider::class)
                        ->join('sell_order_services', 'payments.payable_id', '=', 'sell_order_services.id')
                        ->whereColumn('sell_order_services.sell_order_id', 'sell_orders.id')
                        ->where('amount_currency', $currency)
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, $servicePaidAlias);

                $query->selectSub(function ($q) use ($currency, $serviceCostAlias, $servicePaidAlias, $serviceRemainingAlias) {
                    $q->selectRaw("{$serviceCostAlias} - {$servicePaidAlias} AS {$serviceRemainingAlias}");
                }, $serviceRemainingAlias);
            }
        });
    }
    protected function casts(): array
    {
        return [
            'model_of_shipping' => OrderModelOfShipping::class,
            'incoterm' => OrderIncoterm::class,
            'origin' => OrderOrigin::class,
        ];
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function pickupCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'pickup_country_id');
    }

    public function pickupCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'pickup_city_id');
    }

    public function deliveryCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'delivery_country_id');
    }

    public function deliveryCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'delivery_city_id');
    }

    public function portOfLoading(): MorphTo
    {
        return $this->morphTo();
    }

    public function portOfDischarge(): MorphTo
    {
        return $this->morphTo();
    }

    public function sellOrderProducts(): HasMany
    {
        return $this->hasMany(SellOrderProduct::class);
    }

    public function sellOrderServices(): HasMany
    {
        return $this->hasMany(SellOrderService::class);
    }
}

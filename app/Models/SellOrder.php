<?php

namespace App\Models;

use App\Enums\OrderIncoterm;
use App\Enums\OrderModelOfShipping;
use App\Enums\OrderOrigin;
use App\Enums\OrderStatus;
use App\Enums\PaymentPayoutStatus;
use App\Enums\StockMovementType;
use App\Filament\Resources\SellOrders\SellOrderResource;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasModelStates;
use App\Models\Concerns\HasPrices;
use App\Models\Concerns\Invoiceable;
use App\Models\Concerns\PaymentPayable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Models\Concerns\CommonBetweenModels;

class SellOrder extends Model
{
    use SoftDeletes, BelongsToCompany, HasPrices, PaymentPayable, CommonBetweenModels, Invoiceable, HasModelStates;

    protected $fillable = [
        'client_id',
        'status',
        'origin',
        'payment_term_id',
        'custom_payment_term',
        'incoterm',
        'model_of_shipping',
        'pickup_country_id',
        'pickup_city_id',
        'pickup_address',
        'port_of_loading_id',
        'port_of_loading_type',
        'delivery_country_id',
        'delivery_city_id',
        'delivery_address',
        'port_of_discharge_id',
        'port_of_discharge_type',
        'company_id',
        'incremental_id',
    ];

    protected static function booted(): void
    {
        static::creating(function ($sellOrder) {
            $sellOrder->status = OrderStatus::PENDING->value;
        });

        static::saved(function (SellOrder $sellOrder) {
            if ($sellOrder->wasChanged('status')) {
                if ($sellOrder->status == OrderStatus::COMPLETED) {
                    foreach ($sellOrder->sellOrderProducts as $sellOrderProduct) {
                        if (
                            StockMovement::query()
                            ->where('product_id', $sellOrderProduct->product_id)
                            ->where('type', StockMovementType::Out->value)
                            ->where('related_type', SellOrder::class)
                            ->where('related_id', $sellOrder->id)
                            ->exists()
                        ) {
                            continue ;
                        }
                        StockMovement::move(
                            type: StockMovementType::Out,
                            product: $sellOrderProduct->product,
                            quantity: $sellOrderProduct->quantity,
                            description: __('Sell Order') . " (SO-{$sellOrder->id})",
                            related: $sellOrder,
                        );
                    }
                }
            }
        });

        static::addGlobalScope('additional_attributes', function (Builder $query) {
            $query->addSelect('sell_orders.*');

            $currencies = distinct_currencies();

            foreach ($currencies as $currency) {
                $productPriceAlias = "price_{$currency}";
                $productPaidAlias = "paid_{$currency}";
                $productRemainingAlias = "remaining_{$currency}";

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('sell_order_products')
                        ->selectRaw('COALESCE(SUM(sell_price * quantity), 0)')
                        ->whereColumn('sell_order_products.sell_order_id', 'sell_orders.id')
                        ->where('sell_price_currency', $currency)            // bound safely
                        ->whereNull('sell_order_products.deleted_at');
                }, $productPriceAlias);

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->whereColumn('payments.payable_id', 'sell_orders.id')
                        ->where('payments.payable_type', SellOrder::class)
                        ->where('payments.party_type', Client::class)
                        ->where('amount_currency', $currency)            // bound safely
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, $productPaidAlias);

                $query->selectSub(function ($q) use ($currency, $productPriceAlias, $productPaidAlias, $productRemainingAlias) {
                    $q->selectRaw("{$productPriceAlias} - {$productPaidAlias} AS {$productRemainingAlias}");
                }, $productRemainingAlias);
            }

            foreach ($currencies as $currency) {
                $serviceCostAlias = "service_cost_{$currency}";
                $servicePaidAlias = "service_paid_{$currency}";
                $serviceRemainingAlias = "service_remaining_{$currency}";

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('sell_order_services')
                        ->join('sell_order_service_items', 'sell_order_services.id', '=', 'sell_order_service_items.sell_order_service_id')
                        ->selectRaw('COALESCE(SUM(sell_order_service_items.price), 0)')
                        ->whereColumn('sell_order_services.sell_order_id', 'sell_orders.id')
                        ->whereNull('sell_order_services.deleted_at')
                        ->whereNull('sell_order_service_items.deleted_at')
                        ->where('sell_order_service_items.price_currency', $currency); // <-- remove if you don't track currency here
                }, $serviceCostAlias);

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->where('payments.payable_type', SellOrderService::class)
                        ->where('payments.party_type', ServiceProvider::class)
                        ->join('sell_order_services', 'payments.payable_id', '=', 'sell_order_services.id')
                        ->whereColumn('sell_order_services.sell_order_id', 'sell_orders.id')
                        ->where('amount_currency', $currency)
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, $servicePaidAlias);

                $query->selectSub(function ($q) use ($currency, $serviceCostAlias, $servicePaidAlias, $serviceRemainingAlias) {
                    $q->selectRaw("{$serviceCostAlias} - {$servicePaidAlias} AS {$serviceRemainingAlias}");
                }, $serviceRemainingAlias);
            }
        });
    }

    public function rawTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => __('Sell Order') . " (SO-{$this->id})",
        );
    }

    public function payoutStatus(): Attribute
    {
        return new Attribute(
            get: function () {
                $currencies = collect(distinct_currencies());

                $fullyPaid = $currencies->filter(function ($currency) {
                        return $this->{"remaining_{$currency}"} <= 0;
                    })->count() == $currencies->count();

                $hasAnyPayment = $currencies->filter(function ($currency) {
                        return $this->{"paid_{$currency}"} > 0;
                    })->count() > 0;

                $partiallyPaid = $currencies->filter(function ($currency) {
                        return $this->{"remaining_{$currency}"} > 0;
                    })->count() > 0 && $hasAnyPayment;

                if ($fullyPaid) {
                    return PaymentPayoutStatus::FULLY_PAID;
                } elseif ($partiallyPaid) {
                    return PaymentPayoutStatus::PARTIALLY_PAID;
                } else {
                    return PaymentPayoutStatus::NO_PAYMENT;
                }
            }
        );
    }

    // servicePaymentStatus
    public function servicePaymentStatus(): Attribute
    {
        return new Attribute(
            get: function () {
                $currencies = collect(distinct_currencies());

                $fullyPaid = $currencies->filter(function ($currency) {
                        return $this->{"service_remaining_{$currency}"} <= 0;
                    })->count() == $currencies->count();

                $hasAnyPayment = $currencies->filter(function ($currency) {
                        return $this->{"service_paid_{$currency}"} > 0;
                    })->count() > 0;

                $partiallyPaid = $currencies->filter(function ($currency) {
                        return $this->{"service_remaining_{$currency}"} > 0;
                    })->count() > 0 && $hasAnyPayment;

                if ($fullyPaid) {
                    return PaymentPayoutStatus::FULLY_PAID;
                } elseif ($partiallyPaid) {
                    return PaymentPayoutStatus::PARTIALLY_PAID;
                } else {
                    return PaymentPayoutStatus::NO_PAYMENT;
                }
            }
        );
    }

    protected function casts(): array
    {
        return [
            'model_of_shipping' => OrderModelOfShipping::class,
            'incoterm' => OrderIncoterm::class,
            'origin' => OrderOrigin::class,
            'status' => OrderStatus::class,
        ];
    }


    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function pickupCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'pickup_country_id');
    }

    public function pickupCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'pickup_city_id');
    }

    public function deliveryCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'delivery_country_id');
    }

    public function deliveryCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'delivery_city_id');
    }

    public function portOfLoading(): MorphTo
    {
        return $this->morphTo();
    }

    public function portOfDischarge(): MorphTo
    {
        return $this->morphTo();
    }

    public function sellOrderProducts(): HasMany
    {
        return $this->hasMany(SellOrderProduct::class);
    }

    public function sellOrderServices(): HasMany
    {
        return $this->hasMany(SellOrderService::class);
    }

    public function scopeFullyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("remaining_{$currency}", '<=', 0);
            }
        });
    }

    public function scopeHasAnyPayment($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->orHaving("paid_{$currency}", '>', 0);
            }
        });
    }

    public function scopePartiallyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->orHaving("remaining_{$currency}", '>', 0);
            }
        })->hasAnyPayment();
    }

    public function scopeNoPayments($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("paid_{$currency}", '<=', 0);
            }
        });
    }

    // serviceFullyPaid
    public function scopeServiceFullyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_remaining_{$currency}", '<=', 0);
            }
        });
    }

    public function scopeHasAnyServicePayment($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_paid_{$currency}", '>', 0);
            }
        });
    }

    // servicePartiallyPaid
    public function scopeServicePartiallyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_remaining_{$currency}", '>', 0);
            }
        })->hasAnyServicePayment();
    }

    // noServicePayment
    public function scopeNoServicePayment($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_paid_{$currency}", '<=', 0);
            }
        });
    }

    public function getInvoiceParty(): ?Model
    {
        return $this->client;
    }

    public function getInvoiceItems(): Collection
    {
        return $this->sellOrderProducts;
    }

    public function getInvoiceableFilamentUrl(): string
    {
        return SellOrderResource::getUrl('edit', [
            'record' => $this,
        ]);
    }

    public function getInvoiceTitle()
    {
        return 'Invoice';
    }

    public function getInvoiceNumber()
    {
        return 'INV-' . $this->id;
    }

    public function getBillToInfo()
    {
        return [
            'company_name' => $this->client?->company_name,
            'email' => $this->client?->email,
            'phone' => $this->client?->phone,
            'address' => $this->client?->address,
            'country' => $this->client?->country?->name,
            'city' => $this->client?->city?->name,
        ];
    }

    public function getPaymentTerm()
    {
        return $this->paymentTerm?->name;
    }

    function stateAttributes()
    {
        return [
            'status',
        ];
    }
}

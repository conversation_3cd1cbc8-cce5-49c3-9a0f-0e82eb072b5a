<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ModelState extends Model
{
    protected $fillable = [
        'user_id',
        'user_type',
        'model_id',
        'model_type',
        'state_type',
        'state',
    ];

    protected static function booted()
    {
        parent::booted();

        static::creating(function (ModelState $model) {
            $model->user_id = auth()->id();
            $model->user_type = auth()->user()->getMorphClass();
        });
    }

    public function model()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->morphTo();
    }

    public function getState()
    {
        return $this->model?->{$this->state_type};
    }
}

<?php

namespace App\Models;

use App\Enums\OrderIncoterm;
use App\Enums\OrderModelOfShipping;
use App\Enums\OrderOrigin;
use App\Enums\OrderStatus;
use App\Enums\PaymentPayoutStatus;
use App\Enums\StockMovementType;
use App\Filament\Resources\PurchaseOrders\PurchaseOrderResource;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasModelStates;
use App\Models\Concerns\HasPrices;
use App\Models\Concerns\Invoiceable;
use App\Models\Concerns\PaymentPayable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use App\Models\Concerns\CommonBetweenModels;

class PurchaseOrder extends Model
{
    use SoftDeletes, BelongsToCompany, HasPrices, PaymentPayable, CommonBetweenModels, Invoiceable, HasModelStates;

    protected $fillable = [
        'supplier_id',
        'status',
        'origin',
        'payment_term_id',
        'custom_payment_term',
        'incoterm',
        'model_of_shipping',
        'pickup_country_id',
        'pickup_city_id',
        'pickup_address',
        'port_of_loading_id',
        'port_of_loading_type',
        'delivery_country_id',
        'delivery_city_id',
        'delivery_address',
        'port_of_discharge_id',
        'port_of_discharge_type',
        'company_id',
        'incremental_id',
    ];

    protected static function booted(): void
    {
        parent::booted();

        static::creating(function ($purchaseOrder) {
            $purchaseOrder->status = OrderStatus::PENDING->value;
        });

        static::saved(function (PurchaseOrder $purchaseOrder) {
            if ($purchaseOrder->wasChanged('status')) {
                if ($purchaseOrder->status == OrderStatus::COMPLETED) {
                    foreach ($purchaseOrder->purchaseOrderProducts as $purchaseOrderProduct) {
                        if (
                            StockMovement::query()
                            ->where('product_id', $purchaseOrderProduct->product_id)
                            ->where('type', StockMovementType::In->value)
                            ->where('related_type', PurchaseOrder::class)
                            ->where('related_id', $purchaseOrder->id)
                            ->exists()
                        ) {
                            continue ;
                        }
                        StockMovement::move(
                            type: StockMovementType::In,
                            product: $purchaseOrderProduct->product,
                            quantity: $purchaseOrderProduct->quantity,
                            description: __('Purchase Order') . " (PO-{$purchaseOrder->id})",
                            related: $purchaseOrder,
                        );
                    }
                }
            }
        });

        static::addGlobalScope('additional_attributes', function (Builder $query) {
            $query->addSelect('purchase_orders.*');

            $currencies = distinct_currencies();

            foreach ($currencies as $currency) {
                $productPriceAlias = "price_{$currency}";
                $productPaidAlias = "paid_{$currency}";
                $productRemainingAlias = "remaining_{$currency}";

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('purchase_order_products')
                        ->selectRaw('COALESCE(SUM(buy_price * quantity), 0)')
                        ->whereColumn('purchase_order_products.purchase_order_id', 'purchase_orders.id')
                        ->where('buy_price_currency', $currency)            // bound safely
                        ->whereNull('purchase_order_products.deleted_at');
                }, $productPriceAlias);

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->whereColumn('payments.payable_id', 'purchase_orders.id')
                        ->where('payments.payable_type', PurchaseOrder::class)
                        ->where('payments.party_type', Supplier::class)
                        ->where('amount_currency', $currency)            // bound safely
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, $productPaidAlias);

                $query->selectSub(function ($q) use ($currency, $productPriceAlias, $productPaidAlias, $productRemainingAlias) {
                    $q->selectRaw("{$productPriceAlias} - {$productPaidAlias} AS {$productRemainingAlias}");
                }, $productRemainingAlias);
            }

            foreach ($currencies as $currency) {
                $serviceCostAlias = "service_cost_{$currency}";
                $servicePaidAlias = "service_paid_{$currency}";
                $serviceRemainingAlias = "service_remaining_{$currency}";

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('purchase_order_services')
                        ->join('purchase_order_service_items', 'purchase_order_services.id', '=', 'purchase_order_service_items.purchase_order_service_id')
                        ->selectRaw('COALESCE(SUM(purchase_order_service_items.price), 0)')
                        ->whereColumn('purchase_order_services.purchase_order_id', 'purchase_orders.id')
                        ->whereNull('purchase_order_services.deleted_at')
                        ->whereNull('purchase_order_service_items.deleted_at')
                        ->where('purchase_order_service_items.price_currency', $currency); // <-- remove if you don't track currency here
                }, $serviceCostAlias);

                $query->selectSub(function ($q) use ($currency) {
                    $q->from('payments')
                        ->selectRaw('COALESCE(SUM(amount), 0)')
                        ->where('payments.payable_type', PurchaseOrderService::class)
                        ->where('payments.party_type', ServiceProvider::class)
                        ->join('purchase_order_services', 'payments.payable_id', '=', 'purchase_order_services.id')
                        ->whereColumn('purchase_order_services.purchase_order_id', 'purchase_orders.id')
                        ->where('amount_currency', $currency)
                        ->whereNull('payments.deleted_at')
                        ->whereNotNull('payments.paid_at');
                }, $servicePaidAlias);

                $query->selectSub(function ($q) use ($currency, $serviceCostAlias, $servicePaidAlias, $serviceRemainingAlias) {
                    $q->selectRaw("{$serviceCostAlias} - {$servicePaidAlias} AS {$serviceRemainingAlias}");
                }, $serviceRemainingAlias);
            }
        });
    }

    public function rawTitle(): Attribute
    {
        return Attribute::make(
            get: fn() => __('Purchase Order') . " (SO-{$this->id})",
        );
    }

    public function payoutStatus(): Attribute
    {
        return new Attribute(
            get: function () {
                $currencies = collect(distinct_currencies());

                $fullyPaid = $currencies->filter(function ($currency) {
                        return $this->{"remaining_{$currency}"} <= 0;
                    })->count() == $currencies->count();

                $hasAnyPayment = $currencies->filter(function ($currency) {
                        return $this->{"paid_{$currency}"} > 0;
                    })->count() > 0;

                $partiallyPaid = $currencies->filter(function ($currency) {
                        return $this->{"remaining_{$currency}"} > 0;
                    })->count() > 0 && $hasAnyPayment;

                if ($fullyPaid) {
                    return PaymentPayoutStatus::FULLY_PAID;
                } elseif ($partiallyPaid) {
                    return PaymentPayoutStatus::PARTIALLY_PAID;
                } else {
                    return PaymentPayoutStatus::NO_PAYMENT;
                }
            }
        );
    }

    // servicePaymentStatus
    public function servicePaymentStatus(): Attribute
    {
        return new Attribute(
            get: function () {
                $currencies = collect(distinct_currencies());

                $fullyPaid = $currencies->filter(function ($currency) {
                        return $this->{"service_remaining_{$currency}"} <= 0;
                    })->count() == $currencies->count();

                $hasAnyPayment = $currencies->filter(function ($currency) {
                        return $this->{"service_paid_{$currency}"} > 0;
                    })->count() > 0;

                $partiallyPaid = $currencies->filter(function ($currency) {
                        return $this->{"service_remaining_{$currency}"} > 0;
                    })->count() > 0 && $hasAnyPayment;

                if ($fullyPaid) {
                    return PaymentPayoutStatus::FULLY_PAID;
                } elseif ($partiallyPaid) {
                    return PaymentPayoutStatus::PARTIALLY_PAID;
                } else {
                    return PaymentPayoutStatus::NO_PAYMENT;
                }
            }
        );
    }

    protected function casts(): array
    {
        return [
            'model_of_shipping' => OrderModelOfShipping::class,
            'incoterm' => OrderIncoterm::class,
            'origin' => OrderOrigin::class,
            'status' => OrderStatus::class,
        ];
    }


    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    public function pickupCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'pickup_country_id');
    }

    public function pickupCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'pickup_city_id');
    }

    public function deliveryCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'delivery_country_id');
    }

    public function deliveryCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'delivery_city_id');
    }

    public function portOfLoading(): MorphTo
    {
        return $this->morphTo();
    }

    public function portOfDischarge(): MorphTo
    {
        return $this->morphTo();
    }

    public function purchaseOrderProducts(): HasMany
    {
        return $this->hasMany(PurchaseOrderProduct::class);
    }

    public function purchaseOrderServices(): HasMany
    {
        return $this->hasMany(PurchaseOrderService::class);
    }

    public function scopeFullyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("remaining_{$currency}", '<=', 0);
            }
        });
    }

    public function scopeHasAnyPayment($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->orHaving("paid_{$currency}", '>', 0);
            }
        });
    }

    public function scopePartiallyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->orHaving("remaining_{$currency}", '>', 0);
            }
        })->hasAnyPayment();
    }

    public function scopeNoPayments($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("paid_{$currency}", '<=', 0);
            }
        });
    }

    // serviceFullyPaid
    public function scopeServiceFullyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_remaining_{$currency}", '<=', 0);
            }
        });
    }

    public function scopeHasAnyServicePayment($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_paid_{$currency}", '>', 0);
            }
        });
    }

    // servicePartiallyPaid
    public function scopeServicePartiallyPaid($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_remaining_{$currency}", '>', 0);
            }
        })->hasAnyServicePayment();
    }

    // noServicePayment
    public function scopeNoServicePayment($query)
    {
        return $query->having(function ($query) {
            foreach (distinct_currencies() as $currency) {
                $query->having("service_paid_{$currency}", '<=', 0);
            }
        });
    }

    public function getInvoiceParty(): ?Model
    {
        return $this->supplier;
    }

    public function getInvoiceItems(): Collection
    {
        return $this->purchaseOrderProducts;
    }

    public function getInvoiceableFilamentUrl(): string
    {
        return PurchaseOrderResource::getUrl('edit', [
            'record' => $this,
        ]);
    }

    public function getInvoiceTitle()
    {
        return 'Invoice';
    }

    public function getInvoiceNumber()
    {
        return 'PO-' . $this->id;
    }

    public function getBillToInfo()
    {
        return [
            'company_name' => $this->supplier?->company_name,
            'email' => $this->supplier?->email,
            'phone' => $this->supplier?->phone,
            'address' => $this->supplier?->address,
            'country' => $this->supplier?->country?->name,
            'city' => $this->supplier?->city?->name,
        ];
    }

    public function getPaymentTerm()
    {
        return $this->paymentTerm?->name;
    }

    function stateAttributes()
    {
        return [
            'status',
        ];
    }
}

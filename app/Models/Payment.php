<?php

namespace App\Models;

use App\Enums\PaymentDueStatus;
use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\MorphToCreator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use SoftDeletes, BelongsToCompany, MorphToCreator;

    protected $fillable = [
        'payable_id',
        'payable_type',
        'party_id',
        'party_type',
        'payment_method_id',
        'creator_id',
        'creator_type',
        'amount',
        'amount_currency',
        'due_date',
        'paid_at',
        'attachments',
        'notes',
        'company_id',
        'incremental_id',
    ];

    protected function casts(): array
    {
        return [
            'due_date' => 'datetime',
            'paid_at' => 'datetime',
            'attachments' => 'array',
        ];
    }

    public function dueStatus(): Attribute
    {
        return new Attribute(
            get: function () {
                if ($this->paid_at) {
                    return PaymentDueStatus::PAID;
                } elseif ($this->due_date->isFuture()) {
                    return PaymentDueStatus::FUTURE;
                } else {
                    return PaymentDueStatus::DUE;
                }
            }
        );
    }

    public function payable()
    {
        return $this->morphTo();
    }

    public function party()
    {
        return $this->morphTo();
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function creator()
    {
        return $this->morphTo();
    }

    public function scopePaid(Builder $query)
    {
        return $query->whereNotNull('paid_at');
    }

    public function scopeUnpaid(Builder $query)
    {
        return $query->whereNull('paid_at');
    }

    public function scopeDue(Builder $query)
    {
        return $query->whereDate('due_date', '<=', now());
    }

    public function scopeFuture(Builder $query)
    {
        return $query->whereDate('due_date', '>', now());
    }
}

<?php

namespace App\Models;

use App\Models\Concerns\BelongsToCompany;
use App\Models\Concerns\HasModelStates;
use App\Enums\TaskStatus;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON><PERSON>\Commentions\Contracts\Commentable;
use <PERSON><PERSON>baum\Commentions\HasComments;

class Task extends Model implements Commentable
{
    use SoftDeletes, BelongsToCompany, HasModelStates, HasComments;

    protected $fillable = [
        'title',
        'description',
        'creator_id',
        'due_date',
        'assignee_id',
        'company_id',
        'incremental_id',
        'status'
    ];

    protected function casts(): array
    {
        return [
            'due_date' => 'datetime',
            'status' => TaskStatus::class,
        ];
    }

    public function stateAttributes()
    {
        return [
            'status',
        ];
    }
    protected static function boot()
    {
        parent::boot();

        static::creating(function (Task $model) {
            $model->creator_id = auth()->id();
            $model->status = TaskStatus::Pending;
        });

        static::created(function (Task $model) {
            if ($model->assignee_id != $model->creator_id) {
                $model->assignee->notify(
                    Notification::make()
                        ->title(__('You have a new task!'))
                        ->body($model->title)
                        ->toDatabase(),
                );
            }
        });

        static::addGlobalScope('filter_by_normal_user', function ($query) {
            if (is_normal()) {
                $query->where('assignee_id', auth()->id());
            }
        });
    }

    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function taskStatuses(): BelongsToMany
    {
        return $this->belongsToMany(TaskStatus::class);
    }

    public function scopeDue($query)
    {
        return $query->whereDate('due_date', '<', now())
            ->where('status', '!=', TaskStatus::Completed->value);
    }
}

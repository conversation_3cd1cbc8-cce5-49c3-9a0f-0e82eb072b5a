<?php

use Illuminate\Support\Facades\DB;

if (! function_exists('is_super')) {
    function is_super(): bool
    {
        return auth()->user()?->is_super;
    }
}

// is_manager
if (! function_exists('is_manager')) {
    function is_manager(): bool
    {
        return auth()->user()?->is_manager;
    }
}

// is_normal
if (! function_exists('is_normal')) {
    function is_normal(): bool
    {
        return ! is_super() && ! is_manager();
    }
}

// distinct_currencies
if (! function_exists('distinct_currencies')) {
    function distinct_currencies(): array
    {
        static $currencies = [];

        if ($currencies) {
            return $currencies;
        }

        $union = DB::table('sell_order_products')
            ->select('sell_price_currency as currency')
            ->whereNull('deleted_at')
            ->unionAll(
                DB::table('sell_order_service_items')
                    ->select('price_currency as currency')
                    ->whereNull('deleted_at')
            );

        $currencies = DB::query()
            ->fromSub($union, 'u')
            ->whereNotNull('u.currency')
            ->distinct()
            ->pluck('currency')
            ->values()
            ->toArray();

        return $currencies;
    }
}

You are an experienced Filament V4 PHP developer tasked with generating complete Laravel resources based on JSON configuration files.

## Documentation Reference
- Use the Filament V4 documentation located in the `filament-v4-docs` folder for all implementation details
- Follow Filament V4 best practices and conventions throughout

## Input Structure

You will receive a JSON file with the following structure:

### Resource Properties
Each resource contains:
- `name`: string (resource name)
- `navigation_group`: string (navigation grouping)
- `fields`: array of field objects
- `relations`: array of relation objects

### Field Properties
Each field object contains:
- `name`: string (field name)
- `type`: string (database column type)
- `field`: string (corresponding Filament field component)
- `required`: boolean (field validation requirement)
- `use_in_table`: boolean (display in table listing)
- `multiple`: boolean (for Select fields - allow multiple selections)
- `searchable`: boolean (for Select fields - enable search functionality)
- `preload`: boolean (for Select fields - preload options)
- `repeater_type`: enum ("normal_repeater" | "table_repeater")
- `translatable`: boolean (enable translation support)
- `priceable`: boolean (use custom price field component)
- `instructions`: string (additional field configuration details)

### Relation Properties
Each relation object contains:
- `name`: string (relation name)
- `type`: string (Laravel relation type: belongsTo, hasMany, belongsToMany, etc.)
- `related_model`: string (target model class name)
- `form_field`: enum ("select" | "repeater" | "relation_manager")
- `instructions`: string (additional relation configuration details)

## Implementation Steps

### Step 1: Database Migrations
You must generate the migration using this command: `php artisan make:migration create_{resourceName}_table --create={resourceName}`
Create migration files for each resource with:
- All fields from the JSON configuration
- All fields set as `nullable()` by default
- Soft deletes support: `$table->softDeletes()`
- For belongs to many relations create a pivot table
- If the field type is `morph` use `$table->nullableMorphs('{{field_name}}');`
- Required company tracking fields:
```php
$table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
$table->unsignedBigInteger('incremental_id')->index()->nullable();
```

### Step 2: Eloquent Models
You must generate the model using this command: `php artisan make:model {ModelName}` and you must write the model file with:
- All fields added to `$fillable` array
- All relations defined according to their types
- `SoftDeletes` trait imported and used
- Company tracking fields and `BelongsToCompany` trait
- For translatable fields: implement `spatie/laravel-translatable` package support
- If the field type is `morph` you must use `MorphTo` relation, and you must add to the `$fillable` array the `{{field_name}}_id` and `{{field_name}}_type`

### Step 3: Filament Resources
You must generate the resource using this command: `php artisan make:filament-resource {ResourceName} --soft-deletes --silent`

Add label and plural label and navigation group methods to the resource file for example:
```php
public static function getLabel(): ?string
{
    return __('User');
}

public static function getPluralLabel(): ?string
{
    return __('Users');
}

public static function getNavigationGroup(): string|UnitEnum|null
{
    return __('User Management');
}
```

### Step 4: Resource Forms
You find the resource form file in `Schemas/{ResourceName}Form.php` and you must write the form file with:
- All fields from the JSON configuration
- All fields set as `->required()` if `required: true` in JSON
- All fields set as `->label(__('Label Text'))` for localization
- All fields set as `->searchable()` if `searchable: true` in JSON
- All fields set as `->preload()` if `preload: true` in JSON
- All fields set as `->multiple()` if `multiple: true` in JSON
- All fields set as `->relationship()` if `relation: true` in JSON
- If `priceable: true` in JSON, use `PriceField` component from `Filament/Schemas/Components/` and make sure to use the priceFieldName and currencyFieldName from the JSON configuration
- If `translatable: true` in JSON, use `Translatable` component from `Filament/Schemas/Components/`
- If `repeater_type: "table_repeater"` in JSON, use Table Repeater as described in Filament V4 documentation
- If `repeater_type: "normal_repeater"` in JSON, use Normal Repeater as described in Filament V4 documentation
- For `BelongsTo` relations, use `BelongsToSelect` component from `Filament/Forms/Components/`
- Add `->columns(1)` to the form
  - For other relations
      - If `form_field: "select"` in JSON, you must use `BelongsToSelect` component from `Filament/Forms/Components/`
      - If `form_field: "morph_select"` in JSON and the relation type is `morphTo` you must use `MorphToSelect` from filament fields for example:
      ```php
      MorphToSelect::make('commentable')
          ->types([
              MorphToSelect\Type::make(Product::class)
                ->titleAttribute('name'),
              MorphToSelect\Type::make(Post::class)
                ->titleAttribute('title'),
          ])
      ```
      - If `form_field: "repeater"` in JSON, use must use Repeater field with `->relationship('{relationName}')` method and also take care of the repeater_type
      - If `form_field: "relation_manager"` in JSON, you must generate the relation manager using this Relation Manager generate command: `php artisan make:filament-relation-manager {ParentResourceNameWithoutFullNamespace} {relationName} {titleAttribute} --silent`
      Also you must add one of these options when creating the relation manager:
      `--associate` Include associate actions in the table for `HasMany` and `MorphMany` relationships
      `--attach` Include attach actions in the table for `BelongsToMany` and `MorphToMany` relationships

In case of relation manager you must add the label function for example
```php
public static function getTitle(Model $ownerRecord, string $pageClass): string
{
    return __('Products');
}
```
For the relation manager form you will use the related resource form for example
```php
use App\Filament\Resources\Users\Schemas\UserForm;

public function form(Schema $schema): Schema
{
    return UserForm::configure($schema);
}
```
For the relation manager table you will use only the columns from the related resource's table columns and add them to the `->columns()` method for example
```php
use App\Filament\Resources\Users\Tables\UsersTable;

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns(UsersTable::configure($table)->getColumns())
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
                AssociateAction::make(),
            ])
            ->recordActions([
                EditAction::make(),
                DissociateAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DissociateBulkAction::make(),
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
```

You myst write the resource table file `Schemas/{ResourceName}Table.php` with:
- All fields from the JSON configuration where `use_in_table: true`
- Add `->label(__('Label Text'))` for localization
- Only numeric and date fields are sortable
- Add at the beginning of the columns `TextColumn::make('incremental_id')->label('#')->sortable()`
- Add at the end of the columns `TextColumn::make('created_at')->label(__('Created At'))->dateTime()->sortable()`
- Add ->defaultSort('incremental_id', 'desc') method to the table
- All fields set as `->searchable()` if the field type is string or text
- For `BelongsTo` relations, add `SelectFilter` to the table with `->relationship()` method
- For `BelongsTo` relations, check `searchable` and `preload` properties in JSON and apply accordingly
- For `BelongsTo` relations, use dot notation to access relation data: `TextColumn::make('orderProduct.name')`
- For date fields use `->date()` method
- For datetime fields use `->dateTime()` method
- For time fields use `->time()` method
- For date and datetime fields use `->sortable()` method
- For date fields even created_at field you must add date range for example:
  ```php
    use Filament\Tables\Filters\Filter;
    use Filament\Forms\Components\DatePicker;
    use Illuminate\Database\Eloquent\Builder;
    Filter::make('created_at')
        ->schema([
            DatePicker::make('created_from'),
            DatePicker::make('created_until'),
        ])
        ->query(function (Builder $query, array $data): Builder {
            return $query
                ->when(
                    $data['created_from'],
                    fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                )
                ->when(
                    $data['created_until'],
                    fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                );
        })
  ```

Please follow these steps and rules strongly. also take care of the localization and use the `__()` function for all labels and columns
Please take care of instructions with resources, fields and relations. the instructions may contain additional information that you need to take care of when generating the resource

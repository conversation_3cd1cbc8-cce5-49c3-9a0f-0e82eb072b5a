{"__meta": {"id": "01K3BF691VSXREGVH6S1WX277Q", "datetime": "2025-08-23 12:41:55", "utime": **********.515977, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.39476, "end": **********.515982, "duration": 0.12122201919555664, "duration_str": "121ms", "measures": [{"label": "Booting", "start": **********.39476, "relative_start": 0, "end": **********.4587, "relative_end": **********.4587, "duration": 0.*****************, "duration_str": "63.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.458707, "relative_start": 0.*****************, "end": **********.515983, "relative_end": 1.1920928955078125e-06, "duration": 0.057276010513305664, "duration_str": "57.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.480637, "relative_start": 0.****************, "end": **********.480802, "relative_end": **********.480802, "duration": 0.00016498565673828125, "duration_str": "165μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.511782, "relative_start": 0.*****************, "end": **********.511782, "relative_end": **********.511782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.515746, "relative_start": 0.****************, "end": **********.515885, "relative_end": **********.515885, "duration": 0.00013899803161621094, "duration_str": "139μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 6194840, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "ooaaps-system.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.511777, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 11, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.010490000000000001, "accumulated_duration_str": "10.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.48134, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF' limit 1", "type": "query", "params": [], "bindings": ["nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.481484, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 13.632}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.485096, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ooaaps_system", "explain": null, "start_percent": 13.632, "width_percent": 4.48}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.486276, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 18.112, "width_percent": 3.527}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.4870622, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 21.64, "width_percent": 5.91}, {"sql": "select distinct `currency` from ((select `sell_price_currency` as `currency` from `sell_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `sell_order_service_items` where `deleted_at` is null) union all (select `buy_price_currency` as `currency` from `purchase_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `purchase_order_service_items` where `deleted_at` is null)) as `u` where `u`.`currency` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "line": 65}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 192}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 136}], "start": **********.489244, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "helpers.php:61", "source": {"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2Fhelpers.php&line=61", "ajax": false, "filename": "helpers.php", "line": "61"}, "connection": "ooaaps_system", "explain": null, "start_percent": 27.55, "width_percent": 4.862}, {"sql": "select `purchase_orders`.*, (select COALESCE(SUM(buy_price * quantity), 0) from `purchase_order_products` where `purchase_order_products`.`purchase_order_id` = `purchase_orders`.`id` and `buy_price_currency` = 'EGP' and `purchase_order_products`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `purchase_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Supplier' and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(buy_price * quantity), 0) from `purchase_order_products` where `purchase_order_products`.`purchase_order_id` = `purchase_orders`.`id` and `buy_price_currency` = 'USD' and `purchase_order_products`.`deleted_at` is null) as `price_USD`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `purchase_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Supplier' and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select price_USD - paid_USD AS remaining_USD) as `remaining_USD`, (select COALESCE(SUM(purchase_order_service_items.price), 0) from `purchase_order_services` inner join `purchase_order_service_items` on `purchase_order_services`.`id` = `purchase_order_service_items`.`purchase_order_service_id` where `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` and `purchase_order_services`.`deleted_at` is null and `purchase_order_service_items`.`deleted_at` is null and `purchase_order_service_items`.`price_currency` = 'EGP') as `service_cost_EGP`, (select COALESCE(SUM(amount), 0) from `payments` inner join `purchase_order_services` on `payments`.`payable_id` = `purchase_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_EGP`, (select service_cost_EGP - service_paid_EGP AS service_remaining_EGP) as `service_remaining_EGP`, (select COALESCE(SUM(purchase_order_service_items.price), 0) from `purchase_order_services` inner join `purchase_order_service_items` on `purchase_order_services`.`id` = `purchase_order_service_items`.`purchase_order_service_id` where `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` and `purchase_order_services`.`deleted_at` is null and `purchase_order_service_items`.`deleted_at` is null and `purchase_order_service_items`.`price_currency` = 'USD') as `service_cost_USD`, (select COALESCE(SUM(amount), 0) from `payments` inner join `purchase_order_services` on `payments`.`payable_id` = `purchase_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_USD`, (select service_cost_USD - service_paid_USD AS service_remaining_USD) as `service_remaining_USD` from `purchase_orders` where `purchase_orders`.`id` = 1 and `purchase_orders`.`company_id` in (1) and `purchase_orders`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\PurchaseOrder", "App\\Models\\Supplier", "EGP", "USD", "App\\Models\\PurchaseOrder", "App\\Models\\Supplier", "USD", "EGP", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "USD", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "line": 92}], "start": **********.4901972, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "ooaaps_system", "explain": null, "start_percent": 32.412, "width_percent": 17.827}, {"sql": "select `purchase_order_services`.*, (select COALESCE(SUM(price), 0) from `purchase_order_service_items` where `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` and `price_currency` = 'EGP' and `purchase_order_service_items`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `purchase_order_services`.`id` = `payments`.`payable_id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(price), 0) from `purchase_order_service_items` where `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` and `price_currency` = 'USD' and `purchase_order_service_items`.`deleted_at` is null) as `price_USD`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `purchase_order_services`.`id` = `payments`.`payable_id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select price_USD - paid_USD AS remaining_USD) as `remaining_USD` from `purchase_order_services` where `purchase_order_services`.`purchase_order_id` = 1 and `purchase_order_services`.`purchase_order_id` is not null and `purchase_order_services`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "USD", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 815}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 837}, {"index": 18, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/HasComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/HasComponents.php", "line": 104}], "start": **********.4955938, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 50.238, "width_percent": 10.772}, {"sql": "select `service_providers`.*, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select due_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select due_USD - paid_USD AS remaining_USD) as `remaining_USD` from `service_providers` where `service_providers`.`id` = 2 and `service_providers`.`company_id` in (1) and `service_providers`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "USD", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "USD", 2, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/PurchaseOrders/Schemas/PurchaseOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/PurchaseOrders/Schemas/PurchaseOrderForm.php", "line": 577}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 62}, {"index": 24, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 77}, {"index": 25, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "line": 188}], "start": **********.4986012, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "PurchaseOrderForm.php:577", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/PurchaseOrders/Schemas/PurchaseOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/PurchaseOrders/Schemas/PurchaseOrderForm.php", "line": 577}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FFilament%2FResources%2FPurchaseOrders%2FSchemas%2FPurchaseOrderForm.php&line=577", "ajax": false, "filename": "PurchaseOrderForm.php", "line": "577"}, "connection": "ooaaps_system", "explain": null, "start_percent": 61.01, "width_percent": 17.636}, {"sql": "select * from `payments` where ((`payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService' and `payments`.`payable_id` in (2))) and ((`payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` in (2))) and `payments`.`company_id` in (1) and `payments`.`deleted_at` is null and `company_id` = 1 order by `incremental_id` desc, `payments`.`id` asc limit 11 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\PurchaseOrderService", 2, "App\\Models\\ServiceProvider", 2, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasRecords.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasRecords.php", "line": 84}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 112}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.5068429, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:36", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=36", "ajax": false, "filename": "CanPaginateRecords.php", "line": "36"}, "connection": "ooaaps_system", "explain": null, "start_percent": 78.646, "width_percent": 8.77}, {"sql": "select count(*) as aggregate from `payments` where ((`payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService' and `payments`.`payable_id` in (2))) and ((`payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` in (2))) and `payments`.`company_id` in (1) and `payments`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["App\\Models\\PurchaseOrderService", 2, "App\\Models\\ServiceProvider", 2, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasBulkActions.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "line": 97}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 114}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 76}], "start": **********.508251, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:196", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasBulkActions.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=196", "ajax": false, "filename": "HasBulkActions.php", "line": "196"}, "connection": "ooaaps_system", "explain": null, "start_percent": 87.417, "width_percent": 6.578}, {"sql": "select `payment_methods`.`name`, `payment_methods`.`id` from `payment_methods` where `payment_methods`.`company_id` in (1) and `payment_methods`.`deleted_at` is null and `company_id` = 1 order by `payment_methods`.`name` asc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Filters/SelectFilter.php", "line": 442}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 90}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.5107632, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "SelectFilter.php:442", "source": {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Filters/SelectFilter.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FFilters%2FSelectFilter.php&line=442", "ajax": false, "filename": "SelectFilter.php", "line": "442"}, "connection": "ooaaps_system", "explain": null, "start_percent": 93.994, "width_percent": 6.006}]}, "models": {"data": {"App\\Models\\PurchaseOrderService": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FPurchaseOrderService.php&line=1", "ajax": false, "filename": "PurchaseOrderService.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\PurchaseOrder": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FPurchaseOrder.php&line=1", "ajax": false, "filename": "PurchaseOrder.php", "line": "?"}}, "App\\Models\\ServiceProvider": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FServiceProvider.php&line=1", "ajax": false, "filename": "ServiceProvider.php", "line": "?"}}, "App\\Models\\PaymentMethod": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FPaymentMethod.php&line=1", "ajax": false, "filename": "PaymentMethod.php", "line": "?"}}}, "count": 7, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 7}}, "livewire": {"data": {"app.filament.resources.purchase-orders.pages.edit-purchase-order #DspEZlEuHrWWwWMRie2d": "array:4 [\n  \"data\" => array:16 [\n    \"data\" => array:36 [\n      \"id\" => 1\n      \"supplier_id\" => 1\n      \"origin\" => \"foreign\"\n      \"incoterm\" => \"exw\"\n      \"payment_term_id\" => 1\n      \"custom_payment_term\" => null\n      \"model_of_shipping\" => \"sea\"\n      \"pickup_country_id\" => 1\n      \"pickup_city_id\" => 1\n      \"pickup_address\" => \"Test Address\"\n      \"port_of_loading_type\" => null\n      \"port_of_loading_id\" => null\n      \"delivery_country_id\" => 1\n      \"delivery_city_id\" => 1\n      \"delivery_address\" => \"asdasdasdasdasdasd\"\n      \"port_of_discharge_type\" => null\n      \"port_of_discharge_id\" => null\n      \"company_id\" => 1\n      \"incremental_id\" => 1\n      \"deleted_at\" => null\n      \"created_at\" => \"2025-08-23T12:15:23.000000Z\"\n      \"updated_at\" => \"2025-08-23T12:15:23.000000Z\"\n      \"price_EGP\" => 650\n      \"paid_EGP\" => 250\n      \"remaining_EGP\" => 400\n      \"price_USD\" => 0\n      \"paid_USD\" => 0\n      \"remaining_USD\" => 0\n      \"service_cost_EGP\" => 1200\n      \"service_paid_EGP\" => 0\n      \"service_remaining_EGP\" => 1200\n      \"service_cost_USD\" => 2000\n      \"service_paid_USD\" => 0\n      \"service_remaining_USD\" => 2000\n      \"purchaseOrderProducts\" => array:2 [\n        \"record-1\" => array:18 [\n          \"id\" => 1\n          \"purchase_order_id\" => 1\n          \"product_id\" => 1\n          \"package_type_id\" => null\n          \"quantity\" => 1\n          \"width\" => null\n          \"height\" => null\n          \"length\" => null\n          \"net_weight\" => null\n          \"gross_weight\" => null\n          \"buy_price\" => 150\n          \"buy_price_currency\" => \"EGP\"\n          \"company_id\" => 1\n          \"incremental_id\" => 1\n          \"deleted_at\" => null\n          \"created_at\" => \"2025-08-23T12:15:23.000000Z\"\n          \"updated_at\" => \"2025-08-23T12:39:19.000000Z\"\n          \"total_price\" => 150\n        ]\n        \"record-2\" => array:18 [\n          \"id\" => 2\n          \"purchase_order_id\" => 1\n          \"product_id\" => 2\n          \"package_type_id\" => null\n          \"quantity\" => 5\n          \"width\" => null\n          \"height\" => null\n          \"length\" => null\n          \"net_weight\" => null\n          \"gross_weight\" => null\n          \"buy_price\" => 100\n          \"buy_price_currency\" => \"EGP\"\n          \"company_id\" => 1\n          \"incremental_id\" => 2\n          \"deleted_at\" => null\n          \"created_at\" => \"2025-08-23T12:15:23.000000Z\"\n          \"updated_at\" => \"2025-08-23T12:39:19.000000Z\"\n          \"total_price\" => 500\n        ]\n      ]\n      \"purchaseOrderServices\" => array:2 [\n        \"record-1\" => array:17 [\n          \"id\" => 1\n          \"purchase_order_id\" => 1\n          \"service_provider_id\" => 1\n          \"service_id\" => 1\n          \"payment_term_id\" => 1\n          \"company_id\" => 1\n          \"incremental_id\" => 1\n          \"deleted_at\" => null\n          \"created_at\" => \"2025-08-23T12:15:23.000000Z\"\n          \"updated_at\" => \"2025-08-23T12:15:23.000000Z\"\n          \"price_EGP\" => 1200\n          \"paid_EGP\" => 0\n          \"remaining_EGP\" => 1200\n          \"price_USD\" => 0\n          \"paid_USD\" => 0\n          \"remaining_USD\" => 0\n          \"purchaseOrderServiceItems\" => array:2 [\n            \"record-1\" => array:10 [\n              \"id\" => 1\n              \"purchase_order_service_id\" => 1\n              \"service_item_id\" => 1\n              \"price\" => 200\n              \"price_currency\" => \"EGP\"\n              \"company_id\" => 1\n              \"incremental_id\" => 1\n              \"deleted_at\" => null\n              \"created_at\" => \"2025-08-23T12:15:23.000000Z\"\n              \"updated_at\" => \"2025-08-23T12:39:19.000000Z\"\n            ]\n            \"record-3\" => array:10 [\n              \"id\" => 3\n              \"purchase_order_service_id\" => 1\n              \"service_item_id\" => 5\n              \"price\" => 1000\n              \"price_currency\" => \"EGP\"\n              \"company_id\" => 1\n              \"incremental_id\" => 3\n              \"deleted_at\" => null\n              \"created_at\" => \"2025-08-23T12:22:07.000000Z\"\n              \"updated_at\" => \"2025-08-23T12:39:19.000000Z\"\n            ]\n          ]\n        ]\n        \"record-2\" => array:17 [\n          \"id\" => 2\n          \"purchase_order_id\" => 1\n          \"service_provider_id\" => 2\n          \"service_id\" => 2\n          \"payment_term_id\" => 1\n          \"company_id\" => 1\n          \"incremental_id\" => 2\n          \"deleted_at\" => null\n          \"created_at\" => \"2025-08-23T12:15:23.000000Z\"\n          \"updated_at\" => \"2025-08-23T12:15:23.000000Z\"\n          \"price_EGP\" => 0\n          \"paid_EGP\" => 0\n          \"remaining_EGP\" => 0\n          \"price_USD\" => 2000\n          \"paid_USD\" => 0\n          \"remaining_USD\" => 2000\n          \"purchaseOrderServiceItems\" => array:1 [\n            \"record-2\" => array:10 [\n              \"id\" => 2\n              \"purchase_order_service_id\" => 2\n              \"service_item_id\" => 2\n              \"price\" => 2000\n              \"price_currency\" => \"USD\"\n              \"company_id\" => 1\n              \"incremental_id\" => 2\n              \"deleted_at\" => null\n              \"created_at\" => \"2025-08-23T12:15:23.000000Z\"\n              \"updated_at\" => \"2025-08-23T12:39:19.000000Z\"\n            ]\n          ]\n        ]\n      ]\n    ]\n    \"previousUrl\" => \"http://ooaaps-system.test/app/1/purchase-orders\"\n    \"mountedActions\" => array:1 [\n      0 => array:4 [\n        \"name\" => \"view_payments\"\n        \"arguments\" => []\n        \"context\" => array:2 [\n          \"recordKey\" => \"1\"\n          \"schemaComponent\" => \"form.services::data::tab.purchaseOrderServices.record-2.financial-overview::data.purchaseOrderServices.record-2::section\"\n        ]\n        \"data\" => null\n      ]\n    ]\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:4 [\n      0 => \"form\"\n      1 => \"content\"\n      2 => \"headerWidgets\"\n      3 => \"footerWidgets\"\n    ]\n    \"parentRecord\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\PurchaseOrder {#2031\n      #connection: \"mysql\"\n      #table: \"purchase_orders\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:34 [\n        \"id\" => 1\n        \"supplier_id\" => 1\n        \"origin\" => \"foreign\"\n        \"incoterm\" => \"exw\"\n        \"payment_term_id\" => 1\n        \"custom_payment_term\" => null\n        \"model_of_shipping\" => \"sea\"\n        \"pickup_country_id\" => 1\n        \"pickup_city_id\" => 1\n        \"pickup_address\" => \"Test Address\"\n        \"port_of_loading_type\" => null\n        \"port_of_loading_id\" => null\n        \"delivery_country_id\" => 1\n        \"delivery_city_id\" => 1\n        \"delivery_address\" => \"asdasdasdasdasdasd\"\n        \"port_of_discharge_type\" => null\n        \"port_of_discharge_id\" => null\n        \"company_id\" => 1\n        \"incremental_id\" => 1\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:15:23\"\n        \"updated_at\" => \"2025-08-23 12:15:23\"\n        \"price_EGP\" => 650.0\n        \"paid_EGP\" => 250.0\n        \"remaining_EGP\" => 400.0\n        \"price_USD\" => 0.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 0.0\n        \"service_cost_EGP\" => 1200.0\n        \"service_paid_EGP\" => 0.0\n        \"service_remaining_EGP\" => 1200.0\n        \"service_cost_USD\" => 2000.0\n        \"service_paid_USD\" => 0.0\n        \"service_remaining_USD\" => 2000.0\n      ]\n      #original: array:34 [\n        \"id\" => 1\n        \"supplier_id\" => 1\n        \"origin\" => \"foreign\"\n        \"incoterm\" => \"exw\"\n        \"payment_term_id\" => 1\n        \"custom_payment_term\" => null\n        \"model_of_shipping\" => \"sea\"\n        \"pickup_country_id\" => 1\n        \"pickup_city_id\" => 1\n        \"pickup_address\" => \"Test Address\"\n        \"port_of_loading_type\" => null\n        \"port_of_loading_id\" => null\n        \"delivery_country_id\" => 1\n        \"delivery_city_id\" => 1\n        \"delivery_address\" => \"asdasdasdasdasdasd\"\n        \"port_of_discharge_type\" => null\n        \"port_of_discharge_id\" => null\n        \"company_id\" => 1\n        \"incremental_id\" => 1\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:15:23\"\n        \"updated_at\" => \"2025-08-23 12:15:23\"\n        \"price_EGP\" => 650.0\n        \"paid_EGP\" => 250.0\n        \"remaining_EGP\" => 400.0\n        \"price_USD\" => 0.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 0.0\n        \"service_cost_EGP\" => 1200.0\n        \"service_paid_EGP\" => 0.0\n        \"service_remaining_EGP\" => 1200.0\n        \"service_cost_USD\" => 2000.0\n        \"service_paid_USD\" => 0.0\n        \"service_remaining_USD\" => 2000.0\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:4 [\n        \"model_of_shipping\" => \"App\\Enums\\OrderModelOfShipping\"\n        \"incoterm\" => \"App\\Enums\\OrderIncoterm\"\n        \"origin\" => \"App\\Enums\\OrderOrigin\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:18 [\n        0 => \"supplier_id\"\n        1 => \"origin\"\n        2 => \"payment_term_id\"\n        3 => \"custom_payment_term\"\n        4 => \"incoterm\"\n        5 => \"model_of_shipping\"\n        6 => \"pickup_country_id\"\n        7 => \"pickup_city_id\"\n        8 => \"pickup_address\"\n        9 => \"port_of_loading_id\"\n        10 => \"port_of_loading_type\"\n        11 => \"delivery_country_id\"\n        12 => \"delivery_city_id\"\n        13 => \"delivery_address\"\n        14 => \"port_of_discharge_id\"\n        15 => \"port_of_discharge_type\"\n        16 => \"company_id\"\n        17 => \"incremental_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.purchase-orders.pages.edit-purchase-order\"\n  \"component\" => \"App\\Filament\\Resources\\PurchaseOrders\\Pages\\EditPurchaseOrder\"\n  \"id\" => \"DspEZlEuHrWWwWMRie2d\"\n]", "app.filament.widgets.payments-table #Vneu2rziwLzumI0N29NZ": "array:4 [\n  \"data\" => array:26 [\n    \"payable\" => App\\Models\\PurchaseOrderService {#3595\n      #connection: \"mysql\"\n      #table: \"purchase_order_services\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:16 [\n        \"id\" => 2\n        \"purchase_order_id\" => 1\n        \"service_provider_id\" => 2\n        \"service_id\" => 2\n        \"payment_term_id\" => 1\n        \"company_id\" => 1\n        \"incremental_id\" => 2\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:15:23\"\n        \"updated_at\" => \"2025-08-23 12:15:23\"\n        \"price_EGP\" => 0.0\n        \"paid_EGP\" => 0.0\n        \"remaining_EGP\" => 0.0\n        \"price_USD\" => 2000.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 2000.0\n      ]\n      #original: array:16 [\n        \"id\" => 2\n        \"purchase_order_id\" => 1\n        \"service_provider_id\" => 2\n        \"service_id\" => 2\n        \"payment_term_id\" => 1\n        \"company_id\" => 1\n        \"incremental_id\" => 2\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:15:23\"\n        \"updated_at\" => \"2025-08-23 12:15:23\"\n        \"price_EGP\" => 0.0\n        \"paid_EGP\" => 0.0\n        \"remaining_EGP\" => 0.0\n        \"price_USD\" => 2000.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 2000.0\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"serviceProvider\" => App\\Models\\ServiceProvider {#3798\n          #connection: \"mysql\"\n          #table: \"service_providers\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:26 [\n            \"id\" => 2\n            \"name\" => \"Freight Provider\"\n            \"email\" => \"<EMAIL>\"\n            \"dial_code\" => \"+20\"\n            \"phone\" => \"**********\"\n            \"company_name\" => \"Freight Company\"\n            \"address\" => \"Freight Address\"\n            \"bank_name\" => \"Freight Bank\"\n            \"bank_account_number\" => \"**********\"\n            \"iban_number\" => \"**********\"\n            \"swift_code\" => \"**********\"\n            \"payment_term_id\" => \"1\"\n            \"country_id\" => 1\n            \"city_id\" => 1\n            \"service_id\" => 2\n            \"company_id\" => 1\n            \"incremental_id\" => 2\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-08-23 12:10:04\"\n            \"updated_at\" => \"2025-08-23 12:10:04\"\n            \"due_EGP\" => 0.0\n            \"paid_EGP\" => 0.0\n            \"remaining_EGP\" => 0.0\n            \"due_USD\" => 2000.0\n            \"paid_USD\" => 0.0\n            \"remaining_USD\" => 2000.0\n          ]\n          #original: array:26 [\n            \"id\" => 2\n            \"name\" => \"Freight Provider\"\n            \"email\" => \"<EMAIL>\"\n            \"dial_code\" => \"+20\"\n            \"phone\" => \"**********\"\n            \"company_name\" => \"Freight Company\"\n            \"address\" => \"Freight Address\"\n            \"bank_name\" => \"Freight Bank\"\n            \"bank_account_number\" => \"**********\"\n            \"iban_number\" => \"**********\"\n            \"swift_code\" => \"**********\"\n            \"payment_term_id\" => \"1\"\n            \"country_id\" => 1\n            \"city_id\" => 1\n            \"service_id\" => 2\n            \"company_id\" => 1\n            \"incremental_id\" => 2\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-08-23 12:10:04\"\n            \"updated_at\" => \"2025-08-23 12:10:04\"\n            \"due_EGP\" => 0.0\n            \"paid_EGP\" => 0.0\n            \"remaining_EGP\" => 0.0\n            \"due_USD\" => 2000.0\n            \"paid_USD\" => 0.0\n            \"remaining_USD\" => 2000.0\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"dial_code\"\n            3 => \"phone\"\n            4 => \"company_name\"\n            5 => \"address\"\n            6 => \"service_id\"\n            7 => \"country_id\"\n            8 => \"city_id\"\n            9 => \"payment_term_id\"\n            10 => \"bank_name\"\n            11 => \"bank_account_number\"\n            12 => \"iban_number\"\n            13 => \"swift_code\"\n            14 => \"company_id\"\n            15 => \"incremental_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"purchase_order_id\"\n        1 => \"service_id\"\n        2 => \"service_provider_id\"\n        3 => \"payment_term_id\"\n        4 => \"company_id\"\n        5 => \"incremental_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"party\" => App\\Models\\ServiceProvider {#3798}\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSort\" => null\n    \"selectedTableRecords\" => []\n    \"deselectedTableRecords\" => []\n    \"isTrackingDeselectedTableRecords\" => false\n    \"tableColumns\" => array:7 [\n      0 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"incremental_id\"\n        \"label\" => \"#\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      1 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"amount\"\n        \"label\" => \"Amount\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      2 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"paymentMethod.name\"\n        \"label\" => \"Payment Method\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      3 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"due_status\"\n        \"label\" => \"Due Status\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      4 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"due_date\"\n        \"label\" => \"Due Date\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      5 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"paid_at\"\n        \"label\" => \"Paid At\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      6 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"created_at\"\n        \"label\" => \"Created At\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n    ]\n    \"tableFilters\" => array:1 [\n      \"payment_method_id\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableDeferredFilters\" => array:1 [\n      \"payment_method_id\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.widgets.payments-table\"\n  \"component\" => \"App\\Filament\\Widgets\\PaymentsTable\"\n  \"id\" => \"Vneu2rziwLzumI0N29NZ\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\PurchaseOrders\\Pages\\EditPurchaseOrder@mountAction<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=107\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=107\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:107-182</a>", "middleware": "web", "duration": "120ms", "peak_memory": "16MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-639312820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-639312820\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1342740485 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qwPDOQnCusrzvZbeYDs9RnpuZS6ZwabcMWjJhE63</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"4188 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;id&quot;:1,&quot;supplier_id&quot;:1,&quot;origin&quot;:&quot;foreign&quot;,&quot;incoterm&quot;:&quot;exw&quot;,&quot;payment_term_id&quot;:1,&quot;custom_payment_term&quot;:null,&quot;model_of_shipping&quot;:&quot;sea&quot;,&quot;pickup_country_id&quot;:1,&quot;pickup_city_id&quot;:1,&quot;pickup_address&quot;:&quot;Test Address&quot;,&quot;port_of_loading_type&quot;:null,&quot;port_of_loading_id&quot;:null,&quot;delivery_country_id&quot;:1,&quot;delivery_city_id&quot;:1,&quot;delivery_address&quot;:&quot;asdasdasdasdasdasd&quot;,&quot;port_of_discharge_type&quot;:null,&quot;port_of_discharge_id&quot;:null,&quot;company_id&quot;:1,&quot;incremental_id&quot;:1,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;price_EGP&quot;:650,&quot;paid_EGP&quot;:250,&quot;remaining_EGP&quot;:400,&quot;price_USD&quot;:0,&quot;paid_USD&quot;:0,&quot;remaining_USD&quot;:0,&quot;service_cost_EGP&quot;:1200,&quot;service_paid_EGP&quot;:0,&quot;service_remaining_EGP&quot;:1200,&quot;service_cost_USD&quot;:2000,&quot;service_paid_USD&quot;:0,&quot;service_remaining_USD&quot;:2000,&quot;purchaseOrderProducts&quot;:[{&quot;record-1&quot;:[{&quot;id&quot;:1,&quot;purchase_order_id&quot;:1,&quot;product_id&quot;:1,&quot;package_type_id&quot;:null,&quot;quantity&quot;:1,&quot;width&quot;:null,&quot;height&quot;:null,&quot;length&quot;:null,&quot;net_weight&quot;:null,&quot;gross_weight&quot;:null,&quot;buy_price&quot;:150,&quot;buy_price_currency&quot;:&quot;EGP&quot;,&quot;company_id&quot;:1,&quot;incremental_id&quot;:1,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:39:19.000000Z&quot;,&quot;total_price&quot;:150},{&quot;s&quot;:&quot;arr&quot;}],&quot;record-2&quot;:[{&quot;id&quot;:2,&quot;purchase_order_id&quot;:1,&quot;product_id&quot;:2,&quot;package_type_id&quot;:null,&quot;quantity&quot;:5,&quot;width&quot;:null,&quot;height&quot;:null,&quot;length&quot;:null,&quot;net_weight&quot;:null,&quot;gross_weight&quot;:null,&quot;buy_price&quot;:100,&quot;buy_price_currency&quot;:&quot;EGP&quot;,&quot;company_id&quot;:1,&quot;incremental_id&quot;:2,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:39:19.000000Z&quot;,&quot;total_price&quot;:500},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;purchaseOrderServices&quot;:[{&quot;record-1&quot;:[{&quot;id&quot;:1,&quot;purchase_order_id&quot;:1,&quot;service_provider_id&quot;:1,&quot;service_id&quot;:1,&quot;payment_term_id&quot;:1,&quot;company_id&quot;:1,&quot;incremental_id&quot;:1,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;price_EGP&quot;:1200,&quot;paid_EGP&quot;:0,&quot;remaining_EGP&quot;:1200,&quot;price_USD&quot;:0,&quot;paid_USD&quot;:0,&quot;remaining_USD&quot;:0,&quot;purchaseOrderServiceItems&quot;:[{&quot;record-1&quot;:[{&quot;id&quot;:1,&quot;purchase_order_service_id&quot;:1,&quot;service_item_id&quot;:1,&quot;price&quot;:200,&quot;price_currency&quot;:&quot;EGP&quot;,&quot;company_id&quot;:1,&quot;incremental_id&quot;:1,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:39:19.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;record-3&quot;:[{&quot;id&quot;:3,&quot;purchase_order_service_id&quot;:1,&quot;service_item_id&quot;:5,&quot;price&quot;:1000,&quot;price_currency&quot;:&quot;EGP&quot;,&quot;company_id&quot;:1,&quot;incremental_id&quot;:3,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:22:07.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:39:19.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;record-2&quot;:[{&quot;id&quot;:2,&quot;purchase_order_id&quot;:1,&quot;service_provider_id&quot;:2,&quot;service_id&quot;:2,&quot;payment_term_id&quot;:1,&quot;company_id&quot;:1,&quot;incremental_id&quot;:2,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;price_EGP&quot;:0,&quot;paid_EGP&quot;:0,&quot;remaining_EGP&quot;:0,&quot;price_USD&quot;:2000,&quot;paid_USD&quot;:0,&quot;remaining_USD&quot;:2000,&quot;purchaseOrderServiceItems&quot;:[{&quot;record-2&quot;:[{&quot;id&quot;:2,&quot;purchase_order_service_id&quot;:2,&quot;service_item_id&quot;:2,&quot;price&quot;:2000,&quot;price_currency&quot;:&quot;USD&quot;,&quot;company_id&quot;:1,&quot;incremental_id&quot;:2,&quot;deleted_at&quot;:null,&quot;created_at&quot;:&quot;2025-08-23T12:15:23.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-23T12:39:19.000000Z&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/ooaaps-system.test\\/app\\/1\\/purchase-orders&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;defaultActionContext&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[&quot;form&quot;,&quot;content&quot;,&quot;headerWidgets&quot;,&quot;footerWidgets&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;parentRecord&quot;:null,&quot;activeRelationManager&quot;:null,&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\PurchaseOrder&quot;,&quot;key&quot;:1,&quot;s&quot;:&quot;mdl&quot;}],&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;DspEZlEuHrWWwWMRie2d&quot;,&quot;name&quot;:&quot;app.filament.resources.purchase-orders.pages.edit-purchase-order&quot;,&quot;path&quot;:&quot;app\\/1\\/purchase-orders\\/1\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-3669932470-0&quot;:[&quot;div&quot;,&quot;sQidNBbViMMlxXSr4VdE&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;fb3fec6513e7fefbf5c6b9be7a4da4126503d61fd4b3c1fb7a0b3f8eef813f8b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">view_payments</span>\"\n            <span class=sf-dump-index>1</span> => []\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>recordKey</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>schemaComponent</span>\" => \"<span class=sf-dump-str title=\"120 characters\">form.services::data::tab.purchaseOrderServices.record-2.financial-overview::data.purchaseOrderServices.record-2::section</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342740485\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1302314289 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InJDU0NXK0VqN2k1cFE5RG9YcXhhd3c9PSIsInZhbHVlIjoiQTdEQmZoU05uY2lGMUowSEdzV3NoS0JvS0x0dVU0T0JTVTJJNzQvY2ZRc0wvQlVlUFJTTzd2TWxuMlV2c2t2RVZLeEI3V3UvUUJaN2VLMlBCa3VUU0g2TGxTKzRxRFV4OVZ6MWVkOG9VZFovZTVRRmNURUNhVWZnL0p6amFtQnYiLCJtYWMiOiJhZWYxYjgyMTUxYTFjNzUzYjFlN2YzMjlhOTE5YzNhYTY3MTYxODQzMDNiYWJjOThjNjE4MTZhZmMzYWI3OGQwIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6InQwcDJoSytSVTlLRU5xVUxhMG1NU3c9PSIsInZhbHVlIjoickJhaTJyMW00U1BzN0FDZzZxc2NPaUhJUVRQZERxMTRQTXZ2L1M3WkkzSE1FT3Jwa1FkTlh2b09adk1zdkk5UXpJaGxHY3FrMDNNaTc4QVEwQ3BqaXBUOVc2MENsNnlVWkpIRndjcXByK1FvVFR1MjdJMVUxYkhhTFUwTDRxM3MiLCJtYWMiOiJiZTkyMjM2Y2JiMDgwNzNmODk4NDFkODJmOTdlOGZjNTNmMzlhY2MwYTY5YTk1MDZhYzVjYjQ2ZmFkNWQ5YTEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"86 characters\">http://ooaaps-system.test/app/1/purchase-orders/1/edit?tab=services%3A%3Adata%3A%3Atab</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://ooaaps-system.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5024</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">ooaaps-system.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302314289\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-250044354 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qwPDOQnCusrzvZbeYDs9RnpuZS6ZwabcMWjJhE63</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250044354\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1652109690 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 23 Aug 2025 12:41:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652109690\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1324812606 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qwPDOQnCusrzvZbeYDs9RnpuZS6ZwabcMWjJhE63</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://ooaaps-system.test/app/1/purchase-orders/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$WMvnKS88yDfMHgw6j7ztaOwsmNVNlSfNI2nv7F/t4iqPnf8DCQIUu</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>edf68d0dc9e1293c77c20ae5f502b5d6_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">paymentMethod.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment Method</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">due_status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Due Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due_date</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Due Date</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">paid_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Paid At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>8095f893fa3700f01d600ed3e0d1e01d_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">model_of_shipping</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Model of Shipping</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">purchaseOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>f5b16685aa562a098eb7e35bba4c2ad6_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>3505367d0d4a7f50ee3883de72c9e822_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">invoiceable.raw_title</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Invoiceable</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">total_amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>1334379093eaeb1721e3e07ab596ab24_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>fdbcc01c2504764ebbb084261661ab3e_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Service Provider</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>4d50aaceb04f24feedb903830231d115_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">client.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">model_of_shipping</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Model of Shipping</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"30 characters\">sellOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324812606\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}
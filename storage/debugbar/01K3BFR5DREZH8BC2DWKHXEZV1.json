{"__meta": {"id": "01K3BFR5DREZH8BC2DWKHXEZV1", "datetime": "2025-08-23 12:51:41", "utime": **********.624995, "method": "GET", "uri": "/app/1/sell-orders/1/edit", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 29, "start": **********.418855, "end": **********.625, "duration": 0.2061450481414795, "duration_str": "206ms", "measures": [{"label": "Booting", "start": **********.418855, "relative_start": 0, "end": **********.459667, "relative_end": **********.459667, "duration": 0.040812015533447266, "duration_str": "40.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.459677, "relative_start": 0.*****************, "end": **********.625001, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.491595, "relative_start": 0.*****************, "end": **********.492348, "relative_end": **********.492348, "duration": 0.0007529258728027344, "duration_str": "753μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.535697, "relative_start": 0.*****************, "end": **********.535697, "relative_end": **********.535697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.537209, "relative_start": 0.*****************, "end": **********.537209, "relative_end": **********.537209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.540509, "relative_start": 0.12165403366088867, "end": **********.540509, "relative_end": **********.540509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.545886, "relative_start": 0.1270310878753662, "end": **********.545886, "relative_end": **********.545886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.546857, "relative_start": 0.12800216674804688, "end": **********.546857, "relative_end": **********.546857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.schemas.components.price-field", "start": **********.54872, "relative_start": 0.12986493110656738, "end": **********.54872, "relative_end": **********.54872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::796a364bc49048c9e0c399f0af39d88c", "start": **********.549549, "relative_start": 0.13069415092468262, "end": **********.549549, "relative_end": **********.549549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e86caa0e1677349d64b886c821fab2f8", "start": **********.550549, "relative_start": 0.13169407844543457, "end": **********.550549, "relative_end": **********.550549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::798c9c9cfe0b4a2f34a981ac883672cd", "start": **********.551016, "relative_start": 0.13216114044189453, "end": **********.551016, "relative_end": **********.551016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.551739, "relative_start": 0.13288402557373047, "end": **********.551739, "relative_end": **********.551739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.556735, "relative_start": 0.1378800868988037, "end": **********.556735, "relative_end": **********.556735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.56509, "relative_start": 0.14623498916625977, "end": **********.56509, "relative_end": **********.56509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.569676, "relative_start": 0.15082097053527832, "end": **********.569676, "relative_end": **********.569676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.570452, "relative_start": 0.1515970230102539, "end": **********.570452, "relative_end": **********.570452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.570977, "relative_start": 0.15212202072143555, "end": **********.570977, "relative_end": **********.570977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.574076, "relative_start": 0.15522098541259766, "end": **********.574076, "relative_end": **********.574076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.574858, "relative_start": 0.15600299835205078, "end": **********.574858, "relative_end": **********.574858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.576689, "relative_start": 0.15783405303955078, "end": **********.576689, "relative_end": **********.576689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.577255, "relative_start": 0.1584000587463379, "end": **********.577255, "relative_end": **********.577255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.580286, "relative_start": 0.16143107414245605, "end": **********.580286, "relative_end": **********.580286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.583368, "relative_start": 0.16451311111450195, "end": **********.583368, "relative_end": **********.583368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.59449, "relative_start": 0.17563509941101074, "end": **********.59449, "relative_end": **********.59449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.603974, "relative_start": 0.1851191520690918, "end": **********.603974, "relative_end": **********.603974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7d15c559e2b59f7fe21cb69e18a01cd7", "start": **********.612102, "relative_start": 0.19324707984924316, "end": **********.612102, "relative_end": **********.612102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.622094, "relative_start": 0.20323896408081055, "end": **********.622119, "relative_end": **********.622119, "duration": 2.5033950805664062e-05, "duration_str": "25μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.624866, "relative_start": 0.20601105690002441, "end": **********.624897, "relative_end": **********.624897, "duration": 3.0994415283203125e-05, "duration_str": "31μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 13530016, "peak_usage_str": "13MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "ooaaps-system.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 24, "nb_templates": 24, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.535689, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.537205, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.540505, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.545882, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.546853, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "filament.schemas.components.price-field", "param_count": null, "params": [], "start": **********.548716, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/resources/views/filament/schemas/components/price-field.blade.phpfilament.schemas.components.price-field", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fresources%2Fviews%2Ffilament%2Fschemas%2Fcomponents%2Fprice-field.blade.php&line=1", "ajax": false, "filename": "price-field.blade.php", "line": "?"}}, {"name": "__components::796a364bc49048c9e0c399f0af39d88c", "param_count": null, "params": [], "start": **********.549546, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/796a364bc49048c9e0c399f0af39d88c.blade.php__components::796a364bc49048c9e0c399f0af39d88c", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F796a364bc49048c9e0c399f0af39d88c.blade.php&line=1", "ajax": false, "filename": "796a364bc49048c9e0c399f0af39d88c.blade.php", "line": "?"}}, {"name": "__components::e86caa0e1677349d64b886c821fab2f8", "param_count": null, "params": [], "start": **********.550546, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/e86caa0e1677349d64b886c821fab2f8.blade.php__components::e86caa0e1677349d64b886c821fab2f8", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2Fe86caa0e1677349d64b886c821fab2f8.blade.php&line=1", "ajax": false, "filename": "e86caa0e1677349d64b886c821fab2f8.blade.php", "line": "?"}}, {"name": "__components::798c9c9cfe0b4a2f34a981ac883672cd", "param_count": null, "params": [], "start": **********.551013, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/798c9c9cfe0b4a2f34a981ac883672cd.blade.php__components::798c9c9cfe0b4a2f34a981ac883672cd", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F798c9c9cfe0b4a2f34a981ac883672cd.blade.php&line=1", "ajax": false, "filename": "798c9c9cfe0b4a2f34a981ac883672cd.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.551736, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.556732, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.565086, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.569672, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.570449, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.570974, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.574073, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.574854, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.576685, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.577251, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.580282, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.583364, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.594487, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.60397, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::7d15c559e2b59f7fe21cb69e18a01cd7", "param_count": null, "params": [], "start": **********.612099, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7d15c559e2b59f7fe21cb69e18a01cd7.blade.php__components::7d15c559e2b59f7fe21cb69e18a01cd7", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7d15c559e2b59f7fe21cb69e18a01cd7.blade.php&line=1", "ajax": false, "filename": "7d15c559e2b59f7fe21cb69e18a01cd7.blade.php", "line": "?"}}]}, "queries": {"count": 36, "nb_statements": 35, "nb_visible_statements": 36, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03325, "accumulated_duration_str": "33.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.493196, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF' limit 1", "type": "query", "params": [], "bindings": ["nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.493345, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 6.827}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.496456, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ooaaps_system", "explain": null, "start_percent": 6.827, "width_percent": 1.113}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.497611, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 7.94, "width_percent": 0.872}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.498297, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 8.812, "width_percent": 1.534}, {"sql": "select distinct `currency` from ((select `sell_price_currency` as `currency` from `sell_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `sell_order_service_items` where `deleted_at` is null) union all (select `buy_price_currency` as `currency` from `purchase_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `purchase_order_service_items` where `deleted_at` is null)) as `u` where `u`.`currency` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/EditRecord.php", "line": 82}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.5001059, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "helpers.php:61", "source": {"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2Fhelpers.php&line=61", "ajax": false, "filename": "helpers.php", "line": "61"}, "connection": "ooaaps_system", "explain": null, "start_percent": 10.346, "width_percent": 2.195}, {"sql": "select `sell_orders`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` where `sell_order_products`.`sell_order_id` = `sell_orders`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `sell_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` where `sell_order_products`.`sell_order_id` = `sell_orders`.`id` and `sell_price_currency` = 'USD' and `sell_order_products`.`deleted_at` is null) as `price_USD`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `sell_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select price_USD - paid_USD AS remaining_USD) as `remaining_USD`, (select COALESCE(SUM(sell_order_service_items.price), 0) from `sell_order_services` inner join `sell_order_service_items` on `sell_order_services`.`id` = `sell_order_service_items`.`sell_order_service_id` where `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `sell_order_services`.`deleted_at` is null and `sell_order_service_items`.`deleted_at` is null and `sell_order_service_items`.`price_currency` = 'EGP') as `service_cost_EGP`, (select COALESCE(SUM(amount), 0) from `payments` inner join `sell_order_services` on `payments`.`payable_id` = `sell_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_EGP`, (select service_cost_EGP - service_paid_EGP AS service_remaining_EGP) as `service_remaining_EGP`, (select COALESCE(SUM(sell_order_service_items.price), 0) from `sell_order_services` inner join `sell_order_service_items` on `sell_order_services`.`id` = `sell_order_service_items`.`sell_order_service_id` where `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `sell_order_services`.`deleted_at` is null and `sell_order_service_items`.`deleted_at` is null and `sell_order_service_items`.`price_currency` = 'USD') as `service_cost_USD`, (select COALESCE(SUM(amount), 0) from `payments` inner join `sell_order_services` on `payments`.`payable_id` = `sell_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_USD`, (select service_cost_USD - service_paid_USD AS service_remaining_USD) as `service_remaining_USD` from `sell_orders` where `id` = '1' and `sell_orders`.`company_id` in (1) and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "USD", "App\\Models\\SellOrder", "App\\Models\\Client", "USD", "EGP", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "USD", "1", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/EditRecord.php", "line": 82}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}], "start": **********.5013871, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "HasRoutes.php:44", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasRoutes.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource%2FConcerns%2FHasRoutes.php&line=44", "ajax": false, "filename": "HasRoutes.php", "line": "44"}, "connection": "ooaaps_system", "explain": null, "start_percent": 12.541, "width_percent": 6.316}, {"sql": "select `sell_order_products`.*, ROUND(sell_price * quantity, 2) AS total_price from `sell_order_products` where `sell_order_products`.`sell_order_id` = 1 and `sell_order_products`.`sell_order_id` is not null and `sell_order_products`.`company_id` in (1) and `sell_order_products`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1042}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 922}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 93}], "start": **********.5072, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 18.857, "width_percent": 1.534}, {"sql": "select `sell_order_services`.*, (select COALESCE(SUM(price), 0) from `sell_order_service_items` where `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` and `price_currency` = 'EGP' and `sell_order_service_items`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`id` = `payments`.`payable_id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(price), 0) from `sell_order_service_items` where `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` and `price_currency` = 'USD' and `sell_order_service_items`.`deleted_at` is null) as `price_USD`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`id` = `payments`.`payable_id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select price_USD - paid_USD AS remaining_USD) as `remaining_USD` from `sell_order_services` where `sell_order_services`.`sell_order_id` = 1 and `sell_order_services`.`sell_order_id` is not null and `sell_order_services`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "USD", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1042}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 922}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 93}], "start": **********.512103, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 20.391, "width_percent": 3.759}, {"sql": "select * from `sell_order_service_items` where `sell_order_service_items`.`sell_order_service_id` = 1 and `sell_order_service_items`.`sell_order_service_id` is not null and `sell_order_service_items`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1042}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 922}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 93}], "start": **********.514895, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 24.15, "width_percent": 1.023}, {"sql": "select `service_providers`.*, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select due_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select due_USD - paid_USD AS remaining_USD) as `remaining_USD` from `service_providers` where `service_providers`.`id` = 1 and `service_providers`.`company_id` in (1) and `service_providers`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "USD", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "USD", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "line": 581}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 62}, {"index": 24, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 77}, {"index": 25, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/EntanglesStateWithSingularRelationship.php", "line": 188}], "start": **********.517124, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "SellOrderForm.php:581", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "line": 581}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FFilament%2FResources%2FSellOrders%2FSchemas%2FSellOrderForm.php&line=581", "ajax": false, "filename": "SellOrderForm.php", "line": "581"}, "connection": "ooaaps_system", "explain": null, "start_percent": 25.173, "width_percent": 7.398}, {"sql": "select `clients`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_EGP`, (select due_EGP - collected_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'USD' and `sell_order_products`.`deleted_at` is null) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_USD`, (select due_USD - collected_USD AS remaining_USD) as `remaining_USD` from `clients` where `clients`.`id` = 1 and `clients`.`company_id` in (1) and `clients`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "USD", "App\\Models\\SellOrder", "App\\Models\\Client", "USD", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "line": 632}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 62}, {"index": 24, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 77}, {"index": 25, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 170}], "start": **********.520458, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "SellOrderForm.php:632", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "line": 632}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FFilament%2FResources%2FSellOrders%2FSchemas%2FSellOrderForm.php&line=632", "ajax": false, "filename": "SellOrderForm.php", "line": "632"}, "connection": "ooaaps_system", "explain": null, "start_percent": 32.571, "width_percent": 3.038}, {"sql": "select * from `sell_order_service_items` where `sell_order_service_items`.`sell_order_service_id` = 1 and `sell_order_service_items`.`sell_order_service_id` is not null and `sell_order_service_items`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 815}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 837}, {"index": 18, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasState.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasState.php", "line": 468}], "start": **********.524738, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 35.609, "width_percent": 1.955}, {"sql": "select `clients`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_EGP`, (select due_EGP - collected_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'USD' and `sell_order_products`.`deleted_at` is null) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_USD`, (select due_USD - collected_USD AS remaining_USD) as `remaining_USD` from `clients` where `clients`.`id` = 1 and `clients`.`company_id` in (1) and `clients`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "USD", "App\\Models\\SellOrder", "App\\Models\\Client", "USD", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.530407, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 37.564, "width_percent": 7.068}, {"sql": "select `clients`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_EGP`, (select due_EGP - collected_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'USD' and `sell_order_products`.`deleted_at` is null) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_USD`, (select due_USD - collected_USD AS remaining_USD) as `remaining_USD` from `clients` where `clients`.`company_id` in (1) and `clients`.`deleted_at` is null and `company_id` = 1 order by `clients`.`name` asc limit 50", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "USD", "App\\Models\\SellOrder", "App\\Models\\Client", "USD", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 720}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 182}], "start": **********.53339, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Select.php:882", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=882", "ajax": false, "filename": "Select.php", "line": "882"}, "connection": "ooaaps_system", "explain": null, "start_percent": 44.632, "width_percent": 5.263}, {"sql": "select * from `payment_terms` where `payment_terms`.`id` = 1 and `payment_terms`.`company_id` in (1) and `payment_terms`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.5381799, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 49.895, "width_percent": 3.248}, {"sql": "select `payment_terms`.`name`, `payment_terms`.`id` from `payment_terms` where `payment_terms`.`company_id` in (1) and `payment_terms`.`deleted_at` is null and `company_id` = 1 order by `payment_terms`.`name` asc limit 50", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 720}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 182}], "start": **********.539688, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Select.php:882", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=882", "ajax": false, "filename": "Select.php", "line": "882"}, "connection": "ooaaps_system", "explain": null, "start_percent": 53.143, "width_percent": 1.564}, {"sql": "select * from `products` where `products`.`id` = 1 and `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.543955, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 54.707, "width_percent": 1.955}, {"sql": "select `products`.`name`, `products`.`id` from `products` where `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1 order by `products`.`name` asc limit 50", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 720}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 182}], "start": **********.5450149, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Select.php:882", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=882", "ajax": false, "filename": "Select.php", "line": "882"}, "connection": "ooaaps_system", "explain": null, "start_percent": 56.662, "width_percent": 1.744}, {"sql": "select `name`, `id` from `services` where `services`.`company_id` in (1) and `services`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "line": 427}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": "view", "name": "filament-forms::components.toggle-buttons.index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/toggle-buttons/index.blade.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.555433, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "SellOrderForm.php:427", "source": {"index": 14, "namespace": null, "name": "app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/SellOrders/Schemas/SellOrderForm.php", "line": 427}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FFilament%2FResources%2FSellOrders%2FSchemas%2FSellOrderForm.php&line=427", "ajax": false, "filename": "SellOrderForm.php", "line": "427"}, "connection": "ooaaps_system", "explain": null, "start_percent": 58.406, "width_percent": 1.624}, {"sql": "select `service_providers`.*, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select due_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select due_USD - paid_USD AS remaining_USD) as `remaining_USD` from `service_providers` where `service_providers`.`id` = 1 and `service_providers`.`service_id` = 1 and `service_providers`.`company_id` in (1) and `service_providers`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "USD", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "USD", 1, 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.558779, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 60.03, "width_percent": 8}, {"sql": "select `service_providers`.*, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select due_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select due_USD - paid_USD AS remaining_USD) as `remaining_USD` from `service_providers` where `service_providers`.`service_id` = 1 and `service_providers`.`company_id` in (1) and `service_providers`.`deleted_at` is null and `company_id` = 1 order by `service_providers`.`name` asc limit 50", "type": "query", "params": [], "bindings": ["EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "USD", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "USD", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 720}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 182}], "start": **********.5624201, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "Select.php:882", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=882", "ajax": false, "filename": "Select.php", "line": "882"}, "connection": "ooaaps_system", "explain": null, "start_percent": 68.03, "width_percent": 6.286}, {"sql": "select * from `sell_order_service_items` where `sell_order_service_items`.`sell_order_service_id` = 1 and `sell_order_service_items`.`sell_order_service_id` is not null and `sell_order_service_items`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 815}, {"index": 17, "namespace": "view", "name": "filament-forms::components.repeater.table", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/repeater/table.blade.php", "line": 8}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 76}], "start": **********.566442, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 74.316, "width_percent": 1.113}, {"sql": "select * from `service_items` where `service_items`.`id` = 1 and `service_items`.`service_id` = 1 and `service_items`.`company_id` in (1) and `service_items`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.5680912, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 75.429, "width_percent": 0.872}, {"sql": "select `service_items`.`name`, `service_items`.`id` from `service_items` where `service_items`.`service_id` = 1 and `service_items`.`company_id` in (1) and `service_items`.`deleted_at` is null and `company_id` = 1 order by `service_items`.`name` asc limit 50", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 720}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 182}], "start": **********.5690491, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Select.php:882", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=882", "ajax": false, "filename": "Select.php", "line": "882"}, "connection": "ooaaps_system", "explain": null, "start_percent": 76.301, "width_percent": 1.023}, {"sql": "select * from `service_items` where `service_items`.`id` = 5 and `service_items`.`service_id` = 1 and `service_items`.`company_id` in (1) and `service_items`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": [5, 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.572143, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 77.323, "width_percent": 1.504}, {"sql": "select `service_items`.`name`, `service_items`.`id` from `service_items` where `service_items`.`service_id` = 1 and `service_items`.`company_id` in (1) and `service_items`.`deleted_at` is null and `company_id` = 1 order by `service_items`.`name` asc limit 50", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 720}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 182}], "start": **********.573346, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Select.php:882", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=882", "ajax": false, "filename": "Select.php", "line": "882"}, "connection": "ooaaps_system", "explain": null, "start_percent": 78.827, "width_percent": 1.293}, {"sql": "select * from `payment_terms` where `payment_terms`.`id` = 1 and `payment_terms`.`company_id` in (1) and `payment_terms`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.578656, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 80.12, "width_percent": 1.654}, {"sql": "select `payment_terms`.`name`, `payment_terms`.`id` from `payment_terms` where `payment_terms`.`company_id` in (1) and `payment_terms`.`deleted_at` is null and `company_id` = 1 order by `payment_terms`.`name` asc limit 50", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 720}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 182}], "start": **********.5796149, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Select.php:882", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 882}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=882", "ajax": false, "filename": "Select.php", "line": "882"}, "connection": "ooaaps_system", "explain": null, "start_percent": 81.774, "width_percent": 1.083}, {"sql": "select * from `payments` where ((`payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`payable_id` in (1))) and ((`payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` in (1))) and `payments`.`company_id` in (1) and `payments`.`deleted_at` is null and `company_id` = 1 order by `incremental_id` desc, `payments`.`id` asc limit 11 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\SellOrder", 1, "App\\Models\\Client", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasRecords.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasRecords.php", "line": 84}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 112}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.590118, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:36", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=36", "ajax": false, "filename": "CanPaginateRecords.php", "line": "36"}, "connection": "ooaaps_system", "explain": null, "start_percent": 82.857, "width_percent": 2.195}, {"sql": "select count(*) as aggregate from `payments` where ((`payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`payable_id` in (1))) and ((`payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` in (1))) and `payments`.`company_id` in (1) and `payments`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["App\\Models\\SellOrder", 1, "App\\Models\\Client", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasBulkActions.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "line": 97}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 114}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 76}], "start": **********.591452, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:196", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasBulkActions.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=196", "ajax": false, "filename": "HasBulkActions.php", "line": "196"}, "connection": "ooaaps_system", "explain": null, "start_percent": 85.053, "width_percent": 1.805}, {"sql": "select `payment_methods`.`name`, `payment_methods`.`id` from `payment_methods` where `payment_methods`.`company_id` in (1) and `payment_methods`.`deleted_at` is null and `company_id` = 1 order by `payment_methods`.`name` asc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Filters/SelectFilter.php", "line": 442}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Concerns/HasOptions.php", "line": 36}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/resources/views/components/select.blade.php", "line": 90}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.593822, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "SelectFilter.php:442", "source": {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Filters/SelectFilter.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FFilters%2FSelectFilter.php&line=442", "ajax": false, "filename": "SelectFilter.php", "line": "442"}, "connection": "ooaaps_system", "explain": null, "start_percent": 86.857, "width_percent": 1.203}, {"sql": "select count(*) as aggregate from `tasks` where `status` != 'completed' and `assignee_id` = 1 and `tasks`.`company_id` in (1) and `tasks`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["completed", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Tasks/TaskResource.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/Tasks/TaskResource.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Navigation/NavigationManager.php", "line": 180}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Navigation/NavigationManager.php", "line": 54}], "start": **********.607505, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "TaskResource.php:52", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Tasks/TaskResource.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/Tasks/TaskResource.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FFilament%2FResources%2FTasks%2FTaskResource.php&line=52", "ajax": false, "filename": "TaskResource.php", "line": "52"}, "connection": "ooaaps_system", "explain": null, "start_percent": 88.06, "width_percent": 1.865}, {"sql": "select `companies`.*, `company_user`.`user_id` as `pivot_user_id`, `company_user`.`company_id` as `pivot_company_id` from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 594}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 566}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 252}], "start": **********.6089919, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "User.php:70", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=70", "ajax": false, "filename": "User.php", "line": "70"}, "connection": "ooaaps_system", "explain": null, "start_percent": 89.925, "width_percent": 1.684}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, {"index": 20, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 206}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 96}], "start": **********.611216, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:104", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=104", "ajax": false, "filename": "DatabaseNotifications.php", "line": "104"}, "connection": "ooaaps_system", "explain": null, "start_percent": 91.609, "width_percent": 1.474}, {"sql": "update `sessions` set `payload` = '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', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF'", "type": "query", "params": [], "bindings": ["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", **********, 1, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.622308, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "ooaaps_system", "explain": null, "start_percent": 93.083, "width_percent": 6.917}]}, "models": {"data": {"App\\Models\\SellOrderServiceItem": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrderServiceItem.php&line=1", "ajax": false, "filename": "SellOrderServiceItem.php", "line": "?"}}, "App\\Models\\Service": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FService.php&line=1", "ajax": false, "filename": "Service.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\ServiceProvider": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FServiceProvider.php&line=1", "ajax": false, "filename": "ServiceProvider.php", "line": "?"}}, "App\\Models\\Client": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\PaymentTerm": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FPaymentTerm.php&line=1", "ajax": false, "filename": "PaymentTerm.php", "line": "?"}}, "App\\Models\\ServiceItem": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FServiceItem.php&line=1", "ajax": false, "filename": "ServiceItem.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\SellOrder": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrder.php&line=1", "ajax": false, "filename": "SellOrder.php", "line": "?"}}, "App\\Models\\SellOrderProduct": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrderProduct.php&line=1", "ajax": false, "filename": "SellOrderProduct.php", "line": "?"}}, "App\\Models\\SellOrderService": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrderService.php&line=1", "ajax": false, "filename": "SellOrderService.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\PaymentMethod": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FPaymentMethod.php&line=1", "ajax": false, "filename": "PaymentMethod.php", "line": "?"}}}, "count": 25, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 25}}, "livewire": {"data": {"app.filament.resources.sell-orders.pages.edit-sell-order #OA1I7A2QQ6NeRKWFxz8D": "array:4 [\n  \"data\" => array:16 [\n    \"data\" => array:36 [\n      \"id\" => 1\n      \"client_id\" => 1\n      \"origin\" => \"local\"\n      \"incoterm\" => null\n      \"payment_term_id\" => 1\n      \"custom_payment_term\" => null\n      \"model_of_shipping\" => null\n      \"pickup_country_id\" => null\n      \"pickup_city_id\" => null\n      \"pickup_address\" => null\n      \"port_of_loading_type\" => null\n      \"port_of_loading_id\" => null\n      \"delivery_country_id\" => null\n      \"delivery_city_id\" => null\n      \"delivery_address\" => null\n      \"port_of_discharge_type\" => null\n      \"port_of_discharge_id\" => null\n      \"company_id\" => 1\n      \"incremental_id\" => 1\n      \"deleted_at\" => null\n      \"created_at\" => \"2025-08-23T12:34:59.000000Z\"\n      \"updated_at\" => \"2025-08-23T12:34:59.000000Z\"\n      \"price_EGP\" => 3000.0\n      \"paid_EGP\" => 0.0\n      \"remaining_EGP\" => 3000.0\n      \"price_USD\" => 0.0\n      \"paid_USD\" => 0.0\n      \"remaining_USD\" => 0.0\n      \"service_cost_EGP\" => 4000.0\n      \"service_paid_EGP\" => 0.0\n      \"service_remaining_EGP\" => 4000.0\n      \"service_cost_USD\" => 0.0\n      \"service_paid_USD\" => 0.0\n      \"service_remaining_USD\" => 0.0\n      \"sellOrderProducts\" => array:1 [\n        \"record-1\" => array:18 [\n          \"id\" => 1\n          \"sell_order_id\" => 1\n          \"product_id\" => 1\n          \"package_type_id\" => null\n          \"quantity\" => 10.0\n          \"width\" => null\n          \"height\" => null\n          \"length\" => null\n          \"net_weight\" => null\n          \"gross_weight\" => null\n          \"sell_price\" => 300.0\n          \"sell_price_currency\" => \"EGP\"\n          \"company_id\" => 1\n          \"incremental_id\" => 1\n          \"deleted_at\" => null\n          \"created_at\" => \"2025-08-23T12:34:59.000000Z\"\n          \"updated_at\" => \"2025-08-23T12:39:13.000000Z\"\n          \"total_price\" => 3000.0\n        ]\n      ]\n      \"sellOrderServices\" => array:1 [\n        \"record-1\" => array:17 [\n          \"id\" => 1\n          \"sell_order_id\" => 1\n          \"service_provider_id\" => 1\n          \"service_id\" => 1\n          \"payment_term_id\" => 1\n          \"company_id\" => 1\n          \"incremental_id\" => 1\n          \"deleted_at\" => null\n          \"created_at\" => \"2025-08-23T12:34:59.000000Z\"\n          \"updated_at\" => \"2025-08-23T12:34:59.000000Z\"\n          \"price_EGP\" => 4000.0\n          \"paid_EGP\" => 0.0\n          \"remaining_EGP\" => 4000.0\n          \"price_USD\" => 0.0\n          \"paid_USD\" => 0.0\n          \"remaining_USD\" => 0.0\n          \"sellOrderServiceItems\" => array:2 [\n            \"record-1\" => array:10 [\n              \"id\" => 1\n              \"sell_order_service_id\" => 1\n              \"service_item_id\" => 1\n              \"price\" => 1000.0\n              \"price_currency\" => \"EGP\"\n              \"company_id\" => 1\n              \"incremental_id\" => 1\n              \"deleted_at\" => null\n              \"created_at\" => \"2025-08-23T12:34:59.000000Z\"\n              \"updated_at\" => \"2025-08-23T12:39:13.000000Z\"\n            ]\n            \"record-2\" => array:10 [\n              \"id\" => 2\n              \"sell_order_service_id\" => 1\n              \"service_item_id\" => 5\n              \"price\" => 3000.0\n              \"price_currency\" => \"EGP\"\n              \"company_id\" => 1\n              \"incremental_id\" => 2\n              \"deleted_at\" => null\n              \"created_at\" => \"2025-08-23T12:35:18.000000Z\"\n              \"updated_at\" => \"2025-08-23T12:39:13.000000Z\"\n            ]\n          ]\n        ]\n      ]\n    ]\n    \"previousUrl\" => \"http://ooaaps-system.test/app/1/client-accountings\"\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:1 [\n      0 => \"form\"\n    ]\n    \"parentRecord\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\SellOrder {#1939\n      #connection: \"mysql\"\n      #table: \"sell_orders\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:34 [\n        \"id\" => 1\n        \"client_id\" => 1\n        \"origin\" => \"local\"\n        \"incoterm\" => null\n        \"payment_term_id\" => 1\n        \"custom_payment_term\" => null\n        \"model_of_shipping\" => null\n        \"pickup_country_id\" => null\n        \"pickup_city_id\" => null\n        \"pickup_address\" => null\n        \"port_of_loading_type\" => null\n        \"port_of_loading_id\" => null\n        \"delivery_country_id\" => null\n        \"delivery_city_id\" => null\n        \"delivery_address\" => null\n        \"port_of_discharge_type\" => null\n        \"port_of_discharge_id\" => null\n        \"company_id\" => 1\n        \"incremental_id\" => 1\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:34:59\"\n        \"updated_at\" => \"2025-08-23 12:34:59\"\n        \"price_EGP\" => 3000.0\n        \"paid_EGP\" => 0.0\n        \"remaining_EGP\" => 3000.0\n        \"price_USD\" => 0.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 0.0\n        \"service_cost_EGP\" => 4000.0\n        \"service_paid_EGP\" => 0.0\n        \"service_remaining_EGP\" => 4000.0\n        \"service_cost_USD\" => 0.0\n        \"service_paid_USD\" => 0.0\n        \"service_remaining_USD\" => 0.0\n      ]\n      #original: array:34 [\n        \"id\" => 1\n        \"client_id\" => 1\n        \"origin\" => \"local\"\n        \"incoterm\" => null\n        \"payment_term_id\" => 1\n        \"custom_payment_term\" => null\n        \"model_of_shipping\" => null\n        \"pickup_country_id\" => null\n        \"pickup_city_id\" => null\n        \"pickup_address\" => null\n        \"port_of_loading_type\" => null\n        \"port_of_loading_id\" => null\n        \"delivery_country_id\" => null\n        \"delivery_city_id\" => null\n        \"delivery_address\" => null\n        \"port_of_discharge_type\" => null\n        \"port_of_discharge_id\" => null\n        \"company_id\" => 1\n        \"incremental_id\" => 1\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:34:59\"\n        \"updated_at\" => \"2025-08-23 12:34:59\"\n        \"price_EGP\" => 3000.0\n        \"paid_EGP\" => 0.0\n        \"remaining_EGP\" => 3000.0\n        \"price_USD\" => 0.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 0.0\n        \"service_cost_EGP\" => 4000.0\n        \"service_paid_EGP\" => 0.0\n        \"service_remaining_EGP\" => 4000.0\n        \"service_cost_USD\" => 0.0\n        \"service_paid_USD\" => 0.0\n        \"service_remaining_USD\" => 0.0\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:4 [\n        \"model_of_shipping\" => \"App\\Enums\\OrderModelOfShipping\"\n        \"incoterm\" => \"App\\Enums\\OrderIncoterm\"\n        \"origin\" => \"App\\Enums\\OrderOrigin\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"client\" => App\\Models\\Client {#3874\n          #connection: \"mysql\"\n          #table: \"clients\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:25 [\n            \"id\" => 1\n            \"name\" => \"Test Client\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"dial_code\" => \"+20\"\n            \"company_name\" => \"Test Company\"\n            \"bank_name\" => \"Test Bank\"\n            \"bank_account_number\" => \"**********\"\n            \"iban_number\" => \"**********\"\n            \"swift_code\" => \"**********\"\n            \"country_id\" => 1\n            \"city_id\" => 1\n            \"address\" => \"Test Address\"\n            \"payment_term_id\" => 1\n            \"company_id\" => 1\n            \"incremental_id\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-08-23 12:10:04\"\n            \"updated_at\" => \"2025-08-23 12:10:04\"\n            \"due_EGP\" => 3000.0\n            \"collected_EGP\" => 0.0\n            \"remaining_EGP\" => 3000.0\n            \"due_USD\" => 0.0\n            \"collected_USD\" => 0.0\n            \"remaining_USD\" => 0.0\n          ]\n          #original: array:25 [\n            \"id\" => 1\n            \"name\" => \"Test Client\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"dial_code\" => \"+20\"\n            \"company_name\" => \"Test Company\"\n            \"bank_name\" => \"Test Bank\"\n            \"bank_account_number\" => \"**********\"\n            \"iban_number\" => \"**********\"\n            \"swift_code\" => \"**********\"\n            \"country_id\" => 1\n            \"city_id\" => 1\n            \"address\" => \"Test Address\"\n            \"payment_term_id\" => 1\n            \"company_id\" => 1\n            \"incremental_id\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-08-23 12:10:04\"\n            \"updated_at\" => \"2025-08-23 12:10:04\"\n            \"due_EGP\" => 3000.0\n            \"collected_EGP\" => 0.0\n            \"remaining_EGP\" => 3000.0\n            \"due_USD\" => 0.0\n            \"collected_USD\" => 0.0\n            \"remaining_USD\" => 0.0\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:15 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"phone\"\n            3 => \"dial_code\"\n            4 => \"company_name\"\n            5 => \"bank_name\"\n            6 => \"bank_account_number\"\n            7 => \"iban_number\"\n            8 => \"swift_code\"\n            9 => \"country_id\"\n            10 => \"city_id\"\n            11 => \"address\"\n            12 => \"payment_term_id\"\n            13 => \"company_id\"\n            14 => \"incremental_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:18 [\n        0 => \"client_id\"\n        1 => \"origin\"\n        2 => \"payment_term_id\"\n        3 => \"custom_payment_term\"\n        4 => \"incoterm\"\n        5 => \"model_of_shipping\"\n        6 => \"pickup_country_id\"\n        7 => \"pickup_city_id\"\n        8 => \"pickup_address\"\n        9 => \"port_of_loading_id\"\n        10 => \"port_of_loading_type\"\n        11 => \"delivery_country_id\"\n        12 => \"delivery_city_id\"\n        13 => \"delivery_address\"\n        14 => \"port_of_discharge_id\"\n        15 => \"port_of_discharge_type\"\n        16 => \"company_id\"\n        17 => \"incremental_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.sell-orders.pages.edit-sell-order\"\n  \"component\" => \"App\\Filament\\Resources\\SellOrders\\Pages\\EditSellOrder\"\n  \"id\" => \"OA1I7A2QQ6NeRKWFxz8D\"\n]", "app.filament.widgets.payments-table #FP6jpEhsog3ZMV3PjqWo": "array:4 [\n  \"data\" => array:26 [\n    \"payable\" => App\\Models\\SellOrder {#1939\n      #connection: \"mysql\"\n      #table: \"sell_orders\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:34 [\n        \"id\" => 1\n        \"client_id\" => 1\n        \"origin\" => \"local\"\n        \"incoterm\" => null\n        \"payment_term_id\" => 1\n        \"custom_payment_term\" => null\n        \"model_of_shipping\" => null\n        \"pickup_country_id\" => null\n        \"pickup_city_id\" => null\n        \"pickup_address\" => null\n        \"port_of_loading_type\" => null\n        \"port_of_loading_id\" => null\n        \"delivery_country_id\" => null\n        \"delivery_city_id\" => null\n        \"delivery_address\" => null\n        \"port_of_discharge_type\" => null\n        \"port_of_discharge_id\" => null\n        \"company_id\" => 1\n        \"incremental_id\" => 1\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:34:59\"\n        \"updated_at\" => \"2025-08-23 12:34:59\"\n        \"price_EGP\" => 3000.0\n        \"paid_EGP\" => 0.0\n        \"remaining_EGP\" => 3000.0\n        \"price_USD\" => 0.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 0.0\n        \"service_cost_EGP\" => 4000.0\n        \"service_paid_EGP\" => 0.0\n        \"service_remaining_EGP\" => 4000.0\n        \"service_cost_USD\" => 0.0\n        \"service_paid_USD\" => 0.0\n        \"service_remaining_USD\" => 0.0\n      ]\n      #original: array:34 [\n        \"id\" => 1\n        \"client_id\" => 1\n        \"origin\" => \"local\"\n        \"incoterm\" => null\n        \"payment_term_id\" => 1\n        \"custom_payment_term\" => null\n        \"model_of_shipping\" => null\n        \"pickup_country_id\" => null\n        \"pickup_city_id\" => null\n        \"pickup_address\" => null\n        \"port_of_loading_type\" => null\n        \"port_of_loading_id\" => null\n        \"delivery_country_id\" => null\n        \"delivery_city_id\" => null\n        \"delivery_address\" => null\n        \"port_of_discharge_type\" => null\n        \"port_of_discharge_id\" => null\n        \"company_id\" => 1\n        \"incremental_id\" => 1\n        \"deleted_at\" => null\n        \"created_at\" => \"2025-08-23 12:34:59\"\n        \"updated_at\" => \"2025-08-23 12:34:59\"\n        \"price_EGP\" => 3000.0\n        \"paid_EGP\" => 0.0\n        \"remaining_EGP\" => 3000.0\n        \"price_USD\" => 0.0\n        \"paid_USD\" => 0.0\n        \"remaining_USD\" => 0.0\n        \"service_cost_EGP\" => 4000.0\n        \"service_paid_EGP\" => 0.0\n        \"service_remaining_EGP\" => 4000.0\n        \"service_cost_USD\" => 0.0\n        \"service_paid_USD\" => 0.0\n        \"service_remaining_USD\" => 0.0\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:4 [\n        \"model_of_shipping\" => \"App\\Enums\\OrderModelOfShipping\"\n        \"incoterm\" => \"App\\Enums\\OrderIncoterm\"\n        \"origin\" => \"App\\Enums\\OrderOrigin\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"client\" => App\\Models\\Client {#3874\n          #connection: \"mysql\"\n          #table: \"clients\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:25 [\n            \"id\" => 1\n            \"name\" => \"Test Client\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"dial_code\" => \"+20\"\n            \"company_name\" => \"Test Company\"\n            \"bank_name\" => \"Test Bank\"\n            \"bank_account_number\" => \"**********\"\n            \"iban_number\" => \"**********\"\n            \"swift_code\" => \"**********\"\n            \"country_id\" => 1\n            \"city_id\" => 1\n            \"address\" => \"Test Address\"\n            \"payment_term_id\" => 1\n            \"company_id\" => 1\n            \"incremental_id\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-08-23 12:10:04\"\n            \"updated_at\" => \"2025-08-23 12:10:04\"\n            \"due_EGP\" => 3000.0\n            \"collected_EGP\" => 0.0\n            \"remaining_EGP\" => 3000.0\n            \"due_USD\" => 0.0\n            \"collected_USD\" => 0.0\n            \"remaining_USD\" => 0.0\n          ]\n          #original: array:25 [\n            \"id\" => 1\n            \"name\" => \"Test Client\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"dial_code\" => \"+20\"\n            \"company_name\" => \"Test Company\"\n            \"bank_name\" => \"Test Bank\"\n            \"bank_account_number\" => \"**********\"\n            \"iban_number\" => \"**********\"\n            \"swift_code\" => \"**********\"\n            \"country_id\" => 1\n            \"city_id\" => 1\n            \"address\" => \"Test Address\"\n            \"payment_term_id\" => 1\n            \"company_id\" => 1\n            \"incremental_id\" => 1\n            \"deleted_at\" => null\n            \"created_at\" => \"2025-08-23 12:10:04\"\n            \"updated_at\" => \"2025-08-23 12:10:04\"\n            \"due_EGP\" => 3000.0\n            \"collected_EGP\" => 0.0\n            \"remaining_EGP\" => 3000.0\n            \"due_USD\" => 0.0\n            \"collected_USD\" => 0.0\n            \"remaining_USD\" => 0.0\n          ]\n          #changes: []\n          #previous: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          #relationAutoloadCallback: null\n          #relationAutoloadContext: null\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:15 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"phone\"\n            3 => \"dial_code\"\n            4 => \"company_name\"\n            5 => \"bank_name\"\n            6 => \"bank_account_number\"\n            7 => \"iban_number\"\n            8 => \"swift_code\"\n            9 => \"country_id\"\n            10 => \"city_id\"\n            11 => \"address\"\n            12 => \"payment_term_id\"\n            13 => \"company_id\"\n            14 => \"incremental_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:18 [\n        0 => \"client_id\"\n        1 => \"origin\"\n        2 => \"payment_term_id\"\n        3 => \"custom_payment_term\"\n        4 => \"incoterm\"\n        5 => \"model_of_shipping\"\n        6 => \"pickup_country_id\"\n        7 => \"pickup_city_id\"\n        8 => \"pickup_address\"\n        9 => \"port_of_loading_id\"\n        10 => \"port_of_loading_type\"\n        11 => \"delivery_country_id\"\n        12 => \"delivery_city_id\"\n        13 => \"delivery_address\"\n        14 => \"port_of_discharge_id\"\n        15 => \"port_of_discharge_type\"\n        16 => \"company_id\"\n        17 => \"incremental_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"party\" => App\\Models\\Client {#3874}\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSort\" => null\n    \"selectedTableRecords\" => []\n    \"deselectedTableRecords\" => []\n    \"isTrackingDeselectedTableRecords\" => false\n    \"tableColumns\" => array:7 [\n      0 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"incremental_id\"\n        \"label\" => \"#\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      1 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"amount\"\n        \"label\" => \"Amount\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      2 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"paymentMethod.name\"\n        \"label\" => \"Payment Method\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      3 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"due_status\"\n        \"label\" => \"Due Status\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      4 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"due_date\"\n        \"label\" => \"Due Date\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      5 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"paid_at\"\n        \"label\" => \"Paid At\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      6 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"created_at\"\n        \"label\" => \"Created At\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n    ]\n    \"tableFilters\" => array:1 [\n      \"payment_method_id\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableDeferredFilters\" => array:1 [\n      \"payment_method_id\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.widgets.payments-table\"\n  \"component\" => \"App\\Filament\\Widgets\\PaymentsTable\"\n  \"id\" => \"FP6jpEhsog3ZMV3PjqWo\"\n]", "filament.livewire.topbar #D0QM886p9Vmaf4jxt6Vb": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.topbar\"\n  \"component\" => \"Filament\\Livewire\\Topbar\"\n  \"id\" => \"D0QM886p9Vmaf4jxt6Vb\"\n]", "filament.livewire.global-search #rk6s6QqsMYfcggUbIwrg": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"rk6s6QqsMYfcggUbIwrg\"\n]", "filament.livewire.sidebar #KKsZfvlQK8RBbjTDBJ9d": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.sidebar\"\n  \"component\" => \"Filament\\Livewire\\Sidebar\"\n  \"id\" => \"KKsZfvlQK8RBbjTDBJ9d\"\n]", "filament.livewire.notifications #EJxGMkfpXfxhOn98Ogym": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#16705\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"EJxGMkfpXfxhOn98Ogym\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://ooaaps-system.test/app/1/sell-orders/1/edit", "action_name": "filament.app.resources.sell-orders.edit", "controller_action": "App\\Filament\\Resources\\SellOrders\\Pages\\EditSellOrder", "uri": "GET app/{tenant}/sell-orders/{record}/edit", "controller": "App\\Filament\\Resources\\SellOrders\\Pages\\EditSellOrder@render<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "app/{tenant}/sell-orders", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:53-61</a>", "middleware": "panel:app, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, Filament\\Http\\Middleware\\IdentifyTenant", "duration": "207ms", "peak_memory": "18MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InhnV3lhaGYxYTVlcWhzQTJTN082c1E9PSIsInZhbHVlIjoienBMbCtmbE1uOU9ncGc1VStQT2t6U3djQlFhMG0zWXl6bXNNSHorRy9iZ1VLVzhUbWk2Z3hLSGlTZ1g0TW1oeC80YUIzbklDOUYzRjR1MTl4ZlRVdCt3WXdaUVJkTkJiZmFsSGlMZzNBZGNNZlpiN0ZqQzJiWUMwUUhkdTdkTTciLCJtYWMiOiJhMjYyNmJhNjFhMGJlMDk4Mzk5NmVlOWExMmVmZTMwOGRiOTVlMzNlNmJiYjExZDZjNzNhMDJmYTMyMjUyNmI3IiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6InVOODZXRjI1NTlxRHV1RUFxdzlzWUE9PSIsInZhbHVlIjoiaXp6bDYzQTNoZWowbzNWYmh0am5WWTd3ZE1aY0IyREJtQmM2WHJMeHJoaWk4NkYxYjZoNCtMM3Z2cDBRMzN3dXhST252RHpBWXEzUFdXZ25CU2hxSVlmWWhHUmplbEZobE95QlQwT1R0ZE90MnBicUtaOG5FbmZ0MnE4YmdwdUoiLCJtYWMiOiJmODQ1ZGVkMTcxNzNkYTQ1ZjQ1MWM1YjAxMmQzOTVjOTI1ZDI1NmM3MWQyM2JiNjY1ZmNlZmQ0ZWY1ODM4YmUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">http://ooaaps-system.test/app/1/client-accountings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">ooaaps-system.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-362809262 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qwPDOQnCusrzvZbeYDs9RnpuZS6ZwabcMWjJhE63</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362809262\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-503205993 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 23 Aug 2025 12:51:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFHR0p4c2MyZ1UwUC9FRnNla280SkE9PSIsInZhbHVlIjoiRFMrZUx3NlVQeWJlR3gzMllQblNnVURHMG1nK0ZVNldobmc3M0VyQUNUa1FFYUx1UWZ2YUFleWY4cnc2RVFNQjJvSzYxYnNyME8wc0YxaWFxOUtIcmxITmY1a005QzNjMlpSOG90UXhjb1kwVFBJdnBiRjdWZU5ub0ZTWUJLZmUiLCJtYWMiOiI1ZGVkYmY5MTgxN2I0Mjc2YmUyOGM2NDU5MThiMWI1OGY2YzAzN2U3NDdmMjUxN2MwZTVmM2I5YWEzMDUyZTJmIiwidGFnIjoiIn0%3D; expires=Sat, 23 Aug 2025 14:51:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel-session=eyJpdiI6InFpMUN0QkRjbEE3SmkwSHl4TEZGRVE9PSIsInZhbHVlIjoibmsva2duaURzTFJnaFlrSmpyTVh0TzVVWTJ5ek5BTTVYREpWbTluQzRjSzR6OC9VMjdleTA5MnFvWGtEUk5ncG5TVUlOTG56YXhrVjNLd096Q2cxZTFaRTRjSXQvVkFIc3BqcDJZL2VZUFNmODRpMnBSMkRSdEFxUnhiYmJKLzQiLCJtYWMiOiI0MjY5ODA2ZGNlMjE1M2E3MDJjODZmYjkxOTgyYjdiYjRiN2FiYjllNGZkZTExZWU5MjFjODEzM2IzOTIxNmVlIiwidGFnIjoiIn0%3D; expires=Sat, 23 Aug 2025 14:51:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFHR0p4c2MyZ1UwUC9FRnNla280SkE9PSIsInZhbHVlIjoiRFMrZUx3NlVQeWJlR3gzMllQblNnVURHMG1nK0ZVNldobmc3M0VyQUNUa1FFYUx1UWZ2YUFleWY4cnc2RVFNQjJvSzYxYnNyME8wc0YxaWFxOUtIcmxITmY1a005QzNjMlpSOG90UXhjb1kwVFBJdnBiRjdWZU5ub0ZTWUJLZmUiLCJtYWMiOiI1ZGVkYmY5MTgxN2I0Mjc2YmUyOGM2NDU5MThiMWI1OGY2YzAzN2U3NDdmMjUxN2MwZTVmM2I5YWEzMDUyZTJmIiwidGFnIjoiIn0%3D; expires=Sat, 23-Aug-2025 14:51:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel-session=eyJpdiI6InFpMUN0QkRjbEE3SmkwSHl4TEZGRVE9PSIsInZhbHVlIjoibmsva2duaURzTFJnaFlrSmpyTVh0TzVVWTJ5ek5BTTVYREpWbTluQzRjSzR6OC9VMjdleTA5MnFvWGtEUk5ncG5TVUlOTG56YXhrVjNLd096Q2cxZTFaRTRjSXQvVkFIc3BqcDJZL2VZUFNmODRpMnBSMkRSdEFxUnhiYmJKLzQiLCJtYWMiOiI0MjY5ODA2ZGNlMjE1M2E3MDJjODZmYjkxOTgyYjdiYjRiN2FiYjllNGZkZTExZWU5MjFjODEzM2IzOTIxNmVlIiwidGFnIjoiIn0%3D; expires=Sat, 23-Aug-2025 14:51:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503205993\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2034623410 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qwPDOQnCusrzvZbeYDs9RnpuZS6ZwabcMWjJhE63</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://ooaaps-system.test/app/1/sell-orders/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$WMvnKS88yDfMHgw6j7ztaOwsmNVNlSfNI2nv7F/t4iqPnf8DCQIUu</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>edf68d0dc9e1293c77c20ae5f502b5d6_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">paymentMethod.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment Method</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">due_status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Due Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due_date</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Due Date</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">paid_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Paid At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>8095f893fa3700f01d600ed3e0d1e01d_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">model_of_shipping</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Model of Shipping</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">purchaseOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>f5b16685aa562a098eb7e35bba4c2ad6_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>3505367d0d4a7f50ee3883de72c9e822_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">invoiceable.raw_title</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Invoiceable</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">total_amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>1334379093eaeb1721e3e07ab596ab24_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>fdbcc01c2504764ebbb084261661ab3e_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Service Provider</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>4d50aaceb04f24feedb903830231d115_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">client.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">model_of_shipping</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Model of Shipping</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"30 characters\">sellOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>7da9d82922959661e1edba4b2da1bec2_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">collected</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034623410\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://ooaaps-system.test/app/1/sell-orders/1/edit", "action_name": "filament.app.resources.sell-orders.edit", "controller_action": "App\\Filament\\Resources\\SellOrders\\Pages\\EditSellOrder"}, "badge": null}}
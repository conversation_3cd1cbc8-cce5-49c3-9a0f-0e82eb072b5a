{"__meta": {"id": "01K3BQXVV11ZBEKF3ZB1SDFK2N", "datetime": "2025-08-23 15:14:37", "utime": **********.02513, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.971871, "end": **********.025136, "duration": 0.05326509475708008, "duration_str": "53.27ms", "measures": [{"label": "Booting", "start": **********.971871, "relative_start": 0, "end": **********.995775, "relative_end": **********.995775, "duration": 0.023904085159301758, "duration_str": "23.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.99578, "relative_start": 0.*****************, "end": **********.025137, "relative_end": 9.5367431640625e-07, "duration": 0.029356956481933594, "duration_str": "29.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.013758, "relative_start": 0.*****************, "end": **********.013887, "relative_end": **********.013887, "duration": 0.0001289844512939453, "duration_str": "129μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.024986, "relative_start": 0.053115129470825195, "end": **********.025055, "relative_end": **********.025055, "duration": 6.890296936035156e-05, "duration_str": "69μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5063032, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "ooaaps-system.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 9, "nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0036, "accumulated_duration_str": "3.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.014389, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = '93YyHB5yCoaLSXVaKfeyXL0IuOOuo5SXaD0BZWlc' limit 1", "type": "query", "params": [], "bindings": ["93YyHB5yCoaLSXVaKfeyXL0IuOOuo5SXaD0BZWlc"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.014555, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 38.889}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.017509, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ooaaps_system", "explain": null, "start_percent": 38.889, "width_percent": 11.944}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.018656, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 50.833, "width_percent": 7.778}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.019341, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 58.611, "width_percent": 10.278}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.0207999, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 68.889, "width_percent": 6.111}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.021364, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 75, "width_percent": 8.056}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 82}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/resources/views/database-notifications.blade.php", "line": 6}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 16}], "start": **********.0226681, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:82", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=82", "ajax": false, "filename": "DatabaseNotifications.php", "line": "82"}, "connection": "ooaaps_system", "explain": null, "start_percent": 83.056, "width_percent": 9.167}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/resources/views/database-notifications.blade.php", "line": 7}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 16}], "start": **********.0233011, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:104", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=104", "ajax": false, "filename": "DatabaseNotifications.php", "line": "104"}, "connection": "ooaaps_system", "explain": null, "start_percent": 92.222, "width_percent": 7.778}]}, "models": {"data": {"App\\Models\\Company": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 3}}, "livewire": {"data": {"filament.livewire.database-notifications #KVLbSVUGlEbjkOp2I1M7": "array:4 [\n  \"data\" => array:11 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n    \"paginators\" => []\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"KVLbSVUGlEbjkOp2I1M7\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "52.15ms", "peak_memory": "6MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1332015696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1332015696\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1944589280 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CTRMUFM6mbNJLdZyh3nlZTznhVkYdcKI7Zpm7XFJ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"711 characters\">{&quot;data&quot;:{&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;defaultActionContext&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;KVLbSVUGlEbjkOp2I1M7&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;app\\/1\\/production-orders\\/1\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;9d84093bad7db8c7f509c586f9cef1cee1fa6037608e7d87cf4a2d1774ee794a&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6ImNZMmlrUE5pUUk1eDh1TFJycWliIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJhMDE0OTIzYTljNGQ5ZWNmZDA5NDU4OTk0Zjk5MTI1OWFkNDE4ZTM3Yzk1NDQ3NTM1ZGZlNTNlODA2MWY0NTdkIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944589280\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1997875788 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ill6eTk2Y0pST0doSTk0eVh1R3Z6VHc9PSIsInZhbHVlIjoiMnBnN2lRaTVIZVhwaVdrNVNOTG5PeXZFYk51RFBuUVVqVEQzVzEvc1BscEY2YW5ZalhqL09NdDEvQnk3WHpFekZwV2NkY3hpY3p2NmtLQVE3NUpqd2tyUExBYXgvT3ZxZkZJKytJWVBFSUo5cW1aaWFEUlJPbC8wYUFaSTRiVmciLCJtYWMiOiI0ODBiZmY1YWQ5YTM0NmMzMGExYzRmYjBlYzc3YjA0YTVlZDk3YTIxOTM2M2Y4ZGQ5OWNjNjUzOWZlMjJjMDY5IiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6InRhcVpzTmZiV3BYS0JDaktLczU0Y1E9PSIsInZhbHVlIjoiNDZZOVNLdlUxSEtuWU13dC9qbHF4ZXk5TzRvRmJVY3c1WU9ZR0h5dHV2YkFoU0VsdS9DZDhRUERHK0NBMmFvRHNLVS9Tc3hMaENXdzhFTWxXSVdzMG53UER5ZDNtUjZLbHJPdmdxL2IxRjJ4QVhmZ1liRmpnUi8rUGp5NXc0WTciLCJtYWMiOiI4NDBmZWZiYmM4ZDIzY2M1ZTFkYjc3YmE2ODAxYzAxODVmZmE0MjQyNTdiOTM4ZTliOTFhY2Q0NDg1NDViZWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://ooaaps-system.test/app/1/production-orders/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://ooaaps-system.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1196</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">ooaaps-system.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997875788\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-219912492 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CTRMUFM6mbNJLdZyh3nlZTznhVkYdcKI7Zpm7XFJ</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93YyHB5yCoaLSXVaKfeyXL0IuOOuo5SXaD0BZWlc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219912492\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-66918904 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 23 Aug 2025 15:14:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66918904\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1933382772 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CTRMUFM6mbNJLdZyh3nlZTznhVkYdcKI7Zpm7XFJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://ooaaps-system.test/app/1/production-orders/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$rZfpX1pJu.N5qxsbyt28a.0dUREHMCu2d9/Rvc1r813sHhBagwpjK</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>4d50aaceb04f24feedb903830231d115_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">client.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"30 characters\">sellOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>404dd4614b633fea236bf905f628be6f_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>a9995a3b61aaed0eb44a33c693a84ef6_columns</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>cbb568ab888e0499be2398899a02ad38_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">notes</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Notes</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>e998996711f23e212448cd47e210dc66_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Product</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>b7d18c38c4d3dcee1008ec99e511af63_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933382772\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}
{"__meta": {"id": "01K3BR7ZD7G84FRE6G4HKZYT8E", "datetime": "2025-08-23 15:20:08", "utime": **********.359915, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.269477, "end": **********.359921, "duration": 0.09044408798217773, "duration_str": "90.44ms", "measures": [{"label": "Booting", "start": **********.269477, "relative_start": 0, "end": **********.322687, "relative_end": **********.322687, "duration": 0.*****************, "duration_str": "53.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.322697, "relative_start": 0.*****************, "end": **********.359922, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "37.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.348865, "relative_start": 0.*****************, "end": **********.349026, "relative_end": **********.349026, "duration": 0.0001609325408935547, "duration_str": "161μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.359702, "relative_start": 0.****************, "end": **********.359801, "relative_end": **********.359801, "duration": 9.894371032714844e-05, "duration_str": "99μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5059440, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "ooaaps-system.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00321, "accumulated_duration_str": "3.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.349628, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = '93YyHB5yCoaLSXVaKfeyXL0IuOOuo5SXaD0BZWlc' limit 1", "type": "query", "params": [], "bindings": ["93YyHB5yCoaLSXVaKfeyXL0IuOOuo5SXaD0BZWlc"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.3497639, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 44.237}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.352792, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ooaaps_system", "explain": null, "start_percent": 44.237, "width_percent": 10.28}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.3538618, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 54.517, "width_percent": 7.165}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.354452, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 61.682, "width_percent": 11.838}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' order by `created_at` desc limit 51 offset 0", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 82}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/resources/views/database-notifications.blade.php", "line": 6}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 16}], "start": **********.356611, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:82", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=82", "ajax": false, "filename": "DatabaseNotifications.php", "line": "82"}, "connection": "ooaaps_system", "explain": null, "start_percent": 73.52, "width_percent": 14.953}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, {"index": 20, "namespace": "view", "name": "filament-notifications::database-notifications", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/resources/views/database-notifications.blade.php", "line": 7}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 76}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 16}], "start": **********.357499, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:104", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=104", "ajax": false, "filename": "DatabaseNotifications.php", "line": "104"}, "connection": "ooaaps_system", "explain": null, "start_percent": 88.474, "width_percent": 11.526}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "livewire": {"data": {"filament.livewire.database-notifications #Fik58Lf8z13jjAuFcip6": "array:4 [\n  \"data\" => array:11 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n    \"paginators\" => array:1 [\n      \"database-notifications-page\" => 1\n    ]\n  ]\n  \"name\" => \"filament.livewire.database-notifications\"\n  \"component\" => \"Filament\\Livewire\\DatabaseNotifications\"\n  \"id\" => \"Fik58Lf8z13jjAuFcip6\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "87.98ms", "peak_memory": "10MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-234028448 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-234028448\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2024064038 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CTRMUFM6mbNJLdZyh3nlZTznhVkYdcKI7Zpm7XFJ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"712 characters\">{&quot;data&quot;:{&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;defaultActionContext&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;database-notifications-page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;Fik58Lf8z13jjAuFcip6&quot;,&quot;name&quot;:&quot;filament.livewire.database-notifications&quot;,&quot;path&quot;:&quot;app\\/1\\/finished-products&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;5031b4c62fe129e263529f255d416865ae2827bfa9870943fdc6190d992fef9b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024064038\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1653885458 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImQza2ZobWJtdjcrZDJkVE1OYnUrMXc9PSIsInZhbHVlIjoiSlNoRDRvbFNKUzJKcHA4QUwrYW9wV0txRW1zdjBRSVRTRFlMa3RCOThPTkF1Y0JyTUFyK2h0UFRqSlc4RzlLdmxkeGFiYzljTFNlTk0xVm43dHFWUzJEUnFuV1NRNUtreDFRQWkzTFVZc1pIOEF3ZUlZUGlNV0dUWStGUllZOVEiLCJtYWMiOiI1YTkxODY5YmI3NmY4MGRiN2UxN2U3ZmY1YTgwODU5OTViNDZmOWVhMmIzZjMzZGJlZTBkOGY4YTNiY2FkNGMyIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6ImNXdFE1czBnK0pqcjh5ZTlRUnJZa3c9PSIsInZhbHVlIjoicGRhUnFTQVpjREx6RDFFd29FK0dIK2x4YzFtRDEvRDA2cmwvOGxvbXVabWFSa0xDVXk5N2tqMWc3SWpuc1FqSnBCdytQSzdMNkNnelpLUXQ3VVJjczVtREQ1TzBMYWIrcCt2QlZlMjNteXgvMU8vcWtNS0lnWW9aVmFxL1hGcEciLCJtYWMiOiJjN2ZkYzYxNmEwNmI3OGZhMDRhYmY5N2M3NGE3NGYzOWVjNGY2Zjc1NzU5OWY2ZDA2OWUzODBmMGMyMTEwMGI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://ooaaps-system.test/app/1/finished-products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://ooaaps-system.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">900</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">ooaaps-system.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653885458\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2099792048 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CTRMUFM6mbNJLdZyh3nlZTznhVkYdcKI7Zpm7XFJ</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">93YyHB5yCoaLSXVaKfeyXL0IuOOuo5SXaD0BZWlc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099792048\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-309955436 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 23 Aug 2025 15:20:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309955436\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1061093498 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CTRMUFM6mbNJLdZyh3nlZTznhVkYdcKI7Zpm7XFJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://ooaaps-system.test/app/1/finished-products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$rZfpX1pJu.N5qxsbyt28a.0dUREHMCu2d9/Rvc1r813sHhBagwpjK</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>4d50aaceb04f24feedb903830231d115_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">client.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"30 characters\">sellOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>404dd4614b633fea236bf905f628be6f_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>a9995a3b61aaed0eb44a33c693a84ef6_columns</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>cbb568ab888e0499be2398899a02ad38_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">notes</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Notes</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>e998996711f23e212448cd47e210dc66_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Product</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>b7d18c38c4d3dcee1008ec99e511af63_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>edf68d0dc9e1293c77c20ae5f502b5d6_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">paymentMethod.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment Method</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">due_status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Due Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due_date</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Due Date</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">paid_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Paid At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>3505367d0d4a7f50ee3883de72c9e822_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">invoiceable.raw_title</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Invoiceable</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">total_amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>78800d593c7827af4ba15d4e1a88aa17_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due_date</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Due Date</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">creator.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Creator</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">assignee.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Assignee</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>ba951fb6cfcb5b1bdc4f66ad21e8351a_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Content</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">budget</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Budget</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">marketingPlatform.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Marketing Platform</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>a61f5648b19fd32500a1ed86b4f20a05_columns</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>0debc738b6e3f52d9b6e58ff71783c65_columns</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>848371e3c3b899fa29dff6fba4ede76d_columns</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">leadSource.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Lead Source</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">assignee.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Assignee</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061093498\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}
{"__meta": {"id": "01K3BQ1C6F1GDQQ27JMPNVE2XM", "datetime": "2025-08-23 14:59:03", "utime": **********.503116, "method": "GET", "uri": "/app/1/inventories", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 7, "start": **********.387863, "end": **********.503121, "duration": 0.11525797843933105, "duration_str": "115ms", "measures": [{"label": "Booting", "start": **********.387863, "relative_start": 0, "end": **********.436208, "relative_end": **********.436208, "duration": 0.048345088958740234, "duration_str": "48.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.436217, "relative_start": 0.048354148864746094, "end": **********.503122, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "66.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.461942, "relative_start": 0.*****************, "end": **********.46235, "relative_end": **********.46235, "duration": 0.00040793418884277344, "duration_str": "408μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.481496, "relative_start": 0.*****************, "end": **********.481496, "relative_end": **********.481496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7d15c559e2b59f7fe21cb69e18a01cd7", "start": **********.489648, "relative_start": 0.*****************, "end": **********.489648, "relative_end": **********.489648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.500647, "relative_start": 0.11278414726257324, "end": **********.500676, "relative_end": **********.500676, "duration": 2.8848648071289062e-05, "duration_str": "29μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.502946, "relative_start": 0.11508297920227051, "end": **********.502977, "relative_end": **********.502977, "duration": 3.0994415283203125e-05, "duration_str": "31μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5055632, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "ooaaps-system.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.48149, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::7d15c559e2b59f7fe21cb69e18a01cd7", "param_count": null, "params": [], "start": **********.489644, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7d15c559e2b59f7fe21cb69e18a01cd7.blade.php__components::7d15c559e2b59f7fe21cb69e18a01cd7", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7d15c559e2b59f7fe21cb69e18a01cd7.blade.php&line=1", "ajax": false, "filename": "7d15c559e2b59f7fe21cb69e18a01cd7.blade.php", "line": "?"}}]}, "queries": {"count": 11, "nb_statements": 10, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00848, "accumulated_duration_str": "8.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.463209, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far' limit 1", "type": "query", "params": [], "bindings": ["qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.463348, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 27.712}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.4665189, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ooaaps_system", "explain": null, "start_percent": 27.712, "width_percent": 5.071}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.467802, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 32.783, "width_percent": 5.307}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.468642, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 38.09, "width_percent": 7.311}, {"sql": "select count(*) as aggregate from `products` where `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 49}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasRecords.php", "line": 174}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasRecords.php", "line": 84}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 112}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.473626, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:49", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=49", "ajax": false, "filename": "CanPaginateRecords.php", "line": "49"}, "connection": "ooaaps_system", "explain": null, "start_percent": 45.401, "width_percent": 6.486}, {"sql": "select `products`.*, (select COALESCE(SUM(CASE WHEN stock_movements.type = \"in\" THEN quantity ELSE -quantity END), 0) from `stock_movements` where `stock_movements`.`product_id` = `products`.`id` and `stock_movements`.`deleted_at` is null) as `quantity` from `products` where `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1 order by `incremental_id` desc, `products`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasRecords.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasRecords.php", "line": 84}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 112}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.4746, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:52", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=52", "ajax": false, "filename": "CanPaginateRecords.php", "line": "52"}, "connection": "ooaaps_system", "explain": null, "start_percent": 51.887, "width_percent": 10.259}, {"sql": "select count(*) as aggregate from `tasks` where `status` != 'completed' and `assignee_id` = 1 and `tasks`.`company_id` in (1) and `tasks`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["completed", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Tasks/TaskResource.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/Tasks/TaskResource.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Navigation/NavigationManager.php", "line": 180}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Navigation/NavigationManager.php", "line": 54}], "start": **********.4852061, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "TaskResource.php:52", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Tasks/TaskResource.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/Tasks/TaskResource.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FFilament%2FResources%2FTasks%2FTaskResource.php&line=52", "ajax": false, "filename": "TaskResource.php", "line": "52"}, "connection": "ooaaps_system", "explain": null, "start_percent": 62.146, "width_percent": 5.778}, {"sql": "select `companies`.*, `company_user`.`user_id` as `pivot_user_id`, `company_user`.`company_id` as `pivot_company_id` from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 594}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 566}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 252}], "start": **********.486635, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "User.php:70", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=70", "ajax": false, "filename": "User.php", "line": "70"}, "connection": "ooaaps_system", "explain": null, "start_percent": 67.925, "width_percent": 4.127}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, {"index": 20, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 206}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 96}], "start": **********.488745, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:104", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=104", "ajax": false, "filename": "DatabaseNotifications.php", "line": "104"}, "connection": "ooaaps_system", "explain": null, "start_percent": 72.052, "width_percent": 6.014}, {"sql": "update `sessions` set `payload` = '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', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far'", "type": "query", "params": [], "bindings": ["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", **********, 1, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.5008872, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "ooaaps_system", "explain": null, "start_percent": 78.066, "width_percent": 21.934}]}, "models": {"data": {"App\\Models\\Product": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 6, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 6}}, "livewire": {"data": {"app.filament.resources.inventories.pages.list-inventories #ChH1FCP5rjiDJ5VZ353f": "array:4 [\n  \"data\" => array:26 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableSearch\" => \"\"\n    \"tableSort\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n    \"parentRecord\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"selectedTableRecords\" => []\n    \"deselectedTableRecords\" => []\n    \"isTrackingDeselectedTableRecords\" => false\n    \"tableColumns\" => array:4 [\n      0 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"incremental_id\"\n        \"label\" => \"#\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      1 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"name\"\n        \"label\" => \"Name\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      2 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"type\"\n        \"label\" => \"Type\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      3 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"quantity\"\n        \"label\" => \"Quantity\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n    ]\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.inventories.pages.list-inventories\"\n  \"component\" => \"App\\Filament\\Resources\\Inventories\\Pages\\ListInventories\"\n  \"id\" => \"ChH1FCP5rjiDJ5VZ353f\"\n]", "filament.livewire.topbar #Nq8lP8ivZK26iV4LUjQ0": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.topbar\"\n  \"component\" => \"Filament\\Livewire\\Topbar\"\n  \"id\" => \"Nq8lP8ivZK26iV4LUjQ0\"\n]", "filament.livewire.global-search #DNjT4hak0FQxHGeHpJvb": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"DNjT4hak0FQxHGeHpJvb\"\n]", "filament.livewire.sidebar #7fGXSP5vyXbcXYxx9qMe": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.sidebar\"\n  \"component\" => \"Filament\\Livewire\\Sidebar\"\n  \"id\" => \"7fGXSP5vyXbcXYxx9qMe\"\n]", "filament.livewire.notifications #1jNeaqZvpNxeHWC0jbHT": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3461\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"1jNeaqZvpNxeHWC0jbHT\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://ooaaps-system.test/app/1/inventories", "action_name": "filament.app.resources.inventories.index", "controller_action": "App\\Filament\\Resources\\Inventories\\Pages\\ListInventories", "uri": "GET app/{tenant}/inventories", "controller": "App\\Filament\\Resources\\Inventories\\Pages\\ListInventories@render<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "app/{tenant}/inventories", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:53-61</a>", "middleware": "panel:app, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, Filament\\Http\\Middleware\\IdentifyTenant", "duration": "113ms", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IndWNERoTFhNSkJLUkpMNDZaYWpBUlE9PSIsInZhbHVlIjoielo3bmF6WEhreWdUSW85NU1qSUZHQjVieEc3UUZwRDEyZlJCNkhZckZOdlhqQ2xaam5LV05GN3d6ZTlBaFY5TjJLam5DWnRwcDdvcEdtcCtXa1hBTUM0WlovQURSQTAyM0ZKdW5qRFg4bWZDcy9nWDJnQ3dnTTNJaEd1N1J2a2IiLCJtYWMiOiJkNTg4ZDZiNDIwY2I3NjNiNmMyMjBjZDg3ZDJhZjM5YWMyYTc4OTExZmJkZmNhNzBhNjM5MDBjNWEzNTA4NWY1IiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IlF2N0NmVktYV0Y3Q2hqbnRTb0tBd1E9PSIsInZhbHVlIjoiZlg1WjlaQ0llR3RRaUN5ZmhmSDA2bENnZk1WTGsvQmpOUXVCeGVkYlRYdTU3dUllbU84cGZGcWhkUVVlTndmckFlWTE0RGM4d3QyRHUzSVlVSHBVNmdhV0lYYSs2dGFSRUV1cTBtWmV6elUrMFp6WURuTFd5RVdCRTVaR0MwSVUiLCJtYWMiOiJlMWZkYjVkMjM1OTExY2I5N2Y2NmQ5NzNjMzQ2NGY1YjZmOTg5ZDJhZDk5M2MzYzVlODUzOTFjOGE3MGRkNjhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://ooaaps-system.test/app/1/purchase-orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">ooaaps-system.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-764243838 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FmLPSoyJmvWe8ZBarpiBS9o9w4YtuQQpXp5tMz9d</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764243838\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1251954024 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 23 Aug 2025 14:59:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNReU10bVUxWmowOGpxcklhcUpTcFE9PSIsInZhbHVlIjoiemx2K3o1cDBERDVqa3NyMDBkaExFVG9wRHJxaWxhUlJQU3JJSzMvTXlqUHE5b3lWa1hxTTF6YWFhYzhyRk9oOGxvajhmcmIyaWJSaEYxYVpLQzFZU25yRXNVYS9XMTZiL01TekRmL2JqVTFNaXNUMUJUanNoUE9DUytzZ3YyejYiLCJtYWMiOiIzYzRkYmZmY2RlOWVlMjRlMWNkNGM1OTBhMDRiNmI0NjNhOTk4OTA1OTUwYmFlMmE3MWQwZDAyYmNiNDUzMjNmIiwidGFnIjoiIn0%3D; expires=Sat, 23 Aug 2025 16:59:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel-session=eyJpdiI6ImRId1dDQi9nM3ZZcStyUCtmVlk4SFE9PSIsInZhbHVlIjoiaDFBcTNWWDBndjIza3hETU80Ukl3a2ZnWXd5RzRxbktTR3FPZDJYZmVkdU1JblpzVkFqN21UeWMvRTVobTlpVm5OSmQrQllGL0FBUWpHWXJiSTczV2VQU0RiVVF5NlBuM0t0b1VHRUxaQlRDSkpOYjJSZWJlNHQrc0ZvbFJ5WDkiLCJtYWMiOiI3ZGU4NjhjNGUzMGE4OTM4NzhjNWNmMWE5NmY2YzU0ZTZlNjM0OGRjYmNhZDBjYjUwMzc4NzE0NTIwZDlkMWQ2IiwidGFnIjoiIn0%3D; expires=Sat, 23 Aug 2025 16:59:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNReU10bVUxWmowOGpxcklhcUpTcFE9PSIsInZhbHVlIjoiemx2K3o1cDBERDVqa3NyMDBkaExFVG9wRHJxaWxhUlJQU3JJSzMvTXlqUHE5b3lWa1hxTTF6YWFhYzhyRk9oOGxvajhmcmIyaWJSaEYxYVpLQzFZU25yRXNVYS9XMTZiL01TekRmL2JqVTFNaXNUMUJUanNoUE9DUytzZ3YyejYiLCJtYWMiOiIzYzRkYmZmY2RlOWVlMjRlMWNkNGM1OTBhMDRiNmI0NjNhOTk4OTA1OTUwYmFlMmE3MWQwZDAyYmNiNDUzMjNmIiwidGFnIjoiIn0%3D; expires=Sat, 23-Aug-2025 16:59:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel-session=eyJpdiI6ImRId1dDQi9nM3ZZcStyUCtmVlk4SFE9PSIsInZhbHVlIjoiaDFBcTNWWDBndjIza3hETU80Ukl3a2ZnWXd5RzRxbktTR3FPZDJYZmVkdU1JblpzVkFqN21UeWMvRTVobTlpVm5OSmQrQllGL0FBUWpHWXJiSTczV2VQU0RiVVF5NlBuM0t0b1VHRUxaQlRDSkpOYjJSZWJlNHQrc0ZvbFJ5WDkiLCJtYWMiOiI3ZGU4NjhjNGUzMGE4OTM4NzhjNWNmMWE5NmY2YzU0ZTZlNjM0OGRjYmNhZDBjYjUwMzc4NzE0NTIwZDlkMWQ2IiwidGFnIjoiIn0%3D; expires=Sat, 23-Aug-2025 16:59:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251954024\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FmLPSoyJmvWe8ZBarpiBS9o9w4YtuQQpXp5tMz9d</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://ooaaps-system.test/app/1/inventories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$UTIJpCjgSNNfL1JJzCz3xuh/fumNeNC0KZk1/6gmavaQY1DpDm1g.</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>3505367d0d4a7f50ee3883de72c9e822_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">invoiceable.raw_title</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Invoiceable</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">total_amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>cbb568ab888e0499be2398899a02ad38_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">notes</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Notes</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>edf68d0dc9e1293c77c20ae5f502b5d6_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">paymentMethod.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment Method</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">due_status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Due Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due_date</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Due Date</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">paid_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Paid At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>404dd4614b633fea236bf905f628be6f_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>b7d18c38c4d3dcee1008ec99e511af63_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>a9995a3b61aaed0eb44a33c693a84ef6_columns</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>8095f893fa3700f01d600ed3e0d1e01d_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">purchaseOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>1334379093eaeb1721e3e07ab596ab24_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>f5b16685aa562a098eb7e35bba4c2ad6_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://ooaaps-system.test/app/1/inventories", "action_name": "filament.app.resources.inventories.index", "controller_action": "App\\Filament\\Resources\\Inventories\\Pages\\ListInventories"}, "badge": null}}
{"__meta": {"id": "01K3BJ6XQP7WSYCAXNVXYSDXHH", "datetime": "2025-08-23 13:34:42", "utime": **********.4227, "method": "GET", "uri": "/app/1/service-provider-accountings", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 8, "start": **********.296327, "end": **********.422705, "duration": 0.*****************, "duration_str": "126ms", "measures": [{"label": "Booting", "start": **********.296327, "relative_start": 0, "end": **********.335992, "relative_end": **********.335992, "duration": 0.*****************, "duration_str": "39.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.336003, "relative_start": 0.*****************, "end": **********.422706, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "86.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.363399, "relative_start": 0.*****************, "end": **********.364208, "relative_end": **********.364208, "duration": 0.0008089542388916016, "duration_str": "809μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.389986, "relative_start": 0.****************, "end": **********.389986, "relative_end": **********.389986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.397964, "relative_start": 0.*****************, "end": **********.397964, "relative_end": **********.397964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7d15c559e2b59f7fe21cb69e18a01cd7", "start": **********.407519, "relative_start": 0.11119198799133301, "end": **********.407519, "relative_end": **********.407519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.420766, "relative_start": 0.12443900108337402, "end": **********.420787, "relative_end": **********.420787, "duration": 2.09808349609375e-05, "duration_str": "21μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.422575, "relative_start": 0.12624788284301758, "end": **********.422597, "relative_end": **********.422597, "duration": 2.193450927734375e-05, "duration_str": "22μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5310688, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "ooaaps-system.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.389977, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.397959, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::7d15c559e2b59f7fe21cb69e18a01cd7", "param_count": null, "params": [], "start": **********.407516, "type": "blade", "hash": "blade/Users/<USER>/development/sites/ooaaps-system/storage/framework/views/7d15c559e2b59f7fe21cb69e18a01cd7.blade.php__components::7d15c559e2b59f7fe21cb69e18a01cd7", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fstorage%2Fframework%2Fviews%2F7d15c559e2b59f7fe21cb69e18a01cd7.blade.php&line=1", "ajax": false, "filename": "7d15c559e2b59f7fe21cb69e18a01cd7.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 11, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01231, "accumulated_duration_str": "12.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.365211, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF' limit 1", "type": "query", "params": [], "bindings": ["nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.365356, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 20.877}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.3689692, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ooaaps_system", "explain": null, "start_percent": 20.877, "width_percent": 3.574}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.370351, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 24.452, "width_percent": 2.437}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.3711538, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 26.889, "width_percent": 3.493}, {"sql": "select distinct `currency` from ((select `sell_price_currency` as `currency` from `sell_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `sell_order_service_items` where `deleted_at` is null) union all (select `buy_price_currency` as `currency` from `purchase_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `purchase_order_service_items` where `deleted_at` is null)) as `u` where `u`.`currency` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 49}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasRecords.php", "line": 174}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasRecords.php", "line": 84}, {"index": 22, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 112}], "start": **********.379621, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "helpers.php:61", "source": {"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2Fhelpers.php&line=61", "ajax": false, "filename": "helpers.php", "line": "61"}, "connection": "ooaaps_system", "explain": null, "start_percent": 30.382, "width_percent": 9.342}, {"sql": "select count(*) as aggregate from `service_providers` where (`service_providers`.`deleted_at` is null) and `service_providers`.`company_id` in (1) and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 49}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasRecords.php", "line": 174}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasRecords.php", "line": 84}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 112}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.381412, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:49", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=49", "ajax": false, "filename": "CanPaginateRecords.php", "line": "49"}, "connection": "ooaaps_system", "explain": null, "start_percent": 39.724, "width_percent": 6.093}, {"sql": "select `service_providers`.*, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'EGP' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select due_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select SUM(total) from ((select COALESCE(SUM(price),0) as total from `sell_order_service_items` inner join `sell_order_services` on `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` inner join `sell_orders` on `sell_order_services`.`sell_order_id` = `sell_orders`.`id` where `sell_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `sell_order_service_items`.`deleted_at` is null and `sell_orders`.`deleted_at` is null and `sell_order_services`.`deleted_at` is null) union all (select COALESCE(SUM(price),0) as total from `purchase_order_service_items` inner join `purchase_order_services` on `purchase_order_service_items`.`purchase_order_service_id` = `purchase_order_services`.`id` inner join `purchase_orders` on `purchase_order_services`.`purchase_order_id` = `purchase_orders`.`id` where `purchase_order_services`.`service_provider_id` = `service_providers`.`id` and `price_currency` = 'USD' and `purchase_order_service_items`.`deleted_at` is null and `purchase_orders`.`deleted_at` is null and `purchase_order_services`.`deleted_at` is null)) as `t`) as `due_USD`, (select COALESCE(SUM(amount), 0) from `payments` where (`payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' or `payments`.`payable_type` = 'App\\\\Models\\\\PurchaseOrderService') and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `payments`.`party_id` = `service_providers`.`id` and `amount_currency` = 'USD' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_USD`, (select due_USD - paid_USD AS remaining_USD) as `remaining_USD` from `service_providers` where (`service_providers`.`deleted_at` is null) and `service_providers`.`company_id` in (1) and `company_id` = 1 order by `service_providers`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": ["EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "EGP", "USD", "USD", "App\\Models\\SellOrderService", "App\\Models\\PurchaseOrderService", "App\\Models\\ServiceProvider", "USD", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/HasRecords.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Table/Concerns/HasRecords.php", "line": 84}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/resources/views/index.blade.php", "line": 112}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "line": 38}], "start": **********.382868, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:52", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=52", "ajax": false, "filename": "CanPaginateRecords.php", "line": "52"}, "connection": "ooaaps_system", "explain": null, "start_percent": 45.816, "width_percent": 28.188}, {"sql": "select count(*) as aggregate from `tasks` where `status` != 'completed' and `assignee_id` = 1 and `tasks`.`company_id` in (1) and `tasks`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["completed", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/Tasks/TaskResource.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/Tasks/TaskResource.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Resource/Concerns/HasNavigation.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Navigation/NavigationManager.php", "line": 180}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Navigation/NavigationManager.php", "line": 54}], "start": **********.402119, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "TaskResource.php:52", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/Tasks/TaskResource.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Filament/Resources/Tasks/TaskResource.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FFilament%2FResources%2FTasks%2FTaskResource.php&line=52", "ajax": false, "filename": "TaskResource.php", "line": "52"}, "connection": "ooaaps_system", "explain": null, "start_percent": 74.005, "width_percent": 4.63}, {"sql": "select `companies`.*, `company_user`.`user_id` as `pivot_user_id`, `company_user`.`company_id` as `pivot_company_id` from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 594}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 566}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasRoutes.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/FilamentManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/FilamentManager.php", "line": 252}], "start": **********.4040089, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "User.php:70", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=70", "ajax": false, "filename": "User.php", "line": "70"}, "connection": "ooaaps_system", "explain": null, "start_percent": 78.635, "width_percent": 4.874}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and json_unquote(json_extract(`data`, '$.\"format\"')) = 'filament' and `read_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "filament"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, {"index": 20, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 206}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 96}], "start": **********.406387, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseNotifications.php:104", "source": {"index": 19, "namespace": null, "name": "vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/notifications/src/Livewire/DatabaseNotifications.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fnotifications%2Fsrc%2FLivewire%2FDatabaseNotifications.php&line=104", "ajax": false, "filename": "DatabaseNotifications.php", "line": "104"}, "connection": "ooaaps_system", "explain": null, "start_percent": 83.509, "width_percent": 5.199}, {"sql": "update `sessions` set `payload` = '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', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF'", "type": "query", "params": [], "bindings": ["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", **********, 1, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.42099, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "ooaaps_system", "explain": null, "start_percent": 88.708, "width_percent": 11.292}]}, "models": {"data": {"App\\Models\\Company": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\ServiceProvider": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FServiceProvider.php&line=1", "ajax": false, "filename": "ServiceProvider.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 5, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 5}}, "livewire": {"data": {"app.filament.resources.service-provider-accountings.pages.list-service-provider-accountings #aoH7MBymREhcSUHCKvvf": "array:4 [\n  \"data\" => array:26 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"trashed\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableSearch\" => \"\"\n    \"tableSort\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n    \"parentRecord\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"selectedTableRecords\" => []\n    \"deselectedTableRecords\" => []\n    \"isTrackingDeselectedTableRecords\" => false\n    \"tableColumns\" => array:6 [\n      0 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"incremental_id\"\n        \"label\" => \"#\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      1 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"name\"\n        \"label\" => \"Service Provider\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      2 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"company_name\"\n        \"label\" => \"Company Name\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      3 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"due\"\n        \"label\" => \"Due\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      4 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"paid\"\n        \"label\" => \"Paid\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n      5 => array:7 [\n        \"type\" => \"column\"\n        \"name\" => \"remaining\"\n        \"label\" => \"Remaining\"\n        \"isHidden\" => false\n        \"isToggled\" => true\n        \"isToggleable\" => false\n        \"isToggledHiddenByDefault\" => null\n      ]\n    ]\n    \"tableDeferredFilters\" => array:1 [\n      \"trashed\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.service-provider-accountings.pages.list-service-provider-accountings\"\n  \"component\" => \"App\\Filament\\Resources\\ServiceProviderAccountings\\Pages\\ListServiceProviderAccountings\"\n  \"id\" => \"aoH7MBymREhcSUHCKvvf\"\n]", "filament.livewire.topbar #IWe8C2EgV0teL5UoXfqA": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.topbar\"\n  \"component\" => \"Filament\\Livewire\\Topbar\"\n  \"id\" => \"IWe8C2EgV0teL5UoXfqA\"\n]", "filament.livewire.global-search #fiyroHrUZXFa9KVsQEXT": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"fiyroHrUZXFa9KVsQEXT\"\n]", "filament.livewire.sidebar #JRCsxeKmM2tXWlgiW8H6": "array:4 [\n  \"data\" => array:10 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionRecord\" => null\n    \"defaultTableActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.sidebar\"\n  \"component\" => \"Filament\\Livewire\\Sidebar\"\n  \"id\" => \"JRCsxeKmM2tXWlgiW8H6\"\n]", "filament.livewire.notifications #AGcdKFrggvqW7ZriopzX": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#4674\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"AGcdKFrggvqW7ZriopzX\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://ooaaps-system.test/app/1/service-provider-accountings", "action_name": "filament.app.resources.service-provider-accountings.index", "controller_action": "App\\Filament\\Resources\\ServiceProviderAccountings\\Pages\\ListServiceProviderAccountings", "uri": "GET app/{tenant}/service-provider-accountings", "controller": "App\\Filament\\Resources\\ServiceProviderAccountings\\Pages\\ListServiceProviderAccountings@render<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "app/{tenant}/service-provider-accountings", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:53-61</a>", "middleware": "panel:app, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, Filament\\Http\\Middleware\\IdentifyTenant", "duration": "124ms", "peak_memory": "8MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRubVZSZW5PKyswekt3Wk0xYmpxOEE9PSIsInZhbHVlIjoibDl2ZnFvSWVaNjU2RkloRllYNU0ydjc4UkNHd09yM3g4Nmxyc0Z3UzdKbEJmZERDSTNZbCs0dWRoTTIvWDQ4eVpaamVhVUhFdzRXY1BZVzlvY21vV05rV0lTQjFyWUdkTHRaSkNrVy9DSFdwVHROc2Y0RUxxMGxuOXFlYXVyMmEiLCJtYWMiOiIzMGIxNGU4MTljNmFmOTE1OGMyYzdjMzAyNDNlOTBhNGMxMzI3MDY2NjZiY2UyNGFmMDhhMzhjYjRmNDkwMjRiIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6ImdZeFh2L0R2ZGs4eWtkbGFEOGI2NWc9PSIsInZhbHVlIjoiSFM1NGZETnRRT3BDN3hVWldqU09zSDdNVGhYdTVsMituWG9jQ05mNmJSd0xmRHo3RDhRcmFFaGJFSXh1TjZkTFExN0I0VWpOLzlMbUlmNmFkS01Bamd6S01VTHJhNTR3cDdGOW5IMkdoKzRFOVVTM2RoaGJSNXIyc1ljTkU5SzciLCJtYWMiOiI0NmM3Mjk3ZDllNjhkMmYyNTVhODA1MTA4MDAzM2E2MzBiNjc0YWE4YjU2OTJlZjA5NzcyZTNkZWNmZWYyN2MxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"86 characters\">http://ooaaps-system.test/app/1/purchase-orders/1/edit?tab=services%3A%3Adata%3A%3Atab</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">ooaaps-system.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1559154720 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qwPDOQnCusrzvZbeYDs9RnpuZS6ZwabcMWjJhE63</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nLPM8Gnm33sMbe0WpEx43rkkAEre8LKhkFFf7UmF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559154720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-429444486 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 23 Aug 2025 13:34:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpwRE9tMmRpb2Y3VFFtMjYzUDNhZGc9PSIsInZhbHVlIjoiL25FdGp3SG5vekxra2lVUlE4dU9FWld3SjZxZFF2UVp0N0hwZlA5WldMdVVLaUF0SDdJQ2lFbndWMlNQRXRMOEtoOVNmL0xqdmhPdXY3aGFrVzdJMmZXeGh1Y2Q1TG9ac1ZKdElRMWhxenFkajlLQ3h1bFhQdVZ1UVNFQWhNZzkiLCJtYWMiOiI4ZWJlOTk2MzE3OGZkMWZmYjUxM2UxNWY0Nzk3NWM2NDAwYjhlYTUxYWJjZjMxODE4Nzk2NzRmZGU0MThmNjM0IiwidGFnIjoiIn0%3D; expires=Sat, 23 Aug 2025 15:34:42 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel-session=eyJpdiI6IkF1OGNlU3JWRWN5TEVWVWM2L1d3VlE9PSIsInZhbHVlIjoiQWdzQjFqejgrY1J4NDE0N3FOeVNjUG9tSzhTVW8zcGYveWFvTXlrR0RUS3Bmbis5YlRIampUMFVqaWJkSkdmUnlHbHFWOG9FU1FSUHI2ZVdnZ0MyN0M3RnYwYnZ4RDRRVXBBeHUwT0Y4aTE1VkNTWHJ3YmVWeXhLZER3UVVVQWYiLCJtYWMiOiI4YzA0OTZkNDg1OTU5NmFlZGY0N2E3MTBjNTFkODU2ZDk5MTlmYzAxYjM5NjM4ZTRlOTQyYTMwZGU2NDMzMTBiIiwidGFnIjoiIn0%3D; expires=Sat, 23 Aug 2025 15:34:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpwRE9tMmRpb2Y3VFFtMjYzUDNhZGc9PSIsInZhbHVlIjoiL25FdGp3SG5vekxra2lVUlE4dU9FWld3SjZxZFF2UVp0N0hwZlA5WldMdVVLaUF0SDdJQ2lFbndWMlNQRXRMOEtoOVNmL0xqdmhPdXY3aGFrVzdJMmZXeGh1Y2Q1TG9ac1ZKdElRMWhxenFkajlLQ3h1bFhQdVZ1UVNFQWhNZzkiLCJtYWMiOiI4ZWJlOTk2MzE3OGZkMWZmYjUxM2UxNWY0Nzk3NWM2NDAwYjhlYTUxYWJjZjMxODE4Nzk2NzRmZGU0MThmNjM0IiwidGFnIjoiIn0%3D; expires=Sat, 23-Aug-2025 15:34:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel-session=eyJpdiI6IkF1OGNlU3JWRWN5TEVWVWM2L1d3VlE9PSIsInZhbHVlIjoiQWdzQjFqejgrY1J4NDE0N3FOeVNjUG9tSzhTVW8zcGYveWFvTXlrR0RUS3Bmbis5YlRIampUMFVqaWJkSkdmUnlHbHFWOG9FU1FSUHI2ZVdnZ0MyN0M3RnYwYnZ4RDRRVXBBeHUwT0Y4aTE1VkNTWHJ3YmVWeXhLZER3UVVVQWYiLCJtYWMiOiI4YzA0OTZkNDg1OTU5NmFlZGY0N2E3MTBjNTFkODU2ZDk5MTlmYzAxYjM5NjM4ZTRlOTQyYTMwZGU2NDMzMTBiIiwidGFnIjoiIn0%3D; expires=Sat, 23-Aug-2025 15:34:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429444486\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qwPDOQnCusrzvZbeYDs9RnpuZS6ZwabcMWjJhE63</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">http://ooaaps-system.test/app/1/service-provider-accountings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$WMvnKS88yDfMHgw6j7ztaOwsmNVNlSfNI2nv7F/t4iqPnf8DCQIUu</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>edf68d0dc9e1293c77c20ae5f502b5d6_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">paymentMethod.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment Method</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">due_status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Due Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due_date</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Due Date</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">paid_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Paid At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>8095f893fa3700f01d600ed3e0d1e01d_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">model_of_shipping</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Model of Shipping</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">purchaseOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>f5b16685aa562a098eb7e35bba4c2ad6_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>3505367d0d4a7f50ee3883de72c9e822_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">invoiceable.raw_title</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Invoiceable</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">total_amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>1334379093eaeb1721e3e07ab596ab24_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>fdbcc01c2504764ebbb084261661ab3e_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Service Provider</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>4d50aaceb04f24feedb903830231d115_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">client.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">model_of_shipping</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Model of Shipping</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"30 characters\">sellOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>7da9d82922959661e1edba4b2da1bec2_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">collected</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://ooaaps-system.test/app/1/service-provider-accountings", "action_name": "filament.app.resources.service-provider-accountings.index", "controller_action": "App\\Filament\\Resources\\ServiceProviderAccountings\\Pages\\ListServiceProviderAccountings"}, "badge": null}}
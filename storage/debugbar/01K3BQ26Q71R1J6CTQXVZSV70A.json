{"__meta": {"id": "01K3BQ26Q71R1J6CTQXVZSV70A", "datetime": "2025-08-23 14:59:30", "utime": **********.663708, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.507599, "end": **********.663714, "duration": 0.15611481666564941, "duration_str": "156ms", "measures": [{"label": "Booting", "start": **********.507599, "relative_start": 0, "end": **********.550526, "relative_end": **********.550526, "duration": 0.042926788330078125, "duration_str": "42.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.550537, "relative_start": 0.****************, "end": **********.663715, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.578335, "relative_start": 0.*****************, "end": **********.578438, "relative_end": **********.578438, "duration": 0.000102996826171875, "duration_str": "103μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.66356, "relative_start": 0.****************, "end": **********.663624, "relative_end": **********.663624, "duration": 6.413459777832031e-05, "duration_str": "64μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8841096, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "ooaaps-system.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 55, "nb_statements": 42, "nb_visible_statements": 55, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03319000000000001, "accumulated_duration_str": "33.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.57901, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far' limit 1", "type": "query", "params": [], "bindings": ["qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.579162, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ooaaps_system", "explain": null, "start_percent": 0, "width_percent": 7.111}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.583566, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ooaaps_system", "explain": null, "start_percent": 7.111, "width_percent": 1.296}, {"sql": "select * from `companies` where `id` = '1' and `companies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.58468, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasTenancy.php:201", "source": {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Panel/Concerns/HasTenancy.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPanel%2FConcerns%2FHasTenancy.php&line=201", "ajax": false, "filename": "HasTenancy.php", "line": "201"}, "connection": "ooaaps_system", "explain": null, "start_percent": 8.406, "width_percent": 1.537}, {"sql": "select exists(select * from `companies` inner join `company_user` on `companies`.`id` = `company_user`.`company_id` where `company_user`.`user_id` = 1 and `companies`.`id` = 1 and `companies`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/IdentifyTenant.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 219}], "start": **********.585713, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "User.php:75", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/User.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=75", "ajax": false, "filename": "User.php", "line": "75"}, "connection": "ooaaps_system", "explain": null, "start_percent": 9.943, "width_percent": 1.748}, {"sql": "select distinct `currency` from ((select `sell_price_currency` as `currency` from `sell_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `sell_order_service_items` where `deleted_at` is null) union all (select `buy_price_currency` as `currency` from `purchase_order_products` where `deleted_at` is null) union all (select `price_currency` as `currency` from `purchase_order_service_items` where `deleted_at` is null)) as `u` where `u`.`currency` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}], "start": **********.59043, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "helpers.php:61", "source": {"index": 13, "namespace": null, "name": "app/helpers.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/helpers.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2Fhelpers.php&line=61", "ajax": false, "filename": "helpers.php", "line": "61"}, "connection": "ooaaps_system", "explain": null, "start_percent": 11.69, "width_percent": 2.079}, {"sql": "select `clients`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_EGP`, (select due_EGP - collected_EGP AS remaining_EGP) as `remaining_EGP` from `clients` where `clients`.`id` = '1' and `clients`.`company_id` in (1) and `clients`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "1", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.591566, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 13.769, "width_percent": 4.218}, {"sql": "select * from `payment_terms` where `payment_terms`.`id` = 1 and `payment_terms`.`company_id` in (1) and `payment_terms`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.593929, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 17.987, "width_percent": 1.386}, {"sql": "select `sell_order_products`.*, ROUND(sell_price * quantity, 2) AS total_price from `sell_order_products` where `sell_order_products`.`sell_order_id` is null and `sell_order_products`.`sell_order_id` is not null and `sell_order_products`.`company_id` in (1) and `sell_order_products`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 815}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 837}, {"index": 18, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/CanBeValidated.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/CanBeValidated.php", "line": 88}], "start": **********.5953941, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 19.373, "width_percent": 0.964}, {"sql": "select `products`.*, (select COALESCE(SUM(CASE WHEN stock_movements.type = \"in\" THEN quantity ELSE -quantity END), 0) from `stock_movements` where `stock_movements`.`product_id` = `products`.`id` and `stock_movements`.`deleted_at` is null) as `quantity` from `products` where `products`.`id` = '1' and `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.596769, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 20.337, "width_percent": 1.386}, {"sql": "select `products`.*, (select COALESCE(SUM(CASE WHEN stock_movements.type = \"in\" THEN quantity ELSE -quantity END), 0) from `stock_movements` where `stock_movements`.`product_id` = `products`.`id` and `stock_movements`.`deleted_at` is null) as `quantity` from `products` where `products`.`id` = '2' and `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["2", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.598253, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 21.723, "width_percent": 1.235}, {"sql": "select `sell_order_services`.*, (select COALESCE(SUM(price), 0) from `sell_order_service_items` where `sell_order_service_items`.`sell_order_service_id` = `sell_order_services`.`id` and `price_currency` = 'EGP' and `sell_order_service_items`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`id` = `payments`.`payable_id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP` from `sell_order_services` where `sell_order_services`.`sell_order_id` is null and `sell_order_services`.`sell_order_id` is not null and `sell_order_services`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "EGP", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 815}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 837}, {"index": 18, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/HasChildComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/CanBeValidated.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/CanBeValidated.php", "line": 88}], "start": **********.6000268, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:1152", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=1152", "ajax": false, "filename": "Repeater.php", "line": "1152"}, "connection": "ooaaps_system", "explain": null, "start_percent": 22.959, "width_percent": 1.838}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}], "start": **********.616276, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:16", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=16", "ajax": false, "filename": "BelongsToCompany.php", "line": "16"}, "connection": "ooaaps_system", "explain": null, "start_percent": 24.797, "width_percent": 0}, {"sql": "select `incremental_id` from `sell_orders` where `company_id` = 1 for update", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}], "start": **********.6163352, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:21", "source": {"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=21", "ajax": false, "filename": "BelongsToCompany.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 24.797, "width_percent": 1.326}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}], "start": **********.6172, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:26", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=26", "ajax": false, "filename": "BelongsToCompany.php", "line": "26"}, "connection": "ooaaps_system", "explain": null, "start_percent": 26.122, "width_percent": 0}, {"sql": "insert into `sell_orders` (`client_id`, `origin`, `payment_term_id`, `company_id`, `incremental_id`, `status`, `updated_at`, `created_at`) values ('1', 'local', 1, 1, 1, 'pending', '2025-08-23 14:59:30', '2025-08-23 14:59:30')", "type": "query", "params": [], "bindings": ["1", "local", 1, 1, 1, "pending", "2025-08-23 14:59:30", "2025-08-23 14:59:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 96}], "start": **********.617332, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "CreateRecord.php:202", "source": {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FCreateRecord.php&line=202", "ajax": false, "filename": "CreateRecord.php", "line": "202"}, "connection": "ooaaps_system", "explain": null, "start_percent": 26.122, "width_percent": 3.857}, {"sql": "select `clients`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` inner join `sell_orders` on `sell_order_products`.`sell_order_id` = `sell_orders`.`id` where `sell_orders`.`client_id` = `clients`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `due_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `payments`.`party_id` = `clients`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `collected_EGP`, (select due_EGP - collected_EGP AS remaining_EGP) as `remaining_EGP` from `clients` where `clients`.`id` = '1' and `clients`.`company_id` in (1) and `clients`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "1", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/SellOrder.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrder.php", "line": 359}, {"index": 22, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 14}, {"index": 29, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 30, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.619071, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "SellOrder.php:359", "source": {"index": 21, "namespace": null, "name": "app/Models/SellOrder.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrder.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrder.php&line=359", "ajax": false, "filename": "SellOrder.php", "line": "359"}, "connection": "ooaaps_system", "explain": null, "start_percent": 29.979, "width_percent": 2.983}, {"sql": "select `invoices`.*, (select COALESCE(SUM(amount * quantity), 0) from `invoice_items` where `invoice_items`.`invoice_id` = `invoices`.`id` and `amount_currency` = 'EGP' and `invoice_items`.`deleted_at` is null) as `total_amount_EGP` from `invoices` where `invoices`.`invoiceable_type` = 'App\\\\Models\\\\SellOrder' and `invoices`.`invoiceable_id` = 1 and `invoices`.`invoiceable_id` is not null and `invoices`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 29, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}], "start": **********.620584, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Invoiceable.php:17", "source": {"index": 21, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceable.php&line=17", "ajax": false, "filename": "Invoiceable.php", "line": "17"}, "connection": "ooaaps_system", "explain": null, "start_percent": 32.962, "width_percent": 2.049}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, {"index": 20, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 25}, {"index": 27, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 28, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.621736, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:16", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=16", "ajax": false, "filename": "BelongsToCompany.php", "line": "16"}, "connection": "ooaaps_system", "explain": null, "start_percent": 35.011, "width_percent": 0}, {"sql": "select `incremental_id` from `invoices` where `company_id` = 1 for update", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, {"index": 24, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 25}, {"index": 31, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 32, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.621778, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:21", "source": {"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=21", "ajax": false, "filename": "BelongsToCompany.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 35.011, "width_percent": 1.326}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, {"index": 20, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 25}, {"index": 27, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 28, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.62258, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:26", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=26", "ajax": false, "filename": "BelongsToCompany.php", "line": "26"}, "connection": "ooaaps_system", "explain": null, "start_percent": 36.336, "width_percent": 0}, {"sql": "insert into `invoices` (`party_id`, `party_type`, `invoiceable_id`, `invoiceable_type`, `incremental_id`, `company_id`, `updated_at`, `created_at`) values (1, 'App\\\\Models\\\\Client', 1, 'App\\\\Models\\\\SellOrder', 2, 1, '2025-08-23 14:59:30', '2025-08-23 14:59:30')", "type": "query", "params": [], "bindings": [1, "App\\Models\\Client", 1, "App\\Models\\SellOrder", 2, 1, "2025-08-23 14:59:30", "2025-08-23 14:59:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 25}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/Util.php", "line": 43}], "start": **********.622651, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Invoiceable.php:25", "source": {"index": 18, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceable.php&line=25", "ajax": false, "filename": "Invoiceable.php", "line": "25"}, "connection": "ooaaps_system", "explain": null, "start_percent": 36.336, "width_percent": 2.44}, {"sql": "select `sell_order_products`.*, ROUND(sell_price * quantity, 2) AS total_price from `sell_order_products` where `sell_order_products`.`sell_order_id` = 1 and `sell_order_products`.`sell_order_id` is not null and `sell_order_products`.`company_id` in (1) and `sell_order_products`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/SellOrder.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrder.php", "line": 364}, {"index": 21, "namespace": null, "name": "app/Models/Concerns/Invoiceable.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/Invoiceable.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 29, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.623813, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "SellOrder.php:364", "source": {"index": 20, "namespace": null, "name": "app/Models/SellOrder.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrder.php", "line": 364}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrder.php&line=364", "ajax": false, "filename": "SellOrder.php", "line": "364"}, "connection": "ooaaps_system", "explain": null, "start_percent": 38.777, "width_percent": 1.748}, {"sql": "select * from `model_states` where `model_states`.`model_type` = 'App\\\\Models\\\\SellOrder' and `model_states`.`model_id` = 1 and `model_states`.`model_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\SellOrder", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Concerns/HasModelStates.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/HasModelStates.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/Models/Concerns/HasModelStates.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/HasModelStates.php", "line": 20}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.624768, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasModelStates.php:46", "source": {"index": 16, "namespace": null, "name": "app/Models/Concerns/HasModelStates.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/HasModelStates.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FHasModelStates.php&line=46", "ajax": false, "filename": "HasModelStates.php", "line": "46"}, "connection": "ooaaps_system", "explain": null, "start_percent": 40.524, "width_percent": 1.627}, {"sql": "insert into `model_states` (`state_type`, `state`, `model_id`, `model_type`, `user_id`, `user_type`, `updated_at`, `created_at`) values ('status', 'pending', 1, 'App\\\\Models\\\\SellOrder', 1, 'App\\\\Models\\\\User', '2025-08-23 14:59:30', '2025-08-23 14:59:30')", "type": "query", "params": [], "bindings": ["status", "pending", 1, "App\\Models\\SellOrder", 1, "App\\Models\\User", "2025-08-23 14:59:30", "2025-08-23 14:59:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Concerns/HasModelStates.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/HasModelStates.php", "line": 36}, {"index": 19, "namespace": null, "name": "app/Models/Concerns/HasModelStates.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/HasModelStates.php", "line": 21}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 202}, {"index": 27, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/filament/src/Resources/Pages/CreateRecord.php", "line": 102}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "line": 36}], "start": **********.6256368, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasModelStates.php:36", "source": {"index": 18, "namespace": null, "name": "app/Models/Concerns/HasModelStates.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/HasModelStates.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FHasModelStates.php&line=36", "ajax": false, "filename": "HasModelStates.php", "line": "36"}, "connection": "ooaaps_system", "explain": null, "start_percent": 42.151, "width_percent": 2.29}, {"sql": "select `products`.*, (select COALESCE(SUM(CASE WHEN stock_movements.type = \"in\" THEN quantity ELSE -quantity END), 0) from `stock_movements` where `stock_movements`.`product_id` = `products`.`id` and `stock_movements`.`deleted_at` is null) as `quantity` from `products` where `products`.`id` = '1' and `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.62845, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 44.441, "width_percent": 2.772}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 21, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.633091, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:16", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=16", "ajax": false, "filename": "BelongsToCompany.php", "line": "16"}, "connection": "ooaaps_system", "explain": null, "start_percent": 47.213, "width_percent": 0}, {"sql": "select `incremental_id` from `sell_order_products` where `company_id` = 1 for update", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 25, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.633138, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:21", "source": {"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=21", "ajax": false, "filename": "BelongsToCompany.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 47.213, "width_percent": 1.296}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 21, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.633953, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:26", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=26", "ajax": false, "filename": "BelongsToCompany.php", "line": "26"}, "connection": "ooaaps_system", "explain": null, "start_percent": 48.509, "width_percent": 0}, {"sql": "insert into `sell_order_products` (`product_id`, `quantity`, `sell_price`, `sell_price_currency`, `sell_order_id`, `company_id`, `incremental_id`, `updated_at`, `created_at`) values ('1', 10, 300, 'EGP', 1, 1, 1, '2025-08-23 14:59:30', '2025-08-23 14:59:30')", "type": "query", "params": [], "bindings": ["1", 10, 300, "EGP", 1, 1, 1, "2025-08-23 14:59:30", "2025-08-23 14:59:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 47}], "start": **********.634037, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:995", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=995", "ajax": false, "filename": "Repeater.php", "line": "995"}, "connection": "ooaaps_system", "explain": null, "start_percent": 48.509, "width_percent": 2.772}, {"sql": "select * from `sell_order_products` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 27, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 28, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.6352022, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:21", "source": {"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=21", "ajax": false, "filename": "InvoiceItem.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 51.281, "width_percent": 1.446}, {"sql": "select `sell_orders`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` where `sell_order_products`.`sell_order_id` = `sell_orders`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `sell_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_order_service_items.price), 0) from `sell_order_services` inner join `sell_order_service_items` on `sell_order_services`.`id` = `sell_order_service_items`.`sell_order_service_id` where `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `sell_order_services`.`deleted_at` is null and `sell_order_service_items`.`deleted_at` is null and `sell_order_service_items`.`price_currency` = 'EGP') as `service_cost_EGP`, (select COALESCE(SUM(amount), 0) from `payments` inner join `sell_order_services` on `payments`.`payable_id` = `sell_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_EGP`, (select service_cost_EGP - service_paid_EGP AS service_remaining_EGP) as `service_remaining_EGP` from `sell_orders` where `sell_orders`.`id` = 1 and `sell_orders`.`company_id` in (1) and `sell_orders`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "EGP", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/SellOrderProduct.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrderProduct.php", "line": 61}, {"index": 22, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 30, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 31, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 32, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.636197, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "SellOrderProduct.php:61", "source": {"index": 21, "namespace": null, "name": "app/Models/SellOrderProduct.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrderProduct.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrderProduct.php&line=61", "ajax": false, "filename": "SellOrderProduct.php", "line": "61"}, "connection": "ooaaps_system", "explain": null, "start_percent": 52.727, "width_percent": 5.875}, {"sql": "select * from `sell_orders` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 27, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 28, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.638356, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:23", "source": {"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=23", "ajax": false, "filename": "InvoiceItem.php", "line": "23"}, "connection": "ooaaps_system", "explain": null, "start_percent": 58.602, "width_percent": 1.748}, {"sql": "select `invoices`.*, (select COALESCE(SUM(amount * quantity), 0) from `invoice_items` where `invoice_items`.`invoice_id` = `invoices`.`id` and `amount_currency` = 'EGP' and `invoice_items`.`deleted_at` is null) as `total_amount_EGP` from `invoices` where `invoices`.`invoiceable_type` = 'App\\\\Models\\\\SellOrder' and `invoices`.`invoiceable_id` = 1 and `invoices`.`invoiceable_id` is not null and `invoices`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 30, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 31, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 32, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.6392019, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:23", "source": {"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=23", "ajax": false, "filename": "InvoiceItem.php", "line": "23"}, "connection": "ooaaps_system", "explain": null, "start_percent": 60.35, "width_percent": 2.29}, {"sql": "select `sell_orders`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` where `sell_order_products`.`sell_order_id` = `sell_orders`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `sell_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_order_service_items.price), 0) from `sell_order_services` inner join `sell_order_service_items` on `sell_order_services`.`id` = `sell_order_service_items`.`sell_order_service_id` where `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `sell_order_services`.`deleted_at` is null and `sell_order_service_items`.`deleted_at` is null and `sell_order_service_items`.`price_currency` = 'EGP') as `service_cost_EGP`, (select COALESCE(SUM(amount), 0) from `payments` inner join `sell_order_services` on `payments`.`payable_id` = `sell_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_EGP`, (select service_cost_EGP - service_paid_EGP AS service_remaining_EGP) as `service_remaining_EGP` from `sell_orders` where `sell_orders`.`id` in (1) and `sell_orders`.`company_id` in (1) and `sell_orders`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "EGP", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 34, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 35, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 36, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 37, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.640526, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:23", "source": {"index": 26, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=23", "ajax": false, "filename": "InvoiceItem.php", "line": "23"}, "connection": "ooaaps_system", "explain": null, "start_percent": 62.639, "width_percent": 5.152}, {"sql": "select `invoice_items`.*, ROUND(amount * quantity, 2) AS total_amount from `invoice_items` where `invoice_items`.`itemable_type` = 'App\\\\Models\\\\SellOrderProduct' and `invoice_items`.`itemable_id` = 1 and `invoice_items`.`itemable_id` is not null and `invoice_items`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\SellOrderProduct", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 24}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 30, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 31, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 32, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.6426349, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:24", "source": {"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=24", "ajax": false, "filename": "InvoiceItem.php", "line": "24"}, "connection": "ooaaps_system", "explain": null, "start_percent": 67.792, "width_percent": 1.808}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, {"index": 20, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 29, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 30, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.643701, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:16", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=16", "ajax": false, "filename": "BelongsToCompany.php", "line": "16"}, "connection": "ooaaps_system", "explain": null, "start_percent": 69.599, "width_percent": 0}, {"sql": "select `incremental_id` from `invoice_items` where `company_id` = 1 for update", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, {"index": 24, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 32, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 33, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 34, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.643741, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:21", "source": {"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=21", "ajax": false, "filename": "BelongsToCompany.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 69.599, "width_percent": 1.235}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, {"index": 20, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 29, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 30, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.644506, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:26", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=26", "ajax": false, "filename": "BelongsToCompany.php", "line": "26"}, "connection": "ooaaps_system", "explain": null, "start_percent": 70.835, "width_percent": 0}, {"sql": "insert into `invoice_items` (`amount`, `amount_currency`, `quantity`, `invoice_id`, `itemable_id`, `itemable_type`, `incremental_id`, `company_id`, `updated_at`, `created_at`) values (300, 'EGP', 10, 2, 1, 'App\\\\Models\\\\SellOrderProduct', 3, 1, '2025-08-23 14:59:30', '2025-08-23 14:59:30')", "type": "query", "params": [], "bindings": [300, "EGP", 10, 2, 1, "App\\Models\\SellOrderProduct", 3, 1, "2025-08-23 14:59:30", "2025-08-23 14:59:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 29, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.644581, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:35", "source": {"index": 18, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=35", "ajax": false, "filename": "InvoiceItem.php", "line": "35"}, "connection": "ooaaps_system", "explain": null, "start_percent": 70.835, "width_percent": 2.621}, {"sql": "select `products`.*, (select COALESCE(SUM(CASE WHEN stock_movements.type = \"in\" THEN quantity ELSE -quantity END), 0) from `stock_movements` where `stock_movements`.`product_id` = `products`.`id` and `stock_movements`.`deleted_at` is null) as `quantity` from `products` where `products`.`id` = '2' and `products`.`company_id` in (1) and `products`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["2", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1290}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 970}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}], "start": **********.646247, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Select.php:1003", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Select.php", "line": 1003}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=1003", "ajax": false, "filename": "Select.php", "line": "1003"}, "connection": "ooaaps_system", "explain": null, "start_percent": 73.456, "width_percent": 2.199}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 21, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.650176, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:16", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=16", "ajax": false, "filename": "BelongsToCompany.php", "line": "16"}, "connection": "ooaaps_system", "explain": null, "start_percent": 75.655, "width_percent": 0}, {"sql": "select `incremental_id` from `sell_order_products` where `company_id` = 1 for update", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 25, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.650225, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:21", "source": {"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=21", "ajax": false, "filename": "BelongsToCompany.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 75.655, "width_percent": 1.687}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 21, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.651215, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:26", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=26", "ajax": false, "filename": "BelongsToCompany.php", "line": "26"}, "connection": "ooaaps_system", "explain": null, "start_percent": 77.343, "width_percent": 0}, {"sql": "insert into `sell_order_products` (`product_id`, `quantity`, `sell_price`, `sell_price_currency`, `sell_order_id`, `company_id`, `incremental_id`, `updated_at`, `created_at`) values ('2', 10, 200, 'EGP', 1, 1, 2, '2025-08-23 14:59:30', '2025-08-23 14:59:30')", "type": "query", "params": [], "bindings": ["2", 10, 200, "EGP", 1, 1, 2, "2025-08-23 14:59:30", "2025-08-23 14:59:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 47}], "start": **********.651294, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Repeater.php:995", "source": {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FRepeater.php&line=995", "ajax": false, "filename": "Repeater.php", "line": "995"}, "connection": "ooaaps_system", "explain": null, "start_percent": 77.343, "width_percent": 2.199}, {"sql": "select * from `sell_order_products` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 27, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 28, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.652252, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:21", "source": {"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=21", "ajax": false, "filename": "InvoiceItem.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 79.542, "width_percent": 1.235}, {"sql": "select `sell_orders`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` where `sell_order_products`.`sell_order_id` = `sell_orders`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `sell_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_order_service_items.price), 0) from `sell_order_services` inner join `sell_order_service_items` on `sell_order_services`.`id` = `sell_order_service_items`.`sell_order_service_id` where `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `sell_order_services`.`deleted_at` is null and `sell_order_service_items`.`deleted_at` is null and `sell_order_service_items`.`price_currency` = 'EGP') as `service_cost_EGP`, (select COALESCE(SUM(amount), 0) from `payments` inner join `sell_order_services` on `payments`.`payable_id` = `sell_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_EGP`, (select service_cost_EGP - service_paid_EGP AS service_remaining_EGP) as `service_remaining_EGP` from `sell_orders` where `sell_orders`.`id` = 1 and `sell_orders`.`company_id` in (1) and `sell_orders`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "EGP", 1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/SellOrderProduct.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrderProduct.php", "line": 61}, {"index": 22, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 30, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 31, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 32, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.6531339, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "SellOrderProduct.php:61", "source": {"index": 21, "namespace": null, "name": "app/Models/SellOrderProduct.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/SellOrderProduct.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrderProduct.php&line=61", "ajax": false, "filename": "SellOrderProduct.php", "line": "61"}, "connection": "ooaaps_system", "explain": null, "start_percent": 80.777, "width_percent": 5.002}, {"sql": "select * from `sell_orders` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 27, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 28, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.654977, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:23", "source": {"index": 17, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=23", "ajax": false, "filename": "InvoiceItem.php", "line": "23"}, "connection": "ooaaps_system", "explain": null, "start_percent": 85.779, "width_percent": 1.416}, {"sql": "select `invoices`.*, (select COALESCE(SUM(amount * quantity), 0) from `invoice_items` where `invoice_items`.`invoice_id` = `invoices`.`id` and `amount_currency` = 'EGP' and `invoice_items`.`deleted_at` is null) as `total_amount_EGP` from `invoices` where `invoices`.`invoiceable_type` = 'App\\\\Models\\\\SellOrder' and `invoices`.`invoiceable_id` = 1 and `invoices`.`invoiceable_id` is not null and `invoices`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 30, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 31, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 32, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.655726, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:23", "source": {"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=23", "ajax": false, "filename": "InvoiceItem.php", "line": "23"}, "connection": "ooaaps_system", "explain": null, "start_percent": 87.195, "width_percent": 2.23}, {"sql": "select `sell_orders`.*, (select COALESCE(SUM(sell_price * quantity), 0) from `sell_order_products` where `sell_order_products`.`sell_order_id` = `sell_orders`.`id` and `sell_price_currency` = 'EGP' and `sell_order_products`.`deleted_at` is null) as `price_EGP`, (select COALESCE(SUM(amount), 0) from `payments` where `payments`.`payable_id` = `sell_orders`.`id` and `payments`.`payable_type` = 'App\\\\Models\\\\SellOrder' and `payments`.`party_type` = 'App\\\\Models\\\\Client' and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `paid_EGP`, (select price_EGP - paid_EGP AS remaining_EGP) as `remaining_EGP`, (select COALESCE(SUM(sell_order_service_items.price), 0) from `sell_order_services` inner join `sell_order_service_items` on `sell_order_services`.`id` = `sell_order_service_items`.`sell_order_service_id` where `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `sell_order_services`.`deleted_at` is null and `sell_order_service_items`.`deleted_at` is null and `sell_order_service_items`.`price_currency` = 'EGP') as `service_cost_EGP`, (select COALESCE(SUM(amount), 0) from `payments` inner join `sell_order_services` on `payments`.`payable_id` = `sell_order_services`.`id` where `payments`.`payable_type` = 'App\\\\Models\\\\SellOrderService' and `payments`.`party_type` = 'App\\\\Models\\\\ServiceProvider' and `sell_order_services`.`sell_order_id` = `sell_orders`.`id` and `amount_currency` = 'EGP' and `payments`.`deleted_at` is null and `payments`.`paid_at` is not null) as `service_paid_EGP`, (select service_cost_EGP - service_paid_EGP AS service_remaining_EGP) as `service_remaining_EGP` from `sell_orders` where `sell_orders`.`id` in (1) and `sell_orders`.`company_id` in (1) and `sell_orders`.`deleted_at` is null and `company_id` = 1", "type": "query", "params": [], "bindings": ["EGP", "App\\Models\\SellOrder", "App\\Models\\Client", "EGP", "EGP", "App\\Models\\SellOrderService", "App\\Models\\ServiceProvider", "EGP", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, {"index": 34, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 35, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 36, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 37, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.6570241, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:23", "source": {"index": 26, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=23", "ajax": false, "filename": "InvoiceItem.php", "line": "23"}, "connection": "ooaaps_system", "explain": null, "start_percent": 89.425, "width_percent": 5.122}, {"sql": "select `invoice_items`.*, ROUND(amount * quantity, 2) AS total_amount from `invoice_items` where `invoice_items`.`itemable_type` = 'App\\\\Models\\\\SellOrderProduct' and `invoice_items`.`itemable_id` = 2 and `invoice_items`.`itemable_id` is not null and `invoice_items`.`deleted_at` is null and `company_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\SellOrderProduct", 2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 24}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 30, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 31, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 32, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.6589801, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:24", "source": {"index": 21, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=24", "ajax": false, "filename": "InvoiceItem.php", "line": "24"}, "connection": "ooaaps_system", "explain": null, "start_percent": 94.547, "width_percent": 1.868}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, {"index": 20, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 29, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 30, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.660051, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:16", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=16", "ajax": false, "filename": "BelongsToCompany.php", "line": "16"}, "connection": "ooaaps_system", "explain": null, "start_percent": 96.415, "width_percent": 0}, {"sql": "select `incremental_id` from `invoice_items` where `company_id` = 1 for update", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, {"index": 24, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 32, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 33, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 34, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.6600919, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:21", "source": {"index": 13, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=21", "ajax": false, "filename": "BelongsToCompany.php", "line": "21"}, "connection": "ooaaps_system", "explain": null, "start_percent": 96.415, "width_percent": 1.145}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, {"index": 20, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 29, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 30, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}], "start": **********.660813, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BelongsToCompany.php:26", "source": {"index": 9, "namespace": null, "name": "app/Models/Concerns/BelongsToCompany.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/BelongsToCompany.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FBelongsToCompany.php&line=26", "ajax": false, "filename": "BelongsToCompany.php", "line": "26"}, "connection": "ooaaps_system", "explain": null, "start_percent": 97.56, "width_percent": 0}, {"sql": "insert into `invoice_items` (`amount`, `amount_currency`, `quantity`, `invoice_id`, `itemable_id`, `itemable_type`, `incremental_id`, `company_id`, `updated_at`, `created_at`) values (200, 'EGP', 10, 2, 2, 'App\\\\Models\\\\SellOrderProduct', 4, 1, '2025-08-23 14:59:30', '2025-08-23 14:59:30')", "type": "query", "params": [], "bindings": [200, "EGP", 10, 2, 2, "App\\Models\\SellOrderProduct", 4, 1, "2025-08-23 14:59:30", "2025-08-23 14:59:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/filament/forms/src/Components/Repeater.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/forms/src/Components/Repeater.php", "line": 995}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/support/src/Concerns/EvaluatesClosures.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Components/Concerns/BelongsToModel.php", "line": 55}, {"index": 29, "namespace": null, "name": "vendor/filament/schemas/src/Concerns/BelongsToModel.php", "file": "/Users/<USER>/development/sites/ooaaps-system/vendor/filament/schemas/src/Concerns/BelongsToModel.php", "line": 50}], "start": **********.66089, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "InvoiceItem.php:35", "source": {"index": 18, "namespace": null, "name": "app/Models/Concerns/InvoiceItem.php", "file": "/Users/<USER>/development/sites/ooaaps-system/app/Models/Concerns/InvoiceItem.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FConcerns%2FInvoiceItem.php&line=35", "ajax": false, "filename": "InvoiceItem.php", "line": "35"}, "connection": "ooaaps_system", "explain": null, "start_percent": 97.56, "width_percent": 2.44}]}, "models": {"data": {"App\\Models\\SellOrder": {"created": 1, "retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrder.php&line=1", "ajax": false, "filename": "SellOrder.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\SellOrderProduct": {"created": 2, "retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FSellOrderProduct.php&line=1", "ajax": false, "filename": "SellOrderProduct.php", "line": "?"}}, "App\\Models\\Invoice": {"created": 1, "retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\Client": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\InvoiceItem": {"created": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FInvoiceItem.php&line=1", "ajax": false, "filename": "InvoiceItem.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\PaymentTerm": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FPaymentTerm.php&line=1", "ajax": false, "filename": "PaymentTerm.php", "line": "?"}}, "App\\Models\\ModelState": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fapp%2FModels%2FModelState.php&line=1", "ajax": false, "filename": "ModelState.php", "line": "?"}}}, "count": 26, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 19, "created": 7}}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\SellOrders\\Pages\\CreateSellOrder@create<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FCreateRecord.php&line=75\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fmohamedmoussa%2Fdevelopment%2Fsites%2Fooaaps-system%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FCreateRecord.php&line=75\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Resources/Pages/CreateRecord.php:75-149</a>", "middleware": "web", "duration": "157ms", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1035114273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1035114273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-548543603 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FmLPSoyJmvWe8ZBarpiBS9o9w4YtuQQpXp5tMz9d</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1828 characters\">{&quot;data&quot;:{&quot;record&quot;:null,&quot;data&quot;:[{&quot;client_id&quot;:&quot;1&quot;,&quot;origin&quot;:&quot;local&quot;,&quot;payment_term_id&quot;:1,&quot;sellOrderProducts&quot;:[{&quot;e454fe83-7749-40c0-9147-a0d467853568&quot;:[{&quot;product_id&quot;:&quot;1&quot;,&quot;quantity&quot;:&quot;10&quot;,&quot;sell_price&quot;:300,&quot;sell_price_currency&quot;:&quot;EGP&quot;,&quot;package_type_id&quot;:null,&quot;width&quot;:null,&quot;height&quot;:null,&quot;length&quot;:null,&quot;net_weight&quot;:null,&quot;gross_weight&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;4aaa3bdf-cca5-4dfd-a63c-cf5478b591fc&quot;:[{&quot;product_id&quot;:&quot;2&quot;,&quot;quantity&quot;:&quot;10&quot;,&quot;sell_price&quot;:200,&quot;sell_price_currency&quot;:&quot;EGP&quot;,&quot;package_type_id&quot;:null,&quot;width&quot;:null,&quot;height&quot;:null,&quot;length&quot;:null,&quot;net_weight&quot;:null,&quot;gross_weight&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;model_of_shipping&quot;:&quot;sea&quot;,&quot;incoterm&quot;:&quot;exw&quot;,&quot;pickup_country_id&quot;:null,&quot;pickup_city_id&quot;:null,&quot;pickup_address&quot;:null,&quot;delivery_country_id&quot;:1,&quot;delivery_city_id&quot;:1,&quot;delivery_address&quot;:&quot;Test Address&quot;,&quot;port_of_discharge_type&quot;:&quot;App\\\\Models\\\\Seaport&quot;,&quot;port_of_discharge_id&quot;:1,&quot;port_of_loading_type&quot;:&quot;App\\\\Models\\\\Seaport&quot;,&quot;port_of_loading_id&quot;:1,&quot;sellOrderServices&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/ooaaps-system.test\\/app\\/1\\/sell-orders&quot;,&quot;isCreating&quot;:false,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultActionContext&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableAction&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[&quot;form&quot;,&quot;content&quot;,&quot;headerWidgets&quot;,&quot;footerWidgets&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;parentRecord&quot;:null,&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;JA7TcV8aU4MMYSx6Bmwv&quot;,&quot;name&quot;:&quot;app.filament.resources.sell-orders.pages.create-sell-order&quot;,&quot;path&quot;:&quot;app\\/1\\/sell-orders\\/create&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;4d00fd7e0511b9d64b1caceb3052a6f13c11094df68a069554cbfbc1474497a1&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548543603\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-153324971 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZnb1AycUFnZE1JNXMxc0xLdDV3aXc9PSIsInZhbHVlIjoiQ1E1Uk5tSTdGOWhoclBXdlJiZ3hFNXU1aEpvV1RUMFNBNjltYnVrRW1ZbTIyQWpPd2ZMa3ZIbVZnakVDL2FvS2U4M01zZWN5SVgvNlF5cTZURVVwR1lXWlY3aFNQdCtSVml2em1LMlg1WkhJc1VnS2RXQm5WUnVmaGhMNXQwVG4iLCJtYWMiOiJmYTI5OGM3NjIzNTM1NTc2NzIyZDU4NGU5MWYzYzA2OTc4MWI4ODlkNGIxZDQ4NTAzNWY5YmM3YWI2ZDM3YTNlIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IlJ4YU1XTGh5dlRJcHk1QWNoYWtleGc9PSIsInZhbHVlIjoiMFplTitqdzNCc3BlQU5DZjhVM1hsQnlGcmp1Um5aTExod2c2OTN5c3h3dGVTMDFMWEpidkFwT01IRWVUNTJ6VlY3dlhYdWtpZUNVZksyQ3F2QU1SZFdJZEFvb0NMZ3RtR0hsSVc1QzI2bXpCSTdJS2RiTEpEcVg1M1ZRME5DVEwiLCJtYWMiOiIzNjZhZDI3ZGMzMzU5M2ZjOTZiOGIyZjgwYjc3ZjBjOThiOGIyMTU1NTU0OWM4NDg1YTI1MmIzYTFmY2RhNWI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"82 characters\">http://ooaaps-system.test/app/1/sell-orders/create?tab=services%3A%3Adata%3A%3Atab</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://ooaaps-system.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2219</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">ooaaps-system.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153324971\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-895497720 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FmLPSoyJmvWe8ZBarpiBS9o9w4YtuQQpXp5tMz9d</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qP1xInNNeumRiXQAeaqXmxuSm18dMPxgokbv0Far</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895497720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-992805621 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 23 Aug 2025 14:59:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992805621\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1681310977 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FmLPSoyJmvWe8ZBarpiBS9o9w4YtuQQpXp5tMz9d</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://ooaaps-system.test/app/1/sell-orders/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$UTIJpCjgSNNfL1JJzCz3xuh/fumNeNC0KZk1/6gmavaQY1DpDm1g.</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>3505367d0d4a7f50ee3883de72c9e822_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">invoiceable.raw_title</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Invoiceable</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">total_amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>cbb568ab888e0499be2398899a02ad38_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">notes</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Notes</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>edf68d0dc9e1293c77c20ae5f502b5d6_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">paymentMethod.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment Method</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">due_status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Due Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due_date</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Due Date</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">paid_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Paid At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>404dd4614b633fea236bf905f628be6f_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>b7d18c38c4d3dcee1008ec99e511af63_columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">buy_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Buy Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">sell_price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sell Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>a9995a3b61aaed0eb44a33c693a84ef6_columns</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>8095f893fa3700f01d600ed3e0d1e01d_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">supplier.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">purchaseOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>1334379093eaeb1721e3e07ab596ab24_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>f5b16685aa562a098eb7e35bba4c2ad6_columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Supplier</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company Name</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">due</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Due</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Paid</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">remaining</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Remaining</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>4d50aaceb04f24feedb903830231d115_columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">incremental_id</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str>#</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">client.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Client</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"30 characters\">sellOrderProducts.product.name</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products Price</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">service_cost</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Service Cost</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n        \"<span class=sf-dump-key>isHidden</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggled</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>isToggleable</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>isToggledHiddenByDefault</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9fb4282e-8225-458c-b74b-6cec57e53150</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Created</span>\"\n        \"<span class=sf-dump-key>view</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681310977\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://ooaaps-system.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}
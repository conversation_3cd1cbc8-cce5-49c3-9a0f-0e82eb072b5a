function ye(n){this.content=n}ye.prototype={constructor:ye,find:function(n){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===n)return e;return-1},get:function(n){var e=this.find(n);return e==-1?void 0:this.content[e+1]},update:function(n,e,t){var r=t&&t!=n?this.remove(t):this,i=r.find(n),o=r.content.slice();return i==-1?o.push(t||n,e):(o[i+1]=e,t&&(o[i]=t)),new ye(o)},remove:function(n){var e=this.find(n);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new ye(t)},addToStart:function(n,e){return new ye([n,e].concat(this.remove(n).content))},addToEnd:function(n,e){var t=this.remove(n).content.slice();return t.push(n,e),new ye(t)},addBefore:function(n,e,t){var r=this.remove(e),i=r.content.slice(),o=r.find(n);return i.splice(o==-1?i.length:o,0,e,t),new ye(i)},forEach:function(n){for(var e=0;e<this.content.length;e+=2)n(this.content[e],this.content[e+1])},prepend:function(n){return n=ye.from(n),n.size?new ye(n.content.concat(this.subtract(n).content)):this},append:function(n){return n=ye.from(n),n.size?new ye(this.subtract(n).content.concat(n.content)):this},subtract:function(n){var e=this;n=ye.from(n);for(var t=0;t<n.content.length;t+=2)e=e.remove(n.content[t]);return e},toObject:function(){var n={};return this.forEach(function(e,t){n[e]=t}),n},get size(){return this.content.length>>1}};ye.from=function(n){if(n instanceof ye)return n;var e=[];if(n)for(var t in n)e.push(t,n[t]);return new ye(e)};var Li=ye;function Ys(n,e,t){for(let r=0;;r++){if(r==n.childCount||r==e.childCount)return n.childCount==e.childCount?null:t;let i=n.child(r),o=e.child(r);if(i==o){t+=i.nodeSize;continue}if(!i.sameMarkup(o))return t;if(i.isText&&i.text!=o.text){for(let s=0;i.text[s]==o.text[s];s++)t++;return t}if(i.content.size||o.content.size){let s=Ys(i.content,o.content,t+1);if(s!=null)return s}t+=i.nodeSize}}function Xs(n,e,t,r){for(let i=n.childCount,o=e.childCount;;){if(i==0||o==0)return i==o?null:{a:t,b:r};let s=n.child(--i),l=e.child(--o),a=s.nodeSize;if(s==l){t-=a,r-=a;continue}if(!s.sameMarkup(l))return{a:t,b:r};if(s.isText&&s.text!=l.text){let c=0,f=Math.min(s.text.length,l.text.length);for(;c<f&&s.text[s.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,r--;return{a:t,b:r}}if(s.content.size||l.content.size){let c=Xs(s.content,l.content,t-1,r-1);if(c)return c}t-=a,r-=a}}var S=class n{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let r=0;r<e.length;r++)this.size+=e[r].nodeSize}nodesBetween(e,t,r,i=0,o){for(let s=0,l=0;l<t;s++){let a=this.content[s],c=l+a.nodeSize;if(c>e&&r(a,i+l,o||null,s)!==!1&&a.content.size){let f=l+1;a.nodesBetween(Math.max(0,e-f),Math.min(a.content.size,t-f),r,i+f)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,r,i){let o="",s=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?i?typeof i=="function"?i(l):i:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&r&&(s?s=!1:o+=r),o+=c},0),o}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,r=e.firstChild,i=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(r)&&(i[i.length-1]=t.withText(t.text+r.text),o=1);o<e.content.length;o++)i.push(e.content[o]);return new n(i,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let r=[],i=0;if(t>e)for(let o=0,s=0;s<t;o++){let l=this.content[o],a=s+l.nodeSize;a>e&&((s<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-s),Math.min(l.text.length,t-s)):l=l.cut(Math.max(0,e-s-1),Math.min(l.content.size,t-s-1))),r.push(l),i+=l.nodeSize),s=a}return new n(r,i)}cutByIndex(e,t){return e==t?n.empty:e==0&&t==this.content.length?this:new n(this.content.slice(e,t))}replaceChild(e,t){let r=this.content[e];if(r==t)return this;let i=this.content.slice(),o=this.size+t.nodeSize-r.nodeSize;return i[e]=t,new n(i,o)}addToStart(e){return new n([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new n(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,r=0;t<this.content.length;t++){let i=this.content[t];e(i,r,t),r+=i.nodeSize}}findDiffStart(e,t=0){return Ys(this,e,t)}findDiffEnd(e,t=this.size,r=e.size){return Xs(this,e,t,r)}findIndex(e,t=-1){if(e==0)return Pr(0,e);if(e==this.size)return Pr(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let r=0,i=0;;r++){let o=this.child(r),s=i+o.nodeSize;if(s>=e)return s==e||t>0?Pr(r+1,s):Pr(r,i);i=s}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return n.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new n(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return n.empty;let t,r=0;for(let i=0;i<e.length;i++){let o=e[i];r+=o.nodeSize,i&&o.isText&&e[i-1].sameMarkup(o)?(t||(t=e.slice(0,i)),t[t.length-1]=o.withText(t[t.length-1].text+o.text)):t&&t.push(o)}return new n(t||e,r)}static from(e){if(!e)return n.empty;if(e instanceof n)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new n([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}};S.empty=new S([],0);var Fi={index:0,offset:0};function Pr(n,e){return Fi.index=n,Fi.offset=e,Fi}function Rr(n,e){if(n===e)return!0;if(!(n&&typeof n=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(n);if(Array.isArray(e)!=t)return!1;if(t){if(n.length!=e.length)return!1;for(let r=0;r<n.length;r++)if(!Rr(n[r],e[r]))return!1}else{for(let r in n)if(!(r in e)||!Rr(n[r],e[r]))return!1;for(let r in e)if(!(r in n))return!1}return!0}var $=class n{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,r=!1;for(let i=0;i<e.length;i++){let o=e[i];if(this.eq(o))return e;if(this.type.excludes(o.type))t||(t=e.slice(0,i));else{if(o.type.excludes(this.type))return e;!r&&o.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),r=!0),t&&t.push(o)}}return t||(t=e.slice()),r||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Rr(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let r=e.marks[t.type];if(!r)throw new RangeError(`There is no mark type ${t.type} in this schema`);let i=r.create(t.attrs);return r.checkAttrs(i.attrs),i}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let r=0;r<e.length;r++)if(!e[r].eq(t[r]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return n.none;if(e instanceof n)return[e];let t=e.slice();return t.sort((r,i)=>r.type.rank-i.type.rank),t}};$.none=[];var Ft=class extends Error{},M=class n{constructor(e,t,r){this.content=e,this.openStart=t,this.openEnd=r}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let r=Qs(this.content,e+this.openStart,t);return r&&new n(r,this.openStart,this.openEnd)}removeBetween(e,t){return new n(Zs(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return n.empty;let r=t.openStart||0,i=t.openEnd||0;if(typeof r!="number"||typeof i!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new n(S.fromJSON(e,t.content),r,i)}static maxOpen(e,t=!0){let r=0,i=0;for(let o=e.firstChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.firstChild)r++;for(let o=e.lastChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.lastChild)i++;return new n(e,r,i)}};M.empty=new M(S.empty,0,0);function Zs(n,e,t){let{index:r,offset:i}=n.findIndex(e),o=n.maybeChild(r),{index:s,offset:l}=n.findIndex(t);if(i==e||o.isText){if(l!=t&&!n.child(s).isText)throw new RangeError("Removing non-flat range");return n.cut(0,e).append(n.cut(t))}if(r!=s)throw new RangeError("Removing non-flat range");return n.replaceChild(r,o.copy(Zs(o.content,e-i-1,t-i-1)))}function Qs(n,e,t,r){let{index:i,offset:o}=n.findIndex(e),s=n.maybeChild(i);if(o==e||s.isText)return r&&!r.canReplace(i,i,t)?null:n.cut(0,e).append(t).append(n.cut(e));let l=Qs(s.content,e-o-1,t);return l&&n.replaceChild(i,s.copy(l))}function eu(n,e,t){if(t.openStart>n.depth)throw new Ft("Inserted content deeper than insertion position");if(n.depth-t.openStart!=e.depth-t.openEnd)throw new Ft("Inconsistent open depths");return el(n,e,t,0)}function el(n,e,t,r){let i=n.index(r),o=n.node(r);if(i==e.index(r)&&r<n.depth-t.openStart){let s=el(n,e,t,r+1);return o.copy(o.content.replaceChild(i,s))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&n.depth==r&&e.depth==r){let s=n.parent,l=s.content;return Lt(s,l.cut(0,n.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:s,end:l}=tu(t,n);return Lt(o,nl(n,s,l,e,r))}else return Lt(o,Br(n,e,r))}function tl(n,e){if(!e.type.compatibleContent(n.type))throw new Ft("Cannot join "+e.type.name+" onto "+n.type.name)}function Vi(n,e,t){let r=n.node(t);return tl(r,e.node(t)),r}function Bt(n,e){let t=e.length-1;t>=0&&n.isText&&n.sameMarkup(e[t])?e[t]=n.withText(e[t].text+n.text):e.push(n)}function Vn(n,e,t,r){let i=(e||n).node(t),o=0,s=e?e.index(t):i.childCount;n&&(o=n.index(t),n.depth>t?o++:n.textOffset&&(Bt(n.nodeAfter,r),o++));for(let l=o;l<s;l++)Bt(i.child(l),r);e&&e.depth==t&&e.textOffset&&Bt(e.nodeBefore,r)}function Lt(n,e){return n.type.checkContent(e),n.copy(e)}function nl(n,e,t,r,i){let o=n.depth>i&&Vi(n,e,i+1),s=r.depth>i&&Vi(t,r,i+1),l=[];return Vn(null,n,i,l),o&&s&&e.index(i)==t.index(i)?(tl(o,s),Bt(Lt(o,nl(n,e,t,r,i+1)),l)):(o&&Bt(Lt(o,Br(n,e,i+1)),l),Vn(e,t,i,l),s&&Bt(Lt(s,Br(t,r,i+1)),l)),Vn(r,null,i,l),new S(l)}function Br(n,e,t){let r=[];if(Vn(null,n,t,r),n.depth>t){let i=Vi(n,e,t+1);Bt(Lt(i,Br(n,e,t+1)),r)}return Vn(e,null,t,r),new S(r)}function tu(n,e){let t=e.depth-n.openStart,i=e.node(t).copy(n.content);for(let o=t-1;o>=0;o--)i=e.node(o).copy(S.from(i));return{start:i.resolveNoCache(n.openStart+t),end:i.resolveNoCache(i.content.size-n.openEnd-t)}}var Lr=class n{constructor(e,t,r){this.pos=e,this.path=t,this.parentOffset=r,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let r=this.pos-this.path[this.path.length-1],i=e.child(t);return r?e.child(t).cut(r):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let r=this.path[t*3],i=t==0?0:this.path[t*3-1]+1;for(let o=0;o<e;o++)i+=r.child(o).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return $.none;if(this.textOffset)return e.child(t).marks;let r=e.maybeChild(t-1),i=e.maybeChild(t);if(!r){let l=r;r=i,i=l}let o=r.marks;for(var s=0;s<o.length;s++)o[s].type.spec.inclusive===!1&&(!i||!o[s].isInSet(i.marks))&&(o=o[s--].removeFromSet(o));return o}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let r=t.marks,i=e.parent.maybeChild(e.index());for(var o=0;o<r.length;o++)r[o].type.spec.inclusive===!1&&(!i||!r[o].isInSet(i.marks))&&(r=r[o--].removeFromSet(r));return r}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let r=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);r>=0;r--)if(e.pos<=this.end(r)&&(!t||t(this.node(r))))return new zt(this,e,r);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let r=[],i=0,o=t;for(let s=e;;){let{index:l,offset:a}=s.content.findIndex(o),c=o-a;if(r.push(s,l,i+a),!c||(s=s.child(l),s.isText))break;o=c-1,i+=a+1}return new n(t,r,o)}static resolveCached(e,t){let r=Hs.get(e);if(r)for(let o=0;o<r.elts.length;o++){let s=r.elts[o];if(s.pos==t)return s}else Hs.set(e,r=new $i);let i=r.elts[r.i]=n.resolve(e,t);return r.i=(r.i+1)%nu,i}},$i=class{constructor(){this.elts=[],this.i=0}},nu=12,Hs=new WeakMap,zt=class{constructor(e,t,r){this.$from=e,this.$to=t,this.depth=r}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}},ru=Object.create(null),Re=class n{constructor(e,t,r,i=$.none){this.type=e,this.attrs=t,this.marks=i,this.content=r||S.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,r,i=0){this.content.nodesBetween(e,t,r,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,r,i){return this.content.textBetween(e,t,r,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,r){return this.type==e&&Rr(this.attrs,t||e.defaultAttrs||ru)&&$.sameSet(this.marks,r||$.none)}copy(e=null){return e==this.content?this:new n(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new n(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,r=!1){if(e==t)return M.empty;let i=this.resolve(e),o=this.resolve(t),s=r?0:i.sharedDepth(t),l=i.start(s),c=i.node(s).content.cut(i.pos-l,o.pos-l);return new M(c,i.depth-s,o.depth-s)}replace(e,t,r){return eu(this.resolve(e),this.resolve(t),r)}nodeAt(e){for(let t=this;;){let{index:r,offset:i}=t.content.findIndex(e);if(t=t.maybeChild(r),!t)return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:r}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:r}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:r}=this.content.findIndex(e);if(r<e)return{node:this.content.child(t),index:t,offset:r};let i=this.content.child(t-1);return{node:i,index:t-1,offset:r-i.nodeSize}}resolve(e){return Lr.resolveCached(this,e)}resolveNoCache(e){return Lr.resolve(this,e)}rangeHasMark(e,t,r){let i=!1;return t>e&&this.nodesBetween(e,t,o=>(r.isInSet(o.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),rl(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,r=S.empty,i=0,o=r.childCount){let s=this.contentMatchAt(e).matchFragment(r,i,o),l=s&&s.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=i;a<o;a++)if(!this.type.allowsMarks(r.child(a).marks))return!1;return!0}canReplaceWith(e,t,r,i){if(i&&!this.type.allowsMarks(i))return!1;let o=this.contentMatchAt(e).matchType(r),s=o&&o.matchFragment(this.content,t);return s?s.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=$.none;for(let t=0;t<this.marks.length;t++){let r=this.marks[t];r.type.checkAttrs(r.attrs),e=r.addToSet(e)}if(!$.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let r;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");r=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,r)}let i=S.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,i,r);return o.type.checkAttrs(o.attrs),o}};Re.prototype.text=void 0;var Hi=class n extends Re{constructor(e,t,r,i){if(super(e,t,null,i),!r)throw new RangeError("Empty text nodes are not allowed");this.text=r}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):rl(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new n(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new n(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}};function rl(n,e){for(let t=n.length-1;t>=0;t--)e=n[t].type.name+"("+e+")";return e}var Vt=class n{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let r=new Wi(e,t);if(r.next==null)return n.empty;let i=il(r);r.next&&r.err("Unexpected trailing text");let o=fu(cu(i));return uu(o,r),o}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,r=e.childCount){let i=this;for(let o=t;i&&o<r;o++)i=i.matchType(e.child(o).type);return i}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let r=0;r<e.next.length;r++)if(this.next[t].type==e.next[r].type)return!0;return!1}fillBefore(e,t=!1,r=0){let i=[this];function o(s,l){let a=s.matchFragment(e,r);if(a&&(!t||a.validEnd))return S.from(l.map(c=>c.createAndFill()));for(let c=0;c<s.next.length;c++){let{type:f,next:u}=s.next[c];if(!(f.isText||f.hasRequiredAttrs())&&i.indexOf(u)==-1){i.push(u);let d=o(u,l.concat(f));if(d)return d}}return null}return o(this,[])}findWrapping(e){for(let r=0;r<this.wrapCache.length;r+=2)if(this.wrapCache[r]==e)return this.wrapCache[r+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),r=[{match:this,type:null,via:null}];for(;r.length;){let i=r.shift(),o=i.match;if(o.matchType(e)){let s=[];for(let l=i;l.type;l=l.via)s.push(l.type);return s.reverse()}for(let s=0;s<o.next.length;s++){let{type:l,next:a}=o.next[s];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!i.type||a.validEnd)&&(r.push({match:l.contentMatch,type:l,via:i}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(r){e.push(r);for(let i=0;i<r.next.length;i++)e.indexOf(r.next[i].next)==-1&&t(r.next[i].next)}return t(this),e.map((r,i)=>{let o=i+(r.validEnd?"*":" ")+" ";for(let s=0;s<r.next.length;s++)o+=(s?", ":"")+r.next[s].type.name+"->"+e.indexOf(r.next[s].next);return o}).join(`
`)}};Vt.empty=new Vt(!0);var Wi=class{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}};function il(n){let e=[];do e.push(iu(n));while(n.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function iu(n){let e=[];do e.push(ou(n));while(n.next&&n.next!=")"&&n.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function ou(n){let e=au(n);for(;;)if(n.eat("+"))e={type:"plus",expr:e};else if(n.eat("*"))e={type:"star",expr:e};else if(n.eat("?"))e={type:"opt",expr:e};else if(n.eat("{"))e=su(n,e);else break;return e}function Ws(n){/\D/.test(n.next)&&n.err("Expected number, got '"+n.next+"'");let e=Number(n.next);return n.pos++,e}function su(n,e){let t=Ws(n),r=t;return n.eat(",")&&(n.next!="}"?r=Ws(n):r=-1),n.eat("}")||n.err("Unclosed braced range"),{type:"range",min:t,max:r,expr:e}}function lu(n,e){let t=n.nodeTypes,r=t[e];if(r)return[r];let i=[];for(let o in t){let s=t[o];s.isInGroup(e)&&i.push(s)}return i.length==0&&n.err("No node type or group '"+e+"' found"),i}function au(n){if(n.eat("(")){let e=il(n);return n.eat(")")||n.err("Missing closing paren"),e}else if(/\W/.test(n.next))n.err("Unexpected token '"+n.next+"'");else{let e=lu(n,n.next).map(t=>(n.inline==null?n.inline=t.isInline:n.inline!=t.isInline&&n.err("Mixing inline and block content"),{type:"name",value:t}));return n.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function cu(n){let e=[[]];return i(o(n,0),t()),e;function t(){return e.push([])-1}function r(s,l,a){let c={term:a,to:l};return e[s].push(c),c}function i(s,l){s.forEach(a=>a.to=l)}function o(s,l){if(s.type=="choice")return s.exprs.reduce((a,c)=>a.concat(o(c,l)),[]);if(s.type=="seq")for(let a=0;;a++){let c=o(s.exprs[a],l);if(a==s.exprs.length-1)return c;i(c,l=t())}else if(s.type=="star"){let a=t();return r(l,a),i(o(s.expr,a),a),[r(a)]}else if(s.type=="plus"){let a=t();return i(o(s.expr,l),a),i(o(s.expr,a),a),[r(a)]}else{if(s.type=="opt")return[r(l)].concat(o(s.expr,l));if(s.type=="range"){let a=l;for(let c=0;c<s.min;c++){let f=t();i(o(s.expr,a),f),a=f}if(s.max==-1)i(o(s.expr,a),a);else for(let c=s.min;c<s.max;c++){let f=t();r(a,f),i(o(s.expr,a),f),a=f}return[r(a)]}else{if(s.type=="name")return[r(l,void 0,s.value)];throw new Error("Unknown expr type")}}}}function ol(n,e){return e-n}function js(n,e){let t=[];return r(e),t.sort(ol);function r(i){let o=n[i];if(o.length==1&&!o[0].term)return r(o[0].to);t.push(i);for(let s=0;s<o.length;s++){let{term:l,to:a}=o[s];!l&&t.indexOf(a)==-1&&r(a)}}}function fu(n){let e=Object.create(null);return t(js(n,0));function t(r){let i=[];r.forEach(s=>{n[s].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let f=0;f<i.length;f++)i[f][0]==l&&(c=i[f][1]);js(n,a).forEach(f=>{c||i.push([l,c=[]]),c.indexOf(f)==-1&&c.push(f)})})});let o=e[r.join(",")]=new Vt(r.indexOf(n.length-1)>-1);for(let s=0;s<i.length;s++){let l=i[s][1].sort(ol);o.next.push({type:i[s][0],next:e[l.join(",")]||t(l)})}return o}}function uu(n,e){for(let t=0,r=[n];t<r.length;t++){let i=r[t],o=!i.validEnd,s=[];for(let l=0;l<i.next.length;l++){let{type:a,next:c}=i.next[l];s.push(a.name),o&&!(a.isText||a.hasRequiredAttrs())&&(o=!1),r.indexOf(c)==-1&&r.push(c)}o&&e.err("Only non-generatable nodes ("+s.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function sl(n){let e=Object.create(null);for(let t in n){let r=n[t];if(!r.hasDefault)return null;e[t]=r.default}return e}function ll(n,e){let t=Object.create(null);for(let r in n){let i=e&&e[r];if(i===void 0){let o=n[r];if(o.hasDefault)i=o.default;else throw new RangeError("No value supplied for attribute "+r)}t[r]=i}return t}function al(n,e,t,r){for(let i in e)if(!(i in n))throw new RangeError(`Unsupported attribute ${i} for ${t} of type ${i}`);for(let i in n){let o=n[i];o.validate&&o.validate(e[i])}}function cl(n,e){let t=Object.create(null);if(e)for(let r in e)t[r]=new ji(n,r,e[r]);return t}var Fr=class n{constructor(e,t,r){this.name=e,this.schema=t,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=cl(e,r.attrs),this.defaultAttrs=sl(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Vt.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:ll(this.attrs,e)}create(e=null,t,r){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Re(this,this.computeAttrs(e),S.from(t),$.setFrom(r))}createChecked(e=null,t,r){return t=S.from(t),this.checkContent(t),new Re(this,this.computeAttrs(e),t,$.setFrom(r))}createAndFill(e=null,t,r){if(e=this.computeAttrs(e),t=S.from(t),t.size){let s=this.contentMatch.fillBefore(t);if(!s)return null;t=s.append(t)}let i=this.contentMatch.matchFragment(t),o=i&&i.fillBefore(S.empty,!0);return o?new Re(this,e,t.append(o),$.setFrom(r)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let r=0;r<e.childCount;r++)if(!this.allowsMarks(e.child(r).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){al(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let r=0;r<e.length;r++)this.allowsMarkType(e[r].type)?t&&t.push(e[r]):t||(t=e.slice(0,r));return t?t.length?t:$.none:e}static compile(e,t){let r=Object.create(null);e.forEach((o,s)=>r[o]=new n(o,t,s));let i=t.spec.topNode||"doc";if(!r[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let o in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};function du(n,e,t){let r=t.split("|");return i=>{let o=i===null?"null":typeof i;if(r.indexOf(o)<0)throw new RangeError(`Expected value of type ${r} for attribute ${e} on type ${n}, got ${o}`)}}var ji=class{constructor(e,t,r){this.hasDefault=Object.prototype.hasOwnProperty.call(r,"default"),this.default=r.default,this.validate=typeof r.validate=="string"?du(e,t,r.validate):r.validate}get isRequired(){return!this.hasDefault}},Hn=class n{constructor(e,t,r,i){this.name=e,this.rank=t,this.schema=r,this.spec=i,this.attrs=cl(e,i.attrs),this.excluded=null;let o=sl(this.attrs);this.instance=o?new $(this,o):null}create(e=null){return!e&&this.instance?this.instance:new $(this,ll(this.attrs,e))}static compile(e,t){let r=Object.create(null),i=0;return e.forEach((o,s)=>r[o]=new n(o,i++,t,s)),r}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){al(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}},Wn=class{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let i in e)t[i]=e[i];t.nodes=Li.from(e.nodes),t.marks=Li.from(e.marks||{}),this.nodes=Fr.compile(this.spec.nodes,this),this.marks=Hn.compile(this.spec.marks,this);let r=Object.create(null);for(let i in this.nodes){if(i in this.marks)throw new RangeError(i+" can not be both a node and a mark");let o=this.nodes[i],s=o.spec.content||"",l=o.spec.marks;if(o.contentMatch=r[s]||(r[s]=Vt.parse(s,this.nodes)),o.inlineContent=o.contentMatch.inlineContent,o.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!o.isInline||!o.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=o}o.markSet=l=="_"?null:l?qs(this,l.split(" ")):l==""||!o.inlineContent?[]:null}for(let i in this.marks){let o=this.marks[i],s=o.spec.excludes;o.excluded=s==null?[o]:s==""?[]:qs(this,s.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,r,i){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof Fr){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,r,i)}text(e,t){let r=this.nodes.text;return new Hi(r,r.defaultAttrs,e,$.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Re.fromJSON(this,e)}markFromJSON(e){return $.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}};function qs(n,e){let t=[];for(let r=0;r<e.length;r++){let i=e[r],o=n.marks[i],s=o;if(o)t.push(o);else for(let l in n.marks){let a=n.marks[l];(i=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(i)>-1)&&t.push(s=a)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return t}function pu(n){return n.tag!=null}function hu(n){return n.style!=null}var at=class n{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let r=this.matchedStyles=[];t.forEach(i=>{if(pu(i))this.tags.push(i);else if(hu(i)){let o=/[^=]*/.exec(i.style)[0];r.indexOf(o)<0&&r.push(o),this.styles.push(i)}}),this.normalizeLists=!this.tags.some(i=>{if(!/^(ul|ol)\b/.test(i.tag)||!i.node)return!1;let o=e.nodes[i.node];return o.contentMatch.matchType(o)})}parse(e,t={}){let r=new zr(this,t,!1);return r.addAll(e,$.none,t.from,t.to),r.finish()}parseSlice(e,t={}){let r=new zr(this,t,!0);return r.addAll(e,$.none,t.from,t.to),M.maxOpen(r.finish())}matchTag(e,t,r){for(let i=r?this.tags.indexOf(r)+1:0;i<this.tags.length;i++){let o=this.tags[i];if(yu(e,o.tag)&&(o.namespace===void 0||e.namespaceURI==o.namespace)&&(!o.context||t.matchesContext(o.context))){if(o.getAttrs){let s=o.getAttrs(e);if(s===!1)continue;o.attrs=s||void 0}return o}}}matchStyle(e,t,r,i){for(let o=i?this.styles.indexOf(i)+1:0;o<this.styles.length;o++){let s=this.styles[o],l=s.style;if(!(l.indexOf(e)!=0||s.context&&!r.matchesContext(s.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(s.getAttrs){let a=s.getAttrs(t);if(a===!1)continue;s.attrs=a||void 0}return s}}}static schemaRules(e){let t=[];function r(i){let o=i.priority==null?50:i.priority,s=0;for(;s<t.length;s++){let l=t[s];if((l.priority==null?50:l.priority)<o)break}t.splice(s,0,i)}for(let i in e.marks){let o=e.marks[i].spec.parseDOM;o&&o.forEach(s=>{r(s=_s(s)),s.mark||s.ignore||s.clearMark||(s.mark=i)})}for(let i in e.nodes){let o=e.nodes[i].spec.parseDOM;o&&o.forEach(s=>{r(s=_s(s)),s.node||s.ignore||s.mark||(s.node=i)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new n(e,n.schemaRules(e)))}},fl={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},mu={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},ul={ol:!0,ul:!0},jn=1,qi=2,$n=4;function Js(n,e,t){return e!=null?(e?jn:0)|(e==="full"?qi:0):n&&n.whitespace=="pre"?jn|qi:t&~$n}var hn=class{constructor(e,t,r,i,o,s){this.type=e,this.attrs=t,this.marks=r,this.solid=i,this.options=s,this.content=[],this.activeMarks=$.none,this.match=o||(s&$n?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(S.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let r=this.type.contentMatch,i;return(i=r.findWrapping(e.type))?(this.match=r,i):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&jn)){let r=this.content[this.content.length-1],i;if(r&&r.isText&&(i=/[ \t\r\n\u000c]+$/.exec(r.text))){let o=r;r.text.length==i[0].length?this.content.pop():this.content[this.content.length-1]=o.withText(o.text.slice(0,o.text.length-i[0].length))}}let t=S.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(S.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!fl.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}},zr=class{constructor(e,t,r){this.parser=e,this.options=t,this.isOpen=r,this.open=0,this.localPreserveWS=!1;let i=t.topNode,o,s=Js(null,t.preserveWhitespace,0)|(r?$n:0);i?o=new hn(i.type,i.attrs,$.none,!0,t.topMatch||i.type.contentMatch,s):r?o=new hn(null,null,$.none,!0,null,s):o=new hn(e.schema.topNodeType,null,$.none,!0,null,s),this.nodes=[o],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let r=e.nodeValue,i=this.top,o=i.options&qi?"full":this.localPreserveWS||(i.options&jn)>0;if(o==="full"||i.inlineContext(e)||/[^ \t\r\n\u000c]/.test(r)){if(o)o!=="full"?r=r.replace(/\r?\n|\r/g," "):r=r.replace(/\r\n?/g,`
`);else if(r=r.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(r)&&this.open==this.nodes.length-1){let s=i.content[i.content.length-1],l=e.previousSibling;(!s||l&&l.nodeName=="BR"||s.isText&&/[ \t\r\n\u000c]$/.test(s.text))&&(r=r.slice(1))}r&&this.insertNode(this.parser.schema.text(r),t),this.findInText(e)}else this.findInside(e)}addElement(e,t,r){let i=this.localPreserveWS,o=this.top;(e.tagName=="PRE"||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let s=e.nodeName.toLowerCase(),l;ul.hasOwnProperty(s)&&this.parser.normalizeLists&&gu(e);let a=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(l=this.parser.matchTag(e,this,r));e:if(a?a.ignore:mu.hasOwnProperty(s))this.findInside(e),this.ignoreFallback(e,t);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(e=a.skip);let c,f=this.needsBlock;if(fl.hasOwnProperty(s))o.content.length&&o.content[0].isInline&&this.open&&(this.open--,o=this.top),c=!0,o.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let u=a&&a.skip?t:this.readStyles(e,t);u&&this.addAll(e,u),c&&this.sync(o),this.needsBlock=f}else{let c=this.readStyles(e,t);c&&this.addElementByRule(e,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=i}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),t)}readStyles(e,t){let r=e.style;if(r&&r.length)for(let i=0;i<this.parser.matchedStyles.length;i++){let o=this.parser.matchedStyles[i],s=r.getPropertyValue(o);if(s)for(let l=void 0;;){let a=this.parser.matchStyle(o,s,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?t=t.filter(c=>!a.clearMark(c)):t=t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return t}addElementByRule(e,t,r,i){let o,s;if(t.node)if(s=this.parser.schema.nodes[t.node],s.isLeaf)this.insertNode(s.create(t.attrs),r)||this.leafFallback(e,r);else{let a=this.enter(s,t.attrs||null,r,t.preserveWhitespace);a&&(o=!0,r=a)}else{let a=this.parser.schema.marks[t.mark];r=r.concat(a.create(t.attrs))}let l=this.top;if(s&&s.isLeaf)this.findInside(e);else if(i)this.addElement(e,r,i);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,r));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,r),this.findAround(e,a,!1)}o&&this.sync(l)&&this.open--}addAll(e,t,r,i){let o=r||0;for(let s=r?e.childNodes[r]:e.firstChild,l=i==null?null:e.childNodes[i];s!=l;s=s.nextSibling,++o)this.findAtPoint(e,o),this.addDOM(s,t);this.findAtPoint(e,o)}findPlace(e,t){let r,i;for(let o=this.open;o>=0;o--){let s=this.nodes[o],l=s.findWrapping(e);if(l&&(!r||r.length>l.length)&&(r=l,i=s,!l.length)||s.solid)break}if(!r)return null;this.sync(i);for(let o=0;o<r.length;o++)t=this.enterInner(r[o],null,t,!1);return t}insertNode(e,t){if(e.isInline&&this.needsBlock&&!this.top.type){let i=this.textblockFromContext();i&&(t=this.enterInner(i,null,t))}let r=this.findPlace(e,t);if(r){this.closeExtra();let i=this.top;i.match&&(i.match=i.match.matchType(e.type));let o=$.none;for(let s of r.concat(e.marks))(i.type?i.type.allowsMarkType(s.type):Ks(s.type,e.type))&&(o=s.addToSet(o));return i.content.push(e.mark(o)),!0}return!1}enter(e,t,r,i){let o=this.findPlace(e.create(t),r);return o&&(o=this.enterInner(e,t,r,!0,i)),o}enterInner(e,t,r,i=!1,o){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(e);let l=Js(e,o,s.options);s.options&$n&&s.content.length==0&&(l|=$n);let a=$.none;return r=r.filter(c=>(s.type?s.type.allowsMarkType(c.type):Ks(c.type,e))?(a=c.addToSet(a),!1):!0),this.nodes.push(new hn(e,t,a,i,null,l)),this.open++,r}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=jn)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let r=this.nodes[t].content;for(let i=r.length-1;i>=0;i--)e+=r[i].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let r=0;r<this.find.length;r++)this.find[r].node==e&&this.find[r].offset==t&&(this.find[r].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,r){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)this.find[i].pos==null&&e.nodeType==1&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(r?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),r=this.options.context,i=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),o=-(r?r.depth+1:0)+(i?0:1),s=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=o;a--)if(s(l-1,a))return!0;return!1}else{let f=a>0||a==0&&i?this.nodes[a].type:r&&a>=o?r.node(a-o).type:null;if(!f||f.name!=c&&!f.isInGroup(c))return!1;a--}}return!0};return s(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let r=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(r&&r.isTextblock&&r.defaultAttrs)return r}for(let t in this.parser.schema.nodes){let r=this.parser.schema.nodes[t];if(r.isTextblock&&r.defaultAttrs)return r}}};function gu(n){for(let e=n.firstChild,t=null;e;e=e.nextSibling){let r=e.nodeType==1?e.nodeName.toLowerCase():null;r&&ul.hasOwnProperty(r)&&t?(t.appendChild(e),e=t):r=="li"?t=e:r&&(t=null)}}function yu(n,e){return(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,e)}function _s(n){let e={};for(let t in n)e[t]=n[t];return e}function Ks(n,e){let t=e.schema.nodes;for(let r in t){let i=t[r];if(!i.allowsMarkType(n))continue;let o=[],s=l=>{o.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:f}=l.edge(a);if(c==e||o.indexOf(f)<0&&s(f))return!0}};if(s(i.contentMatch))return!0}}var ct=class n{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},r){r||(r=zi(t).createDocumentFragment());let i=r,o=[];return e.forEach(s=>{if(o.length||s.marks.length){let l=0,a=0;for(;l<o.length&&a<s.marks.length;){let c=s.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(o[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<o.length;)i=o.pop()[1];for(;a<s.marks.length;){let c=s.marks[a++],f=this.serializeMark(c,s.isInline,t);f&&(o.push([c,i]),i.appendChild(f.dom),i=f.contentDOM||f.dom)}}i.appendChild(this.serializeNodeInner(s,t))}),r}serializeNodeInner(e,t){let{dom:r,contentDOM:i}=Ir(zi(t),this.nodes[e.type.name](e),null,e.attrs);if(i){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return r}serializeNode(e,t={}){let r=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let o=this.serializeMark(e.marks[i],e.isInline,t);o&&((o.contentDOM||o.dom).appendChild(r),r=o.dom)}return r}serializeMark(e,t,r={}){let i=this.marks[e.type.name];return i&&Ir(zi(r),i(e,t),null,e.attrs)}static renderSpec(e,t,r=null,i){return Ir(e,t,r,i)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new n(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Us(e.nodes);return t.text||(t.text=r=>r.text),t}static marksFromSchema(e){return Us(e.marks)}};function Us(n){let e={};for(let t in n){let r=n[t].spec.toDOM;r&&(e[t]=r)}return e}function zi(n){return n.document||window.document}var Gs=new WeakMap;function bu(n){let e=Gs.get(n);return e===void 0&&Gs.set(n,e=vu(n)),e}function vu(n){let e=null;function t(r){if(r&&typeof r=="object")if(Array.isArray(r))if(typeof r[0]=="string")e||(e=[]),e.push(r);else for(let i=0;i<r.length;i++)t(r[i]);else for(let i in r)t(r[i])}return t(n),e}function Ir(n,e,t,r){if(typeof e=="string")return{dom:n.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let i=e[0],o;if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(r&&(o=bu(r))&&o.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s=i.indexOf(" ");s>0&&(t=i.slice(0,s),i=i.slice(s+1));let l,a=t?n.createElementNS(t,i):n.createElement(i),c=e[1],f=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){f=2;for(let u in c)if(c[u]!=null){let d=u.indexOf(" ");d>0?a.setAttributeNS(u.slice(0,d),u.slice(d+1),c[u]):a.setAttribute(u,c[u])}}for(let u=f;u<e.length;u++){let d=e[u];if(d===0){if(u<e.length-1||u>f)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:p,contentDOM:h}=Ir(n,d,t,r);if(a.appendChild(p),h){if(l)throw new RangeError("Multiple content holes");l=h}}}return{dom:a,contentDOM:l}}var hl=65535,ml=Math.pow(2,16);function xu(n,e){return n+e*ml}function dl(n){return n&hl}function ku(n){return(n-(n&hl))/ml}var gl=1,yl=2,Vr=4,bl=8,_n=class{constructor(e,t,r){this.pos=e,this.delInfo=t,this.recover=r}get deleted(){return(this.delInfo&bl)>0}get deletedBefore(){return(this.delInfo&(gl|Vr))>0}get deletedAfter(){return(this.delInfo&(yl|Vr))>0}get deletedAcross(){return(this.delInfo&Vr)>0}},ft=class n{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&n.empty)return n.empty}recover(e){let t=0,r=dl(e);if(!this.inverted)for(let i=0;i<r;i++)t+=this.ranges[i*3+2]-this.ranges[i*3+1];return this.ranges[r*3]+t+ku(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,r){let i=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>e)break;let c=this.ranges[l+o],f=this.ranges[l+s],u=a+c;if(e<=u){let d=c?e==a?-1:e==u?1:t:t,p=a+i+(d<0?0:f);if(r)return p;let h=e==(t<0?a:u)?null:xu(l/3,e-a),m=e==a?yl:e==u?gl:Vr;return(t<0?e!=a:e!=u)&&(m|=bl),new _n(p,m,h)}i+=f-c}return r?e+i:new _n(e+i,0,null)}touches(e,t){let r=0,i=dl(t),o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let c=this.ranges[l+o],f=a+c;if(e<=f&&l==i*3)return!0;r+=this.ranges[l+s]-c}return!1}forEach(e){let t=this.inverted?2:1,r=this.inverted?1:2;for(let i=0,o=0;i<this.ranges.length;i+=3){let s=this.ranges[i],l=s-(this.inverted?o:0),a=s+(this.inverted?0:o),c=this.ranges[i+t],f=this.ranges[i+r];e(l,l+c,a,a+f),o+=f-c}}invert(){return new n(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?n.empty:new n(e<0?[0,-e,0]:[0,0,e])}};ft.empty=new ft([]);var Kn=class n{constructor(e=[],t,r=0,i=e.length){this.maps=e,this.mirror=t,this.from=r,this.to=i}slice(e=0,t=this.maps.length){return new n(this.maps,this.mirror,e,t)}copy(){return new n(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(e,t){this.to=this.maps.push(e),t!=null&&this.setMirror(this.maps.length-1,t)}appendMapping(e){for(let t=0,r=this.maps.length;t<e.maps.length;t++){let i=e.getMirror(t);this.appendMap(e.maps[t],i!=null&&i<t?r+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,r=this.maps.length+e.maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e.maps[t].invert(),i!=null&&i>t?r-i-1:void 0)}}invert(){let e=new n;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let r=this.from;r<this.to;r++)e=this.maps[r].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,r){let i=0;for(let o=this.from;o<this.to;o++){let s=this.maps[o],l=s.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(o);if(a!=null&&a>o&&a<this.to){o=a,e=this.maps[a].recover(l.recover);continue}}i|=l.delInfo,e=l.pos}return r?e:new _n(e,i,null)}},Ji=Object.create(null),oe=class{getMap(){return ft.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let r=Ji[t.stepType];if(!r)throw new RangeError(`No step type ${t.stepType} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in Ji)throw new RangeError("Duplicate use of step JSON ID "+e);return Ji[e]=t,t.prototype.jsonID=e,t}},de=class n{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new n(e,null)}static fail(e){return new n(null,e)}static fromReplace(e,t,r,i){try{return n.ok(e.replace(t,r,i))}catch(o){if(o instanceof Ft)return n.fail(o.message);throw o}}};function Yi(n,e,t){let r=[];for(let i=0;i<n.childCount;i++){let o=n.child(i);o.content.size&&(o=o.copy(Yi(o.content,e,o))),o.isInline&&(o=e(o,t,i)),r.push(o)}return S.fromArray(r)}var Un=class n extends oe{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=e.resolve(this.from),i=r.node(r.sharedDepth(this.to)),o=new M(Yi(t.content,(s,l)=>!s.isAtom||!l.type.allowsMarkType(this.mark.type)?s:s.mark(this.mark.addToSet(s.marks)),i),t.openStart,t.openEnd);return de.fromReplace(e,this.from,this.to,o)}invert(){return new $t(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new n(t.pos,r.pos,this.mark)}merge(e){return e instanceof n&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new n(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new n(t.from,t.to,e.markFromJSON(t.mark))}};oe.jsonID("addMark",Un);var $t=class n extends oe{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=new M(Yi(t.content,i=>i.mark(this.mark.removeFromSet(i.marks)),e),t.openStart,t.openEnd);return de.fromReplace(e,this.from,this.to,r)}invert(){return new Un(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new n(t.pos,r.pos,this.mark)}merge(e){return e instanceof n&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new n(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new n(t.from,t.to,e.markFromJSON(t.mark))}};oe.jsonID("removeMark",$t);var Gn=class n extends oe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return de.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return de.fromReplace(e,this.pos,this.pos+1,new M(S.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let r=this.mark.addToSet(t.marks);if(r.length==t.marks.length){for(let i=0;i<t.marks.length;i++)if(!t.marks[i].isInSet(r))return new n(this.pos,t.marks[i]);return new n(this.pos,this.mark)}}return new Yn(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new n(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new n(t.pos,e.markFromJSON(t.mark))}};oe.jsonID("addNodeMark",Gn);var Yn=class n extends oe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return de.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return de.fromReplace(e,this.pos,this.pos+1,new M(S.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new Gn(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new n(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new n(t.pos,e.markFromJSON(t.mark))}};oe.jsonID("removeNodeMark",Yn);var be=class n extends oe{constructor(e,t,r,i=!1){super(),this.from=e,this.to=t,this.slice=r,this.structure=i}apply(e){return this.structure&&Ui(e,this.from,this.to)?de.fail("Structure replace would overwrite content"):de.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new ft([this.from,this.to-this.from,this.slice.size])}invert(e){return new n(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deletedAcross&&r.deletedAcross?null:new n(t.pos,Math.max(t.pos,r.pos),this.slice)}merge(e){if(!(e instanceof n)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?M.empty:new M(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new n(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?M.empty:new M(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new n(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new n(t.from,t.to,M.fromJSON(e,t.slice),!!t.structure)}};oe.jsonID("replace",be);var Q=class n extends oe{constructor(e,t,r,i,o,s,l=!1){super(),this.from=e,this.to=t,this.gapFrom=r,this.gapTo=i,this.slice=o,this.insert=s,this.structure=l}apply(e){if(this.structure&&(Ui(e,this.from,this.gapFrom)||Ui(e,this.gapTo,this.to)))return de.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return de.fail("Gap is not a flat range");let r=this.slice.insertAt(this.insert,t.content);return r?de.fromReplace(e,this.from,this.to,r):de.fail("Content does not fit in gap")}getMap(){return new ft([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new n(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1),i=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),o=this.to==this.gapTo?r.pos:e.map(this.gapTo,1);return t.deletedAcross&&r.deletedAcross||i<t.pos||o>r.pos?null:new n(t.pos,r.pos,i,o,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new n(t.from,t.to,t.gapFrom,t.gapTo,M.fromJSON(e,t.slice),t.insert,!!t.structure)}};oe.jsonID("replaceAround",Q);function Ui(n,e,t){let r=n.resolve(e),i=t-e,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let s=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!s||s.isLeaf)return!0;s=s.firstChild,i--}}return!1}function Su(n,e,t,r){let i=[],o=[],s,l;n.doc.nodesBetween(e,t,(a,c,f)=>{if(!a.isInline)return;let u=a.marks;if(!r.isInSet(u)&&f.type.allowsMarkType(r.type)){let d=Math.max(c,e),p=Math.min(c+a.nodeSize,t),h=r.addToSet(u);for(let m=0;m<u.length;m++)u[m].isInSet(h)||(s&&s.to==d&&s.mark.eq(u[m])?s.to=p:i.push(s=new $t(d,p,u[m])));l&&l.to==d?l.to=p:o.push(l=new Un(d,p,r))}}),i.forEach(a=>n.step(a)),o.forEach(a=>n.step(a))}function wu(n,e,t,r){let i=[],o=0;n.doc.nodesBetween(e,t,(s,l)=>{if(!s.isInline)return;o++;let a=null;if(r instanceof Hn){let c=s.marks,f;for(;f=r.isInSet(c);)(a||(a=[])).push(f),c=f.removeFromSet(c)}else r?r.isInSet(s.marks)&&(a=[r]):a=s.marks;if(a&&a.length){let c=Math.min(l+s.nodeSize,t);for(let f=0;f<a.length;f++){let u=a[f],d;for(let p=0;p<i.length;p++){let h=i[p];h.step==o-1&&u.eq(i[p].style)&&(d=h)}d?(d.to=c,d.step=o):i.push({style:u,from:Math.max(l,e),to:c,step:o})}}}),i.forEach(s=>n.step(new $t(s.from,s.to,s.style)))}function Xi(n,e,t,r=t.contentMatch,i=!0){let o=n.doc.nodeAt(e),s=[],l=e+1;for(let a=0;a<o.childCount;a++){let c=o.child(a),f=l+c.nodeSize,u=r.matchType(c.type);if(!u)s.push(new be(l,f,M.empty));else{r=u;for(let d=0;d<c.marks.length;d++)t.allowsMarkType(c.marks[d].type)||n.step(new $t(l,f,c.marks[d]));if(i&&c.isText&&t.whitespace!="pre"){let d,p=/\r?\n|\r/g,h;for(;d=p.exec(c.text);)h||(h=new M(S.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),s.push(new be(l+d.index,l+d.index+d[0].length,h))}}l=f}if(!r.validEnd){let a=r.fillBefore(S.empty,!0);n.replace(l,l,new M(a,0,0))}for(let a=s.length-1;a>=0;a--)n.step(s[a])}function Mu(n,e,t){return(e==0||n.canReplace(e,n.childCount))&&(t==n.childCount||n.canReplace(0,t))}function ut(n){let t=n.parent.content.cutByIndex(n.startIndex,n.endIndex);for(let r=n.depth;;--r){let i=n.$from.node(r),o=n.$from.index(r),s=n.$to.indexAfter(r);if(r<n.depth&&i.canReplace(o,s,t))return r;if(r==0||i.type.spec.isolating||!Mu(i,o,s))break}return null}function Cu(n,e,t){let{$from:r,$to:i,depth:o}=e,s=r.before(o+1),l=i.after(o+1),a=s,c=l,f=S.empty,u=0;for(let h=o,m=!1;h>t;h--)m||r.index(h)>0?(m=!0,f=S.from(r.node(h).copy(f)),u++):a--;let d=S.empty,p=0;for(let h=o,m=!1;h>t;h--)m||i.after(h+1)<i.end(h)?(m=!0,d=S.from(i.node(h).copy(d)),p++):c++;n.step(new Q(a,c,s,l,new M(f.append(d),u,p),f.size-u,!0))}function gn(n,e,t=null,r=n){let i=Ou(n,e),o=i&&Tu(r,e);return o?i.map(pl).concat({type:e,attrs:t}).concat(o.map(pl)):null}function pl(n){return{type:n,attrs:null}}function Ou(n,e){let{parent:t,startIndex:r,endIndex:i}=n,o=t.contentMatchAt(r).findWrapping(e);if(!o)return null;let s=o.length?o[0]:e;return t.canReplaceWith(r,i,s)?o:null}function Tu(n,e){let{parent:t,startIndex:r,endIndex:i}=n,o=t.child(r),s=e.contentMatch.findWrapping(o.type);if(!s)return null;let a=(s.length?s[s.length-1]:e).contentMatch;for(let c=r;a&&c<i;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:s}function Eu(n,e,t){let r=S.empty;for(let s=t.length-1;s>=0;s--){if(r.size){let l=t[s].type.contentMatch.matchFragment(r);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=S.from(t[s].type.create(t[s].attrs,r))}let i=e.start,o=e.end;n.step(new Q(i,o,i,o,new M(r,0,0),t.length,!0))}function Au(n,e,t,r,i){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let o=n.steps.length;n.doc.nodesBetween(e,t,(s,l)=>{let a=typeof i=="function"?i(s):i;if(s.isTextblock&&!s.hasMarkup(r,a)&&Nu(n.doc,n.mapping.slice(o).map(l),r)){let c=null;if(r.schema.linebreakReplacement){let p=r.whitespace=="pre",h=!!r.contentMatch.matchType(r.schema.linebreakReplacement);p&&!h?c=!1:!p&&h&&(c=!0)}c===!1&&xl(n,s,l,o),Xi(n,n.mapping.slice(o).map(l,1),r,void 0,c===null);let f=n.mapping.slice(o),u=f.map(l,1),d=f.map(l+s.nodeSize,1);return n.step(new Q(u,d,u+1,d-1,new M(S.from(r.create(a,null,s.marks)),0,0),1,!0)),c===!0&&vl(n,s,l,o),!1}})}function vl(n,e,t,r){e.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let a=n.mapping.slice(r).map(t+1+o+s.index);n.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function xl(n,e,t,r){e.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let s=n.mapping.slice(r).map(t+1+o);n.replaceWith(s,s+1,e.type.schema.text(`
`))}})}function Nu(n,e,t){let r=n.resolve(e),i=r.index();return r.parent.canReplaceWith(i,i+1,t)}function Du(n,e,t,r,i){let o=n.doc.nodeAt(e);if(!o)throw new RangeError("No node at given position");t||(t=o.type);let s=t.create(r,null,i||o.marks);if(o.isLeaf)return n.replaceWith(e,e+o.nodeSize,s);if(!t.validContent(o.content))throw new RangeError("Invalid content for node type "+t.name);n.step(new Q(e,e+o.nodeSize,e+1,e+o.nodeSize-1,new M(S.from(s),0,0),1,!0))}function Be(n,e,t=1,r){let i=n.resolve(e),o=i.depth-t,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let c=i.depth-1,f=t-2;c>o;c--,f--){let u=i.node(c),d=i.index(c);if(u.type.spec.isolating)return!1;let p=u.content.cutByIndex(d,u.childCount),h=r&&r[f+1];h&&(p=p.replaceChild(0,h.type.create(h.attrs)));let m=r&&r[f]||u;if(!u.canReplace(d+1,u.childCount)||!m.type.validContent(p))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function Pu(n,e,t=1,r){let i=n.doc.resolve(e),o=S.empty,s=S.empty;for(let l=i.depth,a=i.depth-t,c=t-1;l>a;l--,c--){o=S.from(i.node(l).copy(o));let f=r&&r[c];s=S.from(f?f.type.create(f.attrs,s):i.node(l).copy(s))}n.step(new be(e,e,new M(o.append(s),t,t),!0))}function qe(n,e){let t=n.resolve(e),r=t.index();return kl(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(r,r+1)}function Iu(n,e){e.content.size||n.type.compatibleContent(e.type);let t=n.contentMatchAt(n.childCount),{linebreakReplacement:r}=n.type.schema;for(let i=0;i<e.childCount;i++){let o=e.child(i),s=o.type==r?n.type.schema.nodes.text:o.type;if(t=t.matchType(s),!t||!n.type.allowsMarks(o.marks))return!1}return t.validEnd}function kl(n,e){return!!(n&&e&&!n.isLeaf&&Iu(n,e))}function yn(n,e,t=-1){let r=n.resolve(e);for(let i=r.depth;;i--){let o,s,l=r.index(i);if(i==r.depth?(o=r.nodeBefore,s=r.nodeAfter):t>0?(o=r.node(i+1),l++,s=r.node(i).maybeChild(l)):(o=r.node(i).maybeChild(l-1),s=r.node(i+1)),o&&!o.isTextblock&&kl(o,s)&&r.node(i).canReplace(l,l+1))return e;if(i==0)break;e=t<0?r.before(i):r.after(i)}}function Ru(n,e,t){let r=null,{linebreakReplacement:i}=n.doc.type.schema,o=n.doc.resolve(e-t),s=o.node().type;if(i&&s.inlineContent){let f=s.whitespace=="pre",u=!!s.contentMatch.matchType(i);f&&!u?r=!1:!f&&u&&(r=!0)}let l=n.steps.length;if(r===!1){let f=n.doc.resolve(e+t);xl(n,f.node(),f.before(),l)}s.inlineContent&&Xi(n,e+t-1,s,o.node().contentMatchAt(o.index()),r==null);let a=n.mapping.slice(l),c=a.map(e-t);if(n.step(new be(c,a.map(e+t,-1),M.empty,!0)),r===!0){let f=n.doc.resolve(c);vl(n,f.node(),f.before(),n.steps.length)}return n}function Bu(n,e,t){let r=n.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),t))return e;if(r.parentOffset==0)for(let i=r.depth-1;i>=0;i--){let o=r.index(i);if(r.node(i).canReplaceWith(o,o,t))return r.before(i+1);if(o>0)return null}if(r.parentOffset==r.parent.content.size)for(let i=r.depth-1;i>=0;i--){let o=r.indexAfter(i);if(r.node(i).canReplaceWith(o,o,t))return r.after(i+1);if(o<r.node(i).childCount)return null}return null}function Wr(n,e,t){let r=n.resolve(e);if(!t.content.size)return e;let i=t.content;for(let o=0;o<t.openStart;o++)i=i.firstChild.content;for(let o=1;o<=(t.openStart==0&&t.size?2:1);o++)for(let s=r.depth;s>=0;s--){let l=s==r.depth?0:r.pos<=(r.start(s+1)+r.end(s+1))/2?-1:1,a=r.index(s)+(l>0?1:0),c=r.node(s),f=!1;if(o==1)f=c.canReplace(a,a,i);else{let u=c.contentMatchAt(a).findWrapping(i.firstChild.type);f=u&&c.canReplaceWith(a,a,u[0])}if(f)return l==0?r.pos:l<0?r.before(s+1):r.after(s+1)}return null}function Zn(n,e,t=e,r=M.empty){if(e==t&&!r.size)return null;let i=n.resolve(e),o=n.resolve(t);return Sl(i,o,r)?new be(e,t,r):new Gi(i,o,r).fit()}function Sl(n,e,t){return!t.openStart&&!t.openEnd&&n.start()==e.start()&&n.parent.canReplace(n.index(),e.index(),t.content)}var Gi=class{constructor(e,t,r){this.$from=e,this.$to=t,this.unplaced=r,this.frontier=[],this.placed=S.empty;for(let i=0;i<=e.depth;i++){let o=e.node(i);this.frontier.push({type:o.type,match:o.contentMatchAt(e.indexAfter(i))})}for(let i=e.depth;i>0;i--)this.placed=S.from(e.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,r=this.$from,i=this.close(e<0?this.$to:r.doc.resolve(e));if(!i)return null;let o=this.placed,s=r.depth,l=i.depth;for(;s&&l&&o.childCount==1;)o=o.firstChild.content,s--,l--;let a=new M(o,s,l);return e>-1?new Q(r.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||r.pos!=this.$to.pos?new be(r.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,r=0,i=this.unplaced.openEnd;r<e;r++){let o=t.firstChild;if(t.childCount>1&&(i=0),o.type.spec.isolating&&i<=r){e=r;break}t=o.content}for(let t=1;t<=2;t++)for(let r=t==1?e:this.unplaced.openStart;r>=0;r--){let i,o=null;r?(o=_i(this.unplaced.content,r-1).firstChild,i=o.content):i=this.unplaced.content;let s=i.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],f,u=null;if(t==1&&(s?c.matchType(s.type)||(u=c.fillBefore(S.from(s),!1)):o&&a.compatibleContent(o.type)))return{sliceDepth:r,frontierDepth:l,parent:o,inject:u};if(t==2&&s&&(f=c.findWrapping(s.type)))return{sliceDepth:r,frontierDepth:l,parent:o,wrap:f};if(o&&c.matchType(o.type))break}}}openMore(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=_i(e,t);return!i.childCount||i.firstChild.isLeaf?!1:(this.unplaced=new M(e,t+1,Math.max(r,i.size+t>=e.size-r?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=_i(e,t);if(i.childCount<=1&&t>0){let o=e.size-t<=t+i.size;this.unplaced=new M(qn(e,t-1,1),t-1,o?t-1:r)}else this.unplaced=new M(qn(e,t,1),t,r)}placeNodes({sliceDepth:e,frontierDepth:t,parent:r,inject:i,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let m=0;m<o.length;m++)this.openFrontierNode(o[m]);let s=this.unplaced,l=r?r.content:s.content,a=s.openStart-e,c=0,f=[],{match:u,type:d}=this.frontier[t];if(i){for(let m=0;m<i.childCount;m++)f.push(i.child(m));u=u.matchFragment(i)}let p=l.size+e-(s.content.size-s.openEnd);for(;c<l.childCount;){let m=l.child(c),g=u.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(u=g,f.push(wl(m.mark(d.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?p:-1)))}let h=c==l.childCount;h||(p=-1),this.placed=Jn(this.placed,t,S.from(f)),this.frontier[t].match=u,h&&p<0&&r&&r.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<p;m++){let b=g.lastChild;this.frontier.push({type:b.type,match:b.contentMatchAt(b.childCount)}),g=b.content}this.unplaced=h?e==0?M.empty:new M(qn(s.content,e-1,1),e-1,p<0?s.openEnd:e-1):new M(qn(s.content,e,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!Ki(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:r}=this.$to,i=this.$to.after(r);for(;r>1&&i==this.$to.end(--r);)++i;return i}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:r,type:i}=this.frontier[t],o=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),s=Ki(e,t,i,r,o);if(s){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],f=Ki(e,l,c,a,!0);if(!f||f.childCount)continue e}return{depth:t,fit:s,move:o?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=Jn(this.placed,t.depth,t.fit)),e=t.move;for(let r=t.depth+1;r<=e.depth;r++){let i=e.node(r),o=i.type.contentMatch.fillBefore(i.content,!0,e.index(r));this.openFrontierNode(i.type,i.attrs,o)}return e}openFrontierNode(e,t=null,r){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=Jn(this.placed,this.depth,S.from(e.create(t,r))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(S.empty,!0);t.childCount&&(this.placed=Jn(this.placed,this.frontier.length,t))}};function qn(n,e,t){return e==0?n.cutByIndex(t,n.childCount):n.replaceChild(0,n.firstChild.copy(qn(n.firstChild.content,e-1,t)))}function Jn(n,e,t){return e==0?n.append(t):n.replaceChild(n.childCount-1,n.lastChild.copy(Jn(n.lastChild.content,e-1,t)))}function _i(n,e){for(let t=0;t<e;t++)n=n.firstChild.content;return n}function wl(n,e,t){if(e<=0)return n;let r=n.content;return e>1&&(r=r.replaceChild(0,wl(r.firstChild,e-1,r.childCount==1?t-1:0))),e>0&&(r=n.type.contentMatch.fillBefore(r).append(r),t<=0&&(r=r.append(n.type.contentMatch.matchFragment(r).fillBefore(S.empty,!0)))),n.copy(r)}function Ki(n,e,t,r,i){let o=n.node(e),s=i?n.indexAfter(e):n.index(e);if(s==o.childCount&&!t.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!Lu(t,o.content,s)?l:null}function Lu(n,e,t){for(let r=t;r<e.childCount;r++)if(!n.allowsMarks(e.child(r).marks))return!0;return!1}function Fu(n){return n.spec.defining||n.spec.definingForContent}function zu(n,e,t,r){if(!r.size)return n.deleteRange(e,t);let i=n.doc.resolve(e),o=n.doc.resolve(t);if(Sl(i,o,r))return n.step(new be(e,t,r));let s=Cl(i,n.doc.resolve(t));s[s.length-1]==0&&s.pop();let l=-(i.depth+1);s.unshift(l);for(let d=i.depth,p=i.pos-1;d>0;d--,p--){let h=i.node(d).type.spec;if(h.defining||h.definingAsContext||h.isolating)break;s.indexOf(d)>-1?l=d:i.before(d)==p&&s.splice(1,0,-d)}let a=s.indexOf(l),c=[],f=r.openStart;for(let d=r.content,p=0;;p++){let h=d.firstChild;if(c.push(h),p==r.openStart)break;d=h.content}for(let d=f-1;d>=0;d--){let p=c[d],h=Fu(p.type);if(h&&!p.sameMarkup(i.node(Math.abs(l)-1)))f=d;else if(h||!p.type.isTextblock)break}for(let d=r.openStart;d>=0;d--){let p=(d+f+1)%(r.openStart+1),h=c[p];if(h)for(let m=0;m<s.length;m++){let g=s[(m+a)%s.length],b=!0;g<0&&(b=!1,g=-g);let x=i.node(g-1),w=i.index(g-1);if(x.canReplaceWith(w,w,h.type,h.marks))return n.replace(i.before(g),b?o.after(g):t,new M(Ml(r.content,0,r.openStart,p),p,r.openEnd))}}let u=n.steps.length;for(let d=s.length-1;d>=0&&(n.replace(e,t,r),!(n.steps.length>u));d--){let p=s[d];p<0||(e=i.before(p),t=o.after(p))}}function Ml(n,e,t,r,i){if(e<t){let o=n.firstChild;n=n.replaceChild(0,o.copy(Ml(o.content,e+1,t,r,o)))}if(e>r){let o=i.contentMatchAt(0),s=o.fillBefore(n).append(n);n=s.append(o.matchFragment(s).fillBefore(S.empty,!0))}return n}function Vu(n,e,t,r){if(!r.isInline&&e==t&&n.doc.resolve(e).parent.content.size){let i=Bu(n.doc,e,r.type);i!=null&&(e=t=i)}n.replaceRange(e,t,new M(S.from(r),0,0))}function $u(n,e,t){let r=n.doc.resolve(e),i=n.doc.resolve(t),o=Cl(r,i);for(let s=0;s<o.length;s++){let l=o[s],a=s==o.length-1;if(a&&l==0||r.node(l).type.contentMatch.validEnd)return n.delete(r.start(l),i.end(l));if(l>0&&(a||r.node(l-1).canReplace(r.index(l-1),i.indexAfter(l-1))))return n.delete(r.before(l),i.after(l))}for(let s=1;s<=r.depth&&s<=i.depth;s++)if(e-r.start(s)==r.depth-s&&t>r.end(s)&&i.end(s)-t!=i.depth-s&&r.start(s-1)==i.start(s-1)&&r.node(s-1).canReplace(r.index(s-1),i.index(s-1)))return n.delete(r.before(s),t);n.delete(e,t)}function Cl(n,e){let t=[],r=Math.min(n.depth,e.depth);for(let i=r;i>=0;i--){let o=n.start(i);if(o<n.pos-(n.depth-i)||e.end(i)>e.pos+(e.depth-i)||n.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(o==e.start(i)||i==n.depth&&i==e.depth&&n.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==o-1)&&t.push(i)}return t}var $r=class n extends oe{constructor(e,t,r){super(),this.pos=e,this.attr=t,this.value=r}apply(e){let t=e.nodeAt(this.pos);if(!t)return de.fail("No node at attribute step's position");let r=Object.create(null);for(let o in t.attrs)r[o]=t.attrs[o];r[this.attr]=this.value;let i=t.type.create(r,null,t.marks);return de.fromReplace(e,this.pos,this.pos+1,new M(S.from(i),0,t.isLeaf?0:1))}getMap(){return ft.empty}invert(e){return new n(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new n(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new n(t.pos,t.attr,t.value)}};oe.jsonID("attr",$r);var Hr=class n extends oe{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let i in e.attrs)t[i]=e.attrs[i];t[this.attr]=this.value;let r=e.type.create(t,e.content,e.marks);return de.ok(r)}getMap(){return ft.empty}invert(e){return new n(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new n(t.attr,t.value)}};oe.jsonID("docAttr",Hr);var mn=class extends Error{};mn=function n(e){let t=Error.call(this,e);return t.__proto__=n.prototype,t};mn.prototype=Object.create(Error.prototype);mn.prototype.constructor=mn;mn.prototype.name="TransformError";var Xn=class{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Kn}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new mn(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,r=M.empty){let i=Zn(this.doc,e,t,r);return i&&this.step(i),this}replaceWith(e,t,r){return this.replace(e,t,new M(S.from(r),0,0))}delete(e,t){return this.replace(e,t,M.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,r){return zu(this,e,t,r),this}replaceRangeWith(e,t,r){return Vu(this,e,t,r),this}deleteRange(e,t){return $u(this,e,t),this}lift(e,t){return Cu(this,e,t),this}join(e,t=1){return Ru(this,e,t),this}wrap(e,t){return Eu(this,e,t),this}setBlockType(e,t=e,r,i=null){return Au(this,e,t,r,i),this}setNodeMarkup(e,t,r=null,i){return Du(this,e,t,r,i),this}setNodeAttribute(e,t,r){return this.step(new $r(e,t,r)),this}setDocAttribute(e,t){return this.step(new Hr(e,t)),this}addNodeMark(e,t){return this.step(new Gn(e,t)),this}removeNodeMark(e,t){if(!(t instanceof $)){let r=this.doc.nodeAt(e);if(!r)throw new RangeError("No node at position "+e);if(t=t.isInSet(r.marks),!t)return this}return this.step(new Yn(e,t)),this}split(e,t=1,r){return Pu(this,e,t,r),this}addMark(e,t,r){return Su(this,e,t,r),this}removeMark(e,t,r){return wu(this,e,t,r),this}clearIncompatible(e,t,r){return Xi(this,e,t,r),this}};var Zi=Object.create(null),D=class{constructor(e,t,r){this.$anchor=e,this.$head=t,this.ranges=r||[new qr(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=M.empty){let r=t.content.lastChild,i=null;for(let l=0;l<t.openEnd;l++)i=r,r=r.lastChild;let o=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:c}=s[l],f=e.mapping.slice(o);e.replaceRange(f.map(a.pos),f.map(c.pos),l?M.empty:t),l==0&&El(e,o,(r?r.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let r=e.steps.length,i=this.ranges;for(let o=0;o<i.length;o++){let{$from:s,$to:l}=i[o],a=e.mapping.slice(r),c=a.map(s.pos),f=a.map(l.pos);o?e.deleteRange(c,f):(e.replaceRangeWith(c,f,t),El(e,r,t.isInline?-1:1))}}static findFrom(e,t,r=!1){let i=e.parent.inlineContent?new P(e):bn(e.node(0),e.parent,e.pos,e.index(),t,r);if(i)return i;for(let o=e.depth-1;o>=0;o--){let s=t<0?bn(e.node(0),e.node(o),e.before(o+1),e.index(o),t,r):bn(e.node(0),e.node(o),e.after(o+1),e.index(o)+1,t,r);if(s)return s}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new we(e.node(0))}static atStart(e){return bn(e,e,0,0,1)||new we(e)}static atEnd(e){return bn(e,e,e.content.size,e.childCount,-1)||new we(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let r=Zi[t.type];if(!r)throw new RangeError(`No selection type ${t.type} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in Zi)throw new RangeError("Duplicate use of selection JSON ID "+e);return Zi[e]=t,t.prototype.jsonID=e,t}getBookmark(){return P.between(this.$anchor,this.$head).getBookmark()}};D.prototype.visible=!0;var qr=class{constructor(e,t){this.$from=e,this.$to=t}},Ol=!1;function Tl(n){!Ol&&!n.parent.inlineContent&&(Ol=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+n.parent.type.name+")"))}var P=class n extends D{constructor(e,t=e){Tl(e),Tl(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let r=e.resolve(t.map(this.head));if(!r.parent.inlineContent)return D.near(r);let i=e.resolve(t.map(this.anchor));return new n(i.parent.inlineContent?i:r,r)}replace(e,t=M.empty){if(super.replace(e,t),t==M.empty){let r=this.$from.marksAcross(this.$to);r&&e.ensureMarks(r)}}eq(e){return e instanceof n&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Jr(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new n(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,r=t){let i=e.resolve(t);return new this(i,r==t?i:e.resolve(r))}static between(e,t,r){let i=e.pos-t.pos;if((!r||i)&&(r=i>=0?1:-1),!t.parent.inlineContent){let o=D.findFrom(t,r,!0)||D.findFrom(t,-r,!0);if(o)t=o.$head;else return D.near(t,r)}return e.parent.inlineContent||(i==0?e=t:(e=(D.findFrom(e,-r,!0)||D.findFrom(e,r,!0)).$anchor,e.pos<t.pos!=i<0&&(e=t))),new n(e,t)}};D.jsonID("text",P);var Jr=class n{constructor(e,t){this.anchor=e,this.head=t}map(e){return new n(e.map(this.anchor),e.map(this.head))}resolve(e){return P.between(e.resolve(this.anchor),e.resolve(this.head))}},A=class n extends D{constructor(e){let t=e.nodeAfter,r=e.node(0).resolve(e.pos+t.nodeSize);super(e,r),this.node=t}map(e,t){let{deleted:r,pos:i}=t.mapResult(this.anchor),o=e.resolve(i);return r?D.near(o):new n(o)}content(){return new M(S.from(this.node),0,0)}eq(e){return e instanceof n&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new eo(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new n(e.resolve(t.anchor))}static create(e,t){return new n(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}};A.prototype.visible=!1;D.jsonID("node",A);var eo=class n{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:r}=e.mapResult(this.anchor);return t?new Jr(r,r):new n(r)}resolve(e){let t=e.resolve(this.anchor),r=t.nodeAfter;return r&&A.isSelectable(r)?new A(t):D.near(t)}},we=class n extends D{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=M.empty){if(t==M.empty){e.delete(0,e.doc.content.size);let r=D.atStart(e.doc);r.eq(e.selection)||e.setSelection(r)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new n(e)}map(e){return new n(e)}eq(e){return e instanceof n}getBookmark(){return Hu}};D.jsonID("all",we);var Hu={map(){return this},resolve(n){return new we(n)}};function bn(n,e,t,r,i,o=!1){if(e.inlineContent)return P.create(n,t);for(let s=r-(i>0?0:1);i>0?s<e.childCount:s>=0;s+=i){let l=e.child(s);if(l.isAtom){if(!o&&A.isSelectable(l))return A.create(n,t-(i<0?l.nodeSize:0))}else{let a=bn(n,l,t+i,i<0?l.childCount:0,i,o);if(a)return a}t+=l.nodeSize*i}return null}function El(n,e,t){let r=n.steps.length-1;if(r<e)return;let i=n.steps[r];if(!(i instanceof be||i instanceof Q))return;let o=n.mapping.maps[r],s;o.forEach((l,a,c,f)=>{s==null&&(s=f)}),n.setSelection(D.near(n.doc.resolve(s),t))}var Al=1,jr=2,Nl=4,to=class extends Xn{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|Al)&~jr,this.storedMarks=null,this}get selectionSet(){return(this.updated&Al)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=jr,this}ensureMarks(e){return $.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&jr)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&~jr,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let r=this.selection;return t&&(e=e.mark(this.storedMarks||(r.empty?r.$from.marks():r.$from.marksAcross(r.$to)||$.none))),r.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,r){let i=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(r==null&&(r=t),r=r??t,!e)return this.deleteRange(t,r);let o=this.storedMarks;if(!o){let s=this.doc.resolve(t);o=r==t?s.marks():s.marksAcross(this.doc.resolve(r))}return this.replaceRangeWith(t,r,i.text(e,o)),this.selection.empty||this.setSelection(D.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=Nl,this}get scrolledIntoView(){return(this.updated&Nl)>0}};function Dl(n,e){return!e||!n?n:n.bind(e)}var Ht=class{constructor(e,t,r){this.name=e,this.init=Dl(t.init,r),this.apply=Dl(t.apply,r)}},Wu=[new Ht("doc",{init(n){return n.doc||n.schema.topNodeType.createAndFill()},apply(n){return n.doc}}),new Ht("selection",{init(n,e){return n.selection||D.atStart(e.doc)},apply(n){return n.selection}}),new Ht("storedMarks",{init(n){return n.storedMarks||null},apply(n,e,t,r){return r.selection.$cursor?n.storedMarks:null}}),new Ht("scrollToSelection",{init(){return 0},apply(n,e){return n.scrolledIntoView?e+1:e}})],Qn=class{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Wu.slice(),t&&t.forEach(r=>{if(this.pluginsByKey[r.key])throw new RangeError("Adding different instances of a keyed plugin ("+r.key+")");this.plugins.push(r),this.pluginsByKey[r.key]=r,r.spec.state&&this.fields.push(new Ht(r.key,r.spec.state,r))})}},_r=class n{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let r=0;r<this.config.plugins.length;r++)if(r!=t){let i=this.config.plugins[r];if(i.spec.filterTransaction&&!i.spec.filterTransaction.call(i,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],r=this.applyInner(e),i=null;for(;;){let o=!1;for(let s=0;s<this.config.plugins.length;s++){let l=this.config.plugins[s];if(l.spec.appendTransaction){let a=i?i[s].n:0,c=i?i[s].state:this,f=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,r);if(f&&r.filterTransaction(f,s)){if(f.setMeta("appendedTransaction",e),!i){i=[];for(let u=0;u<this.config.plugins.length;u++)i.push(u<s?{state:r,n:t.length}:{state:this,n:0})}t.push(f),r=r.applyInner(f),o=!0}i&&(i[s]={state:r,n:t.length})}}if(!o)return{state:r,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new n(this.config),r=this.config.fields;for(let i=0;i<r.length;i++){let o=r[i];t[o.name]=o.apply(e,this[o.name],this,t)}return t}get tr(){return new to(this)}static create(e){let t=new Qn(e.doc?e.doc.type.schema:e.schema,e.plugins),r=new n(t);for(let i=0;i<t.fields.length;i++)r[t.fields[i].name]=t.fields[i].init(e,r);return r}reconfigure(e){let t=new Qn(this.schema,e.plugins),r=t.fields,i=new n(t);for(let o=0;o<r.length;o++){let s=r[o].name;i[s]=this.hasOwnProperty(s)?this[s]:r[o].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(r=>r.toJSON())),e&&typeof e=="object")for(let r in e){if(r=="doc"||r=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[r],o=i.spec.state;o&&o.toJSON&&(t[r]=o.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,r){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let i=new Qn(e.schema,e.plugins),o=new n(i);return i.fields.forEach(s=>{if(s.name=="doc")o.doc=Re.fromJSON(e.schema,t.doc);else if(s.name=="selection")o.selection=D.fromJSON(o.doc,t.selection);else if(s.name=="storedMarks")t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(r)for(let l in r){let a=r[l],c=a.spec.state;if(a.key==s.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){o[s.name]=c.fromJSON.call(a,e,t[l],o);return}}o[s.name]=s.init(e,o)}}),o}};function Pl(n,e,t){for(let r in n){let i=n[r];i instanceof Function?i=i.bind(e):r=="handleDOMEvents"&&(i=Pl(i,e,{})),t[r]=i}return t}var q=class{constructor(e){this.spec=e,this.props={},e.props&&Pl(e.props,this,this.props),this.key=e.key?e.key.key:Il("plugin")}getState(e){return e[this.key]}},Qi=Object.create(null);function Il(n){return n in Qi?n+"$"+ ++Qi[n]:(Qi[n]=0,n+"$")}var X=class{constructor(e="key"){this.key=Il(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}};var pe=function(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e},rr=function(n){let e=n.assignedSlot||n.parentNode;return e&&e.nodeType==11?e.host:e},lo=null,pt=function(n,e,t){let r=lo||(lo=document.createRange());return r.setEnd(n,t??n.nodeValue.length),r.setStart(n,e||0),r},ju=function(){lo=null},Ut=function(n,e,t,r){return t&&(Rl(n,e,t,r,-1)||Rl(n,e,t,r,1))},qu=/^(img|br|input|textarea|hr)$/i;function Rl(n,e,t,r,i){for(;;){if(n==t&&e==r)return!0;if(e==(i<0?0:Fe(n))){let o=n.parentNode;if(!o||o.nodeType!=1||lr(n)||qu.test(n.nodeName)||n.contentEditable=="false")return!1;e=pe(n)+(i<0?0:1),n=o}else if(n.nodeType==1){if(n=n.childNodes[e+(i<0?-1:0)],n.contentEditable=="false")return!1;e=i<0?Fe(n):0}else return!1}}function Fe(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function Ju(n,e){for(;;){if(n.nodeType==3&&e)return n;if(n.nodeType==1&&e>0){if(n.contentEditable=="false")return null;n=n.childNodes[e-1],e=Fe(n)}else if(n.parentNode&&!lr(n))e=pe(n),n=n.parentNode;else return null}}function _u(n,e){for(;;){if(n.nodeType==3&&e<n.nodeValue.length)return n;if(n.nodeType==1&&e<n.childNodes.length){if(n.contentEditable=="false")return null;n=n.childNodes[e],e=0}else if(n.parentNode&&!lr(n))e=pe(n)+1,n=n.parentNode;else return null}}function Ku(n,e,t){for(let r=e==0,i=e==Fe(n);r||i;){if(n==t)return!0;let o=pe(n);if(n=n.parentNode,!n)return!1;r=r&&o==0,i=i&&o==Fe(n)}}function lr(n){let e;for(let t=n;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==n||e.contentDOM==n)}var ii=function(n){return n.focusNode&&Ut(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)};function Wt(n,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=n,t.key=t.code=e,t}function Uu(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function Gu(n,e,t){if(n.caretPositionFromPoint)try{let r=n.caretPositionFromPoint(e,t);if(r)return{node:r.offsetNode,offset:Math.min(Fe(r.offsetNode),r.offset)}}catch{}if(n.caretRangeFromPoint){let r=n.caretRangeFromPoint(e,t);if(r)return{node:r.startContainer,offset:Math.min(Fe(r.startContainer),r.startOffset)}}}var Xe=typeof navigator<"u"?navigator:null,Bl=typeof document<"u"?document:null,Tt=Xe&&Xe.userAgent||"",ao=/Edge\/(\d+)/.exec(Tt),pa=/MSIE \d/.exec(Tt),co=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Tt),Ee=!!(pa||co||ao),Ct=pa?document.documentMode:co?+co[1]:ao?+ao[1]:0,Je=!Ee&&/gecko\/(\d+)/i.test(Tt);Je&&+(/Firefox\/(\d+)/.exec(Tt)||[0,0])[1];var fo=!Ee&&/Chrome\/(\d+)/.exec(Tt),xe=!!fo,ha=fo?+fo[1]:0,Me=!Ee&&!!Xe&&/Apple Computer/.test(Xe.vendor),wn=Me&&(/Mobile\/\w+/.test(Tt)||!!Xe&&Xe.maxTouchPoints>2),Le=wn||(Xe?/Mac/.test(Xe.platform):!1),Yu=Xe?/Win/.test(Xe.platform):!1,ht=/Android \d/.test(Tt),ar=!!Bl&&"webkitFontSmoothing"in Bl.documentElement.style,Xu=ar?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Zu(n){let e=n.defaultView&&n.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.documentElement.clientWidth,top:0,bottom:n.documentElement.clientHeight}}function dt(n,e){return typeof n=="number"?n:n[e]}function Qu(n){let e=n.getBoundingClientRect(),t=e.width/n.offsetWidth||1,r=e.height/n.offsetHeight||1;return{left:e.left,right:e.left+n.clientWidth*t,top:e.top,bottom:e.top+n.clientHeight*r}}function Ll(n,e,t){let r=n.someProp("scrollThreshold")||0,i=n.someProp("scrollMargin")||5,o=n.dom.ownerDocument;for(let s=t||n.dom;s;s=rr(s)){if(s.nodeType!=1)continue;let l=s,a=l==o.body,c=a?Zu(o):Qu(l),f=0,u=0;if(e.top<c.top+dt(r,"top")?u=-(c.top-e.top+dt(i,"top")):e.bottom>c.bottom-dt(r,"bottom")&&(u=e.bottom-e.top>c.bottom-c.top?e.top+dt(i,"top")-c.top:e.bottom-c.bottom+dt(i,"bottom")),e.left<c.left+dt(r,"left")?f=-(c.left-e.left+dt(i,"left")):e.right>c.right-dt(r,"right")&&(f=e.right-c.right+dt(i,"right")),f||u)if(a)o.defaultView.scrollBy(f,u);else{let d=l.scrollLeft,p=l.scrollTop;u&&(l.scrollTop+=u),f&&(l.scrollLeft+=f);let h=l.scrollLeft-d,m=l.scrollTop-p;e={left:e.left-h,top:e.top-m,right:e.right-h,bottom:e.bottom-m}}if(a||/^(fixed|sticky)$/.test(getComputedStyle(s).position))break}}function ed(n){let e=n.dom.getBoundingClientRect(),t=Math.max(0,e.top),r,i;for(let o=(e.left+e.right)/2,s=t+1;s<Math.min(innerHeight,e.bottom);s+=5){let l=n.root.elementFromPoint(o,s);if(!l||l==n.dom||!n.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){r=l,i=a.top;break}}return{refDOM:r,refTop:i,stack:ma(n.dom)}}function ma(n){let e=[],t=n.ownerDocument;for(let r=n;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),n!=t);r=rr(r));return e}function td({refDOM:n,refTop:e,stack:t}){let r=n?n.getBoundingClientRect().top:0;ga(t,r==0?0:r-e)}function ga(n,e){for(let t=0;t<n.length;t++){let{dom:r,top:i,left:o}=n[t];r.scrollTop!=i+e&&(r.scrollTop=i+e),r.scrollLeft!=o&&(r.scrollLeft=o)}}var vn=null;function nd(n){if(n.setActive)return n.setActive();if(vn)return n.focus(vn);let e=ma(n);n.focus(vn==null?{get preventScroll(){return vn={preventScroll:!0},!0}}:void 0),vn||(vn=!1,ga(e,0))}function ya(n,e){let t,r=2e8,i,o=0,s=e.top,l=e.top,a,c;for(let f=n.firstChild,u=0;f;f=f.nextSibling,u++){let d;if(f.nodeType==1)d=f.getClientRects();else if(f.nodeType==3)d=pt(f).getClientRects();else continue;for(let p=0;p<d.length;p++){let h=d[p];if(h.top<=s&&h.bottom>=l){s=Math.max(h.bottom,s),l=Math.min(h.top,l);let m=h.left>e.left?h.left-e.left:h.right<e.left?e.left-h.right:0;if(m<r){t=f,r=m,i=m&&t.nodeType==3?{left:h.right<e.left?h.right:h.left,top:e.top}:e,f.nodeType==1&&m&&(o=u+(e.left>=(h.left+h.right)/2?1:0));continue}}else h.top>e.top&&!a&&h.left<=e.left&&h.right>=e.left&&(a=f,c={left:Math.max(h.left,Math.min(h.right,e.left)),top:h.top});!t&&(e.left>=h.right&&e.top>=h.top||e.left>=h.left&&e.top>=h.bottom)&&(o=u+1)}}return!t&&a&&(t=a,i=c,r=0),t&&t.nodeType==3?rd(t,i):!t||r&&t.nodeType==1?{node:n,offset:o}:ya(t,i)}function rd(n,e){let t=n.nodeValue.length,r=document.createRange();for(let i=0;i<t;i++){r.setEnd(n,i+1),r.setStart(n,i);let o=St(r,1);if(o.top!=o.bottom&&Oo(e,o))return{node:n,offset:i+(e.left>=(o.left+o.right)/2?1:0)}}return{node:n,offset:0}}function Oo(n,e){return n.left>=e.left-1&&n.left<=e.right+1&&n.top>=e.top-1&&n.top<=e.bottom+1}function id(n,e){let t=n.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<n.getBoundingClientRect().left?t:n}function od(n,e,t){let{node:r,offset:i}=ya(e,t),o=-1;if(r.nodeType==1&&!r.firstChild){let s=r.getBoundingClientRect();o=s.left!=s.right&&t.left>(s.left+s.right)/2?1:-1}return n.docView.posFromDOM(r,i,o)}function sd(n,e,t,r){let i=-1;for(let o=e,s=!1;o!=n.dom;){let l=n.docView.nearestDesc(o,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&(!s&&a.left>r.left||a.top>r.top?i=l.posBefore:(!s&&a.right<r.left||a.bottom<r.top)&&(i=l.posAfter),s=!0),!l.contentDOM&&i<0&&!l.node.isText))return(l.node.isBlock?r.top<(a.top+a.bottom)/2:r.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;o=l.dom.parentNode}return i>-1?i:n.docView.posFromDOM(e,t,-1)}function ba(n,e,t){let r=n.childNodes.length;if(r&&t.top<t.bottom)for(let i=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-t.top)/(t.bottom-t.top))-2)),o=i;;){let s=n.childNodes[o];if(s.nodeType==1){let l=s.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(Oo(e,c))return ba(s,e,c)}}if((o=(o+1)%r)==i)break}return n}function ld(n,e){let t=n.dom.ownerDocument,r,i=0,o=Gu(t,e.left,e.top);o&&({node:r,offset:i}=o);let s=(n.root.elementFromPoint?n.root:t).elementFromPoint(e.left,e.top),l;if(!s||!n.dom.contains(s.nodeType!=1?s.parentNode:s)){let c=n.dom.getBoundingClientRect();if(!Oo(e,c)||(s=ba(n.dom,e,c),!s))return null}if(Me)for(let c=s;r&&c;c=rr(c))c.draggable&&(r=void 0);if(s=id(s,e),r){if(Je&&r.nodeType==1&&(i=Math.min(i,r.childNodes.length),i<r.childNodes.length)){let f=r.childNodes[i],u;f.nodeName=="IMG"&&(u=f.getBoundingClientRect()).right<=e.left&&u.bottom>e.top&&i++}let c;ar&&i&&r.nodeType==1&&(c=r.childNodes[i-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&i--,r==n.dom&&i==r.childNodes.length-1&&r.lastChild.nodeType==1&&e.top>r.lastChild.getBoundingClientRect().bottom?l=n.state.doc.content.size:(i==0||r.nodeType!=1||r.childNodes[i-1].nodeName!="BR")&&(l=sd(n,r,i,e))}l==null&&(l=od(n,s,e));let a=n.docView.nearestDesc(s,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function Fl(n){return n.top<n.bottom||n.left<n.right}function St(n,e){let t=n.getClientRects();if(t.length){let r=t[e<0?0:t.length-1];if(Fl(r))return r}return Array.prototype.find.call(t,Fl)||n.getBoundingClientRect()}var ad=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function va(n,e,t){let{node:r,offset:i,atom:o}=n.docView.domFromPos(e,t<0?-1:1),s=ar||Je;if(r.nodeType==3)if(s&&(ad.test(r.nodeValue)||(t<0?!i:i==r.nodeValue.length))){let a=St(pt(r,i,i),t);if(Je&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let c=St(pt(r,i-1,i-1),-1);if(c.top==a.top){let f=St(pt(r,i,i+1),-1);if(f.top!=a.top)return er(f,f.left<c.left)}}return a}else{let a=i,c=i,f=t<0?1:-1;return t<0&&!i?(c++,f=-1):t>=0&&i==r.nodeValue.length?(a--,f=1):t<0?a--:c++,er(St(pt(r,a,c),f),f<0)}if(!n.state.doc.resolve(e-(o||0)).parent.inlineContent){if(o==null&&i&&(t<0||i==Fe(r))){let a=r.childNodes[i-1];if(a.nodeType==1)return no(a.getBoundingClientRect(),!1)}if(o==null&&i<Fe(r)){let a=r.childNodes[i];if(a.nodeType==1)return no(a.getBoundingClientRect(),!0)}return no(r.getBoundingClientRect(),t>=0)}if(o==null&&i&&(t<0||i==Fe(r))){let a=r.childNodes[i-1],c=a.nodeType==3?pt(a,Fe(a)-(s?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return er(St(c,1),!1)}if(o==null&&i<Fe(r)){let a=r.childNodes[i];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?pt(a,0,s?0:1):a.nodeType==1?a:null:null;if(c)return er(St(c,-1),!0)}return er(St(r.nodeType==3?pt(r):r,-t),t>=0)}function er(n,e){if(n.width==0)return n;let t=e?n.left:n.right;return{top:n.top,bottom:n.bottom,left:t,right:t}}function no(n,e){if(n.height==0)return n;let t=e?n.top:n.bottom;return{top:t,bottom:t,left:n.left,right:n.right}}function xa(n,e,t){let r=n.state,i=n.root.activeElement;r!=e&&n.updateState(e),i!=n.dom&&n.focus();try{return t()}finally{r!=e&&n.updateState(r),i!=n.dom&&i&&i.focus()}}function cd(n,e,t){let r=e.selection,i=t=="up"?r.$from:r.$to;return xa(n,e,()=>{let{node:o}=n.docView.domFromPos(i.pos,t=="up"?-1:1);for(;;){let l=n.docView.nearestDesc(o,!0);if(!l)break;if(l.node.isBlock){o=l.contentDOM||l.dom;break}o=l.dom.parentNode}let s=va(n,i.pos,1);for(let l=o.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=pt(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let f=a[c];if(f.bottom>f.top+1&&(t=="up"?s.top-f.top>(f.bottom-s.top)*2:f.bottom-s.bottom>(s.bottom-f.top)*2))return!1}}return!0})}var fd=/[\u0590-\u08ac]/;function ud(n,e,t){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,o=!i,s=i==r.parent.content.size,l=n.domSelection();return l?!fd.test(r.parent.textContent)||!l.modify?t=="left"||t=="backward"?o:s:xa(n,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:f,anchorOffset:u}=n.domSelectionRange(),d=l.caretBidiLevel;l.modify("move",t,"character");let p=r.depth?n.docView.domAfterPos(r.before()):n.dom,{focusNode:h,focusOffset:m}=n.domSelectionRange(),g=h&&!p.contains(h.nodeType==1?h:h.parentNode)||a==h&&c==m;try{l.collapse(f,u),a&&(a!=f||c!=u)&&l.extend&&l.extend(a,c)}catch{}return d!=null&&(l.caretBidiLevel=d),g}):r.pos==r.start()||r.pos==r.end()}var zl=null,Vl=null,$l=!1;function dd(n,e,t){return zl==e&&Vl==t?$l:(zl=e,Vl=t,$l=t=="up"||t=="down"?cd(n,e,t):ud(n,e,t))}var ze=0,Hl=1,jt=2,Ze=3,Gt=class{constructor(e,t,r,i){this.parent=e,this.children=t,this.dom=r,this.contentDOM=i,this.dirty=ze,r.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,r){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,r=this.posAtStart;;t++){let i=this.children[t];if(i==e)return r;r+=i.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,r){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(r<0){let o,s;if(e==this.contentDOM)o=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;o=e.previousSibling}for(;o&&!((s=o.pmViewDesc)&&s.parent==this);)o=o.previousSibling;return o?this.posBeforeChild(s)+s.size:this.posAtStart}else{let o,s;if(e==this.contentDOM)o=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;o=e.nextSibling}for(;o&&!((s=o.pmViewDesc)&&s.parent==this);)o=o.nextSibling;return o?this.posBeforeChild(s):this.posAtEnd}let i;if(e==this.dom&&this.contentDOM)i=t>pe(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))i=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let o=e;;o=o.parentNode){if(o==this.dom){i=!1;break}if(o.previousSibling)break}if(i==null&&t==e.childNodes.length)for(let o=e;;o=o.parentNode){if(o==this.dom){i=!0;break}if(o.nextSibling)break}}return i??r>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let r=!0,i=e;i;i=i.parentNode){let o=this.getDesc(i),s;if(o&&(!t||o.node))if(r&&(s=o.nodeDOM)&&!(s.nodeType==1?s.contains(e.nodeType==1?e:e.parentNode):s==e))r=!1;else return o}}getDesc(e){let t=e.pmViewDesc;for(let r=t;r;r=r.parent)if(r==this)return t}posFromDOM(e,t,r){for(let i=e;i;i=i.parentNode){let o=this.getDesc(i);if(o)return o.localPosFromDOM(e,t,r)}return-1}descAt(e){for(let t=0,r=0;t<this.children.length;t++){let i=this.children[t],o=r+i.size;if(r==e&&o!=r){for(;!i.border&&i.children.length;)i=i.children[0];return i}if(e<o)return i.descAt(e-r-i.border);r=o}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let r=0,i=0;for(let o=0;r<this.children.length;r++){let s=this.children[r],l=o+s.size;if(l>e||s instanceof Gr){i=e-o;break}o=l}if(i)return this.children[r].domFromPos(i-this.children[r].border,t);for(let o;r&&!(o=this.children[r-1]).size&&o instanceof Kr&&o.side>=0;r--);if(t<=0){let o,s=!0;for(;o=r?this.children[r-1]:null,!(!o||o.dom.parentNode==this.contentDOM);r--,s=!1);return o&&t&&s&&!o.border&&!o.domAtom?o.domFromPos(o.size,t):{node:this.contentDOM,offset:o?pe(o.dom)+1:0}}else{let o,s=!0;for(;o=r<this.children.length?this.children[r]:null,!(!o||o.dom.parentNode==this.contentDOM);r++,s=!1);return o&&s&&!o.border&&!o.domAtom?o.domFromPos(0,t):{node:this.contentDOM,offset:o?pe(o.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,r=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let i=-1,o=-1;for(let s=r,l=0;;l++){let a=this.children[l],c=s+a.size;if(i==-1&&e<=c){let f=s+a.border;if(e>=f&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,f);e=s;for(let u=l;u>0;u--){let d=this.children[u-1];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(1)){i=pe(d.dom)+1;break}e-=d.size}i==-1&&(i=0)}if(i>-1&&(c>t||l==this.children.length-1)){t=c;for(let f=l+1;f<this.children.length;f++){let u=this.children[f];if(u.size&&u.dom.parentNode==this.contentDOM&&!u.emptyChildAt(-1)){o=pe(u.dom);break}t+=u.size}o==-1&&(o=this.contentDOM.childNodes.length);break}s=c}return{node:this.contentDOM,from:e,to:t,fromOffset:i,toOffset:o}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:r}=this.domFromPos(e,0);if(t.nodeType!=1||r==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[r]}setSelection(e,t,r,i=!1){let o=Math.min(e,t),s=Math.max(e,t);for(let p=0,h=0;p<this.children.length;p++){let m=this.children[p],g=h+m.size;if(o>h&&s<g)return m.setSelection(e-h-m.border,t-h-m.border,r,i);h=g}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=r.root.getSelection(),f=r.domSelectionRange(),u=!1;if((Je||Me)&&e==t){let{node:p,offset:h}=l;if(p.nodeType==3){if(u=!!(h&&p.nodeValue[h-1]==`
`),u&&h==p.nodeValue.length)for(let m=p,g;m;m=m.parentNode){if(g=m.nextSibling){g.nodeName=="BR"&&(l=a={node:g.parentNode,offset:pe(g)+1});break}let b=m.pmViewDesc;if(b&&b.node&&b.node.isBlock)break}}else{let m=p.childNodes[h-1];u=m&&(m.nodeName=="BR"||m.contentEditable=="false")}}if(Je&&f.focusNode&&f.focusNode!=a.node&&f.focusNode.nodeType==1){let p=f.focusNode.childNodes[f.focusOffset];p&&p.contentEditable=="false"&&(i=!0)}if(!(i||u&&Me)&&Ut(l.node,l.offset,f.anchorNode,f.anchorOffset)&&Ut(a.node,a.offset,f.focusNode,f.focusOffset))return;let d=!1;if((c.extend||e==t)&&!u){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),d=!0}catch{}}if(!d){if(e>t){let h=l;l=a,a=h}let p=document.createRange();p.setEnd(a.node,a.offset),p.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(p)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let r=0,i=0;i<this.children.length;i++){let o=this.children[i],s=r+o.size;if(r==s?e<=s&&t>=r:e<s&&t>r){let l=r+o.border,a=s-o.border;if(e>=l&&t<=a){this.dirty=e==r||t==s?jt:Hl,e==l&&t==a&&(o.contentLost||o.dom.parentNode!=this.contentDOM)?o.dirty=Ze:o.markDirty(e-l,t-l);return}else o.dirty=o.dom==o.contentDOM&&o.dom.parentNode==this.contentDOM&&!o.children.length?jt:Ze}r=s}this.dirty=jt}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let r=e==1?jt:Hl;t.dirty<r&&(t.dirty=r)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}},Kr=class extends Gt{constructor(e,t,r,i){let o,s=t.type.toDOM;if(typeof s=="function"&&(s=s(r,()=>{if(!o)return i;if(o.parent)return o.parent.posBeforeChild(o)})),!t.type.spec.raw){if(s.nodeType!=1){let l=document.createElement("span");l.appendChild(s),s=l}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(e,[],s,null),this.widget=t,this.widget=t,o=this}matchesWidget(e){return this.dirty==ze&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}},uo=class extends Gt{constructor(e,t,r,i){super(e,[],t,null),this.textDOM=r,this.text=i}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}},Mn=class n extends Gt{constructor(e,t,r,i,o){super(e,[],r,i),this.mark=t,this.spec=o}static create(e,t,r,i){let o=i.nodeViews[t.type.name],s=o&&o(t,i,r);return(!s||!s.dom)&&(s=ct.renderSpec(document,t.type.spec.toDOM(t,r),null,t.attrs)),new n(e,t,s.dom,s.contentDOM||s.dom,s)}parseRule(){return this.dirty&Ze||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=Ze&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=ze){let r=this.parent;for(;!r.node;)r=r.parent;r.dirty<this.dirty&&(r.dirty=this.dirty),this.dirty=ze}}slice(e,t,r){let i=n.create(this.parent,this.mark,!0,r),o=this.children,s=this.size;t<s&&(o=go(o,t,s,r)),e>0&&(o=go(o,0,e,r));for(let l=0;l<o.length;l++)o[l].parent=i;return i.children=o,i}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}},Ot=class n extends Gt{constructor(e,t,r,i,o,s,l,a,c){super(e,[],o,s),this.node=t,this.outerDeco=r,this.innerDeco=i,this.nodeDOM=l}static create(e,t,r,i,o,s){let l=o.nodeViews[t.type.name],a,c=l&&l(t,o,()=>{if(!a)return s;if(a.parent)return a.parent.posBeforeChild(a)},r,i),f=c&&c.dom,u=c&&c.contentDOM;if(t.isText){if(!f)f=document.createTextNode(t.text);else if(f.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else f||({dom:f,contentDOM:u}=ct.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs));!u&&!t.isText&&f.nodeName!="BR"&&(f.hasAttribute("contenteditable")||(f.contentEditable="false"),t.type.spec.draggable&&(f.draggable=!0));let d=f;return f=wa(f,r,t),c?a=new po(e,t,r,i,f,u||null,d,c,o,s+1):t.isText?new Ur(e,t,r,i,f,d,o):new n(e,t,r,i,f,u||null,d,o,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let r=this.children[t];if(this.dom.contains(r.dom.parentNode)){e.contentElement=r.dom.parentNode;break}}e.contentElement||(e.getContent=()=>S.empty)}return e}matchesNode(e,t,r){return this.dirty==ze&&e.eq(this.node)&&Yr(t,this.outerDeco)&&r.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let r=this.node.inlineContent,i=t,o=e.composing?this.localCompositionInfo(e,t):null,s=o&&o.pos>-1?o:null,l=o&&o.pos<0,a=new mo(this,s&&s.node,e);gd(this.node,this.innerDeco,(c,f,u)=>{c.spec.marks?a.syncToMarks(c.spec.marks,r,e):c.type.side>=0&&!u&&a.syncToMarks(f==this.node.childCount?$.none:this.node.child(f).marks,r,e),a.placeWidget(c,e,i)},(c,f,u,d)=>{a.syncToMarks(c.marks,r,e);let p;a.findNodeMatch(c,f,u,d)||l&&e.state.selection.from>i&&e.state.selection.to<i+c.nodeSize&&(p=a.findIndexWithChild(o.node))>-1&&a.updateNodeAt(c,f,u,p,e)||a.updateNextNode(c,f,u,e,d,i)||a.addNode(c,f,u,e,i),i+=c.nodeSize}),a.syncToMarks([],r,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==jt)&&(s&&this.protectLocalComposition(e,s),ka(this.contentDOM,this.children,e),wn&&yd(this.dom))}localCompositionInfo(e,t){let{from:r,to:i}=e.state.selection;if(!(e.state.selection instanceof P)||r<t||i>t+this.node.content.size)return null;let o=e.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(this.node.inlineContent){let s=o.nodeValue,l=bd(this.node.content,s,r-t,i-t);return l<0?null:{node:o,pos:l,text:s}}else return{node:o,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:r,text:i}){if(this.getDesc(t))return;let o=t;for(;o.parentNode!=this.contentDOM;o=o.parentNode){for(;o.previousSibling;)o.parentNode.removeChild(o.previousSibling);for(;o.nextSibling;)o.parentNode.removeChild(o.nextSibling);o.pmViewDesc&&(o.pmViewDesc=void 0)}let s=new uo(this,o,t,i);e.input.compositionNodes.push(s),this.children=go(this.children,r,r+i.length,e,s)}update(e,t,r,i){return this.dirty==Ze||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,r,i),!0)}updateInner(e,t,r,i){this.updateOuterDeco(t),this.node=e,this.innerDeco=r,this.contentDOM&&this.updateChildren(i,this.posAtStart),this.dirty=ze}updateOuterDeco(e){if(Yr(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,r=this.dom;this.dom=Sa(this.dom,this.nodeDOM,ho(this.outerDeco,this.node,t),ho(e,this.node,t)),this.dom!=r&&(r.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}};function Wl(n,e,t,r,i){wa(r,e,n);let o=new Ot(void 0,n,e,t,r,r,r,i,0);return o.contentDOM&&o.updateChildren(i,0),o}var Ur=class n extends Ot{constructor(e,t,r,i,o,s,l){super(e,t,r,i,o,null,s,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,r,i){return this.dirty==Ze||this.dirty!=ze&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=ze||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,i.trackWrites==this.nodeDOM&&(i.trackWrites=null)),this.node=e,this.dirty=ze,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,r){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,r)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,r){let i=this.node.cut(e,t),o=document.createTextNode(i.text);return new n(this.parent,i,this.outerDeco,this.innerDeco,o,o,r)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=Ze)}get domAtom(){return!1}isText(e){return this.node.text==e}},Gr=class extends Gt{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==ze&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}},po=class extends Ot{constructor(e,t,r,i,o,s,l,a,c,f){super(e,t,r,i,o,s,l,c,f),this.spec=a}update(e,t,r,i){if(this.dirty==Ze)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let o=this.spec.update(e,t,r);return o&&this.updateInner(e,t,r,i),o}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,r,i)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,r,i){this.spec.setSelection?this.spec.setSelection(e,t,r.root):super.setSelection(e,t,r,i)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}};function ka(n,e,t){let r=n.firstChild,i=!1;for(let o=0;o<e.length;o++){let s=e[o],l=s.dom;if(l.parentNode==n){for(;l!=r;)r=jl(r),i=!0;r=r.nextSibling}else i=!0,n.insertBefore(l,r);if(s instanceof Mn){let a=r?r.previousSibling:n.lastChild;ka(s.contentDOM,s.children,t),r=a?a.nextSibling:n.firstChild}}for(;r;)r=jl(r),i=!0;i&&t.trackWrites==n&&(t.trackWrites=null)}var tr=function(n){n&&(this.nodeName=n)};tr.prototype=Object.create(null);var qt=[new tr];function ho(n,e,t){if(n.length==0)return qt;let r=t?qt[0]:new tr,i=[r];for(let o=0;o<n.length;o++){let s=n[o].type.attrs;if(s){s.nodeName&&i.push(r=new tr(s.nodeName));for(let l in s){let a=s[l];a!=null&&(t&&i.length==1&&i.push(r=new tr(e.isInline?"span":"div")),l=="class"?r.class=(r.class?r.class+" ":"")+a:l=="style"?r.style=(r.style?r.style+";":"")+a:l!="nodeName"&&(r[l]=a))}}}return i}function Sa(n,e,t,r){if(t==qt&&r==qt)return e;let i=e;for(let o=0;o<r.length;o++){let s=r[o],l=t[o];if(o){let a;l&&l.nodeName==s.nodeName&&i!=n&&(a=i.parentNode)&&a.nodeName.toLowerCase()==s.nodeName||(a=document.createElement(s.nodeName),a.pmIsDeco=!0,a.appendChild(i),l=qt[0]),i=a}pd(i,l||qt[0],s)}return i}function pd(n,e,t){for(let r in e)r!="class"&&r!="style"&&r!="nodeName"&&!(r in t)&&n.removeAttribute(r);for(let r in t)r!="class"&&r!="style"&&r!="nodeName"&&t[r]!=e[r]&&n.setAttribute(r,t[r]);if(e.class!=t.class){let r=e.class?e.class.split(" ").filter(Boolean):[],i=t.class?t.class.split(" ").filter(Boolean):[];for(let o=0;o<r.length;o++)i.indexOf(r[o])==-1&&n.classList.remove(r[o]);for(let o=0;o<i.length;o++)r.indexOf(i[o])==-1&&n.classList.add(i[o]);n.classList.length==0&&n.removeAttribute("class")}if(e.style!=t.style){if(e.style){let r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,i;for(;i=r.exec(e.style);)n.style.removeProperty(i[1])}t.style&&(n.style.cssText+=t.style)}}function wa(n,e,t){return Sa(n,n,qt,ho(e,t,n.nodeType!=1))}function Yr(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].type.eq(e[t].type))return!1;return!0}function jl(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}var mo=class{constructor(e,t,r){this.lock=t,this.view=r,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=hd(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let r=e;r<t;r++)this.top.children[r].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,r){let i=0,o=this.stack.length>>1,s=Math.min(o,e.length);for(;i<s&&(i==o-1?this.top:this.stack[i+1<<1]).matchesMark(e[i])&&e[i].type.spec.spanning!==!1;)i++;for(;i<o;)this.destroyRest(),this.top.dirty=ze,this.index=this.stack.pop(),this.top=this.stack.pop(),o--;for(;o<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[o])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=Mn.create(this.top,e[o],t,r);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,o++}}findNodeMatch(e,t,r,i){let o=-1,s;if(i>=this.preMatch.index&&(s=this.preMatch.matches[i-this.preMatch.index]).parent==this.top&&s.matchesNode(e,t,r))o=this.top.children.indexOf(s,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,r)&&!this.preMatch.matched.has(c)){o=l;break}}return o<0?!1:(this.destroyBetween(this.index,o),this.index++,!0)}updateNodeAt(e,t,r,i,o){let s=this.top.children[i];return s.dirty==Ze&&s.dom==s.contentDOM&&(s.dirty=jt),s.update(e,t,r,o)?(this.destroyBetween(this.index,i),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let r=e.pmViewDesc;if(r){for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==r)return i}return-1}e=t}}updateNextNode(e,t,r,i,o,s){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof Ot){let c=this.preMatch.matched.get(a);if(c!=null&&c!=o)return!1;let f=a.dom,u,d=this.isLocked(f)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=Ze&&Yr(t,a.outerDeco));if(!d&&a.update(e,t,r,i))return this.destroyBetween(this.index,l),a.dom!=f&&(this.changed=!0),this.index++,!0;if(!d&&(u=this.recreateWrapper(a,e,t,r,i,s)))return this.destroyBetween(this.index,l),this.top.children[this.index]=u,u.contentDOM&&(u.dirty=jt,u.updateChildren(i,s+1),u.dirty=ze),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,r,i,o,s){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!Yr(r,e.outerDeco)||!i.eq(e.innerDeco))return null;let l=Ot.create(this.top,t,r,i,o,s);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,r,i,o){let s=Ot.create(this.top,e,t,r,i,o);s.contentDOM&&s.updateChildren(i,o+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(e,t,r){let i=this.index<this.top.children.length?this.top.children[this.index]:null;if(i&&i.matchesWidget(e)&&(e==i.widget||!i.widget.type.toDOM.parentNode))this.index++;else{let o=new Kr(this.top,e,t,r);this.top.children.splice(this.index++,0,o),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof Mn;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof Ur)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((Me||xe)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let r=document.createElement(e);e=="IMG"&&(r.className="ProseMirror-separator",r.alt=""),e=="BR"&&(r.className="ProseMirror-trailingBreak");let i=new Gr(this.top,[],r,null);t!=this.top?t.children.push(i):t.children.splice(this.index++,0,i),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}};function hd(n,e){let t=e,r=t.children.length,i=n.childCount,o=new Map,s=[];e:for(;i>0;){let l;for(;;)if(r){let c=t.children[r-1];if(c instanceof Mn)t=c,r=c.children.length;else{l=c,r--;break}}else{if(t==e)break e;r=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=n.child(i-1))break;--i,o.set(l,i),s.push(l)}}return{index:i,matched:o,matches:s.reverse()}}function md(n,e){return n.type.side-e.type.side}function gd(n,e,t,r){let i=e.locals(n),o=0;if(i.length==0){for(let c=0;c<n.childCount;c++){let f=n.child(c);r(f,i,e.forChild(o,f),c),o+=f.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let f,u;for(;s<i.length&&i[s].to==o;){let g=i[s++];g.widget&&(f?(u||(u=[f])).push(g):f=g)}if(f)if(u){u.sort(md);for(let g=0;g<u.length;g++)t(u[g],c,!!a)}else t(f,c,!!a);let d,p;if(a)p=-1,d=a,a=null;else if(c<n.childCount)p=c,d=n.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=o&&l.splice(g--,1);for(;s<i.length&&i[s].from<=o&&i[s].to>o;)l.push(i[s++]);let h=o+d.nodeSize;if(d.isText){let g=h;s<i.length&&i[s].from<g&&(g=i[s].from);for(let b=0;b<l.length;b++)l[b].to<g&&(g=l[b].to);g<h&&(a=d.cut(g-o),d=d.cut(0,g-o),h=g,p=-1)}else for(;s<i.length&&i[s].to<h;)s++;let m=d.isInline&&!d.isLeaf?l.filter(g=>!g.inline):l.slice();r(d,m,e.forChild(o,d),p),o=h}}function yd(n){if(n.nodeName=="UL"||n.nodeName=="OL"){let e=n.style.cssText;n.style.cssText=e+"; list-style: square !important",window.getComputedStyle(n).listStyle,n.style.cssText=e}}function bd(n,e,t,r){for(let i=0,o=0;i<n.childCount&&o<=r;){let s=n.child(i++),l=o;if(o+=s.nodeSize,!s.isText)continue;let a=s.text;for(;i<n.childCount;){let c=n.child(i++);if(o+=c.nodeSize,!c.isText)break;a+=c.text}if(o>=t){if(o>=r&&a.slice(r-e.length-l,r-l)==e)return r-e.length;let c=l<r?a.lastIndexOf(e,r-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==r&&a.length>=r+e.length-l&&a.slice(r-l,r-l+e.length)==e)return r}}return-1}function go(n,e,t,r,i){let o=[];for(let s=0,l=0;s<n.length;s++){let a=n[s],c=l,f=l+=a.size;c>=t||f<=e?o.push(a):(c<e&&o.push(a.slice(0,e-c,r)),i&&(o.push(i),i=void 0),f>t&&o.push(a.slice(t-c,a.size,r)))}return o}function To(n,e=null){let t=n.domSelectionRange(),r=n.state.doc;if(!t.focusNode)return null;let i=n.docView.nearestDesc(t.focusNode),o=i&&i.size==0,s=n.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(s<0)return null;let l=r.resolve(s),a,c;if(ii(t)){for(a=s;i&&!i.node;)i=i.parent;let u=i.node;if(i&&u.isAtom&&A.isSelectable(u)&&i.parent&&!(u.isInline&&Ku(t.focusNode,t.focusOffset,i.dom))){let d=i.posBefore;c=new A(s==d?l:r.resolve(d))}}else{if(t instanceof n.dom.ownerDocument.defaultView.Selection&&t.rangeCount>1){let u=s,d=s;for(let p=0;p<t.rangeCount;p++){let h=t.getRangeAt(p);u=Math.min(u,n.docView.posFromDOM(h.startContainer,h.startOffset,1)),d=Math.max(d,n.docView.posFromDOM(h.endContainer,h.endOffset,-1))}if(u<0)return null;[a,s]=d==n.state.selection.anchor?[d,u]:[u,d],l=r.resolve(s)}else a=n.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(a<0)return null}let f=r.resolve(a);if(!c){let u=e=="pointer"||n.state.selection.head<l.pos&&!o?1:-1;c=Eo(n,f,l,u)}return c}function Ma(n){return n.editable?n.hasFocus():Oa(n)&&document.activeElement&&document.activeElement.contains(n.dom)}function mt(n,e=!1){let t=n.state.selection;if(Ca(n,t),!!Ma(n)){if(!e&&n.input.mouseDown&&n.input.mouseDown.allowDefault&&xe){let r=n.domSelectionRange(),i=n.domObserver.currentSelection;if(r.anchorNode&&i.anchorNode&&Ut(r.anchorNode,r.anchorOffset,i.anchorNode,i.anchorOffset)){n.input.mouseDown.delayedSelectionSync=!0,n.domObserver.setCurSelection();return}}if(n.domObserver.disconnectSelection(),n.cursorWrapper)xd(n);else{let{anchor:r,head:i}=t,o,s;ql&&!(t instanceof P)&&(t.$from.parent.inlineContent||(o=Jl(n,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(s=Jl(n,t.to))),n.docView.setSelection(r,i,n,e),ql&&(o&&_l(o),s&&_l(s)),t.visible?n.dom.classList.remove("ProseMirror-hideselection"):(n.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&vd(n))}n.domObserver.setCurSelection(),n.domObserver.connectSelection()}}var ql=Me||xe&&ha<63;function Jl(n,e){let{node:t,offset:r}=n.docView.domFromPos(e,0),i=r<t.childNodes.length?t.childNodes[r]:null,o=r?t.childNodes[r-1]:null;if(Me&&i&&i.contentEditable=="false")return ro(i);if((!i||i.contentEditable=="false")&&(!o||o.contentEditable=="false")){if(i)return ro(i);if(o)return ro(o)}}function ro(n){return n.contentEditable="true",Me&&n.draggable&&(n.draggable=!1,n.wasDraggable=!0),n}function _l(n){n.contentEditable="false",n.wasDraggable&&(n.draggable=!0,n.wasDraggable=null)}function vd(n){let e=n.dom.ownerDocument;e.removeEventListener("selectionchange",n.input.hideSelectionGuard);let t=n.domSelectionRange(),r=t.anchorNode,i=t.anchorOffset;e.addEventListener("selectionchange",n.input.hideSelectionGuard=()=>{(t.anchorNode!=r||t.anchorOffset!=i)&&(e.removeEventListener("selectionchange",n.input.hideSelectionGuard),setTimeout(()=>{(!Ma(n)||n.state.selection.visible)&&n.dom.classList.remove("ProseMirror-hideselection")},20))})}function xd(n){let e=n.domSelection(),t=document.createRange();if(!e)return;let r=n.cursorWrapper.dom,i=r.nodeName=="IMG";i?t.setStart(r.parentNode,pe(r)+1):t.setStart(r,0),t.collapse(!0),e.removeAllRanges(),e.addRange(t),!i&&!n.state.selection.visible&&Ee&&Ct<=11&&(r.disabled=!0,r.disabled=!1)}function Ca(n,e){if(e instanceof A){let t=n.docView.descAt(e.from);t!=n.lastSelectedViewDesc&&(Kl(n),t&&t.selectNode(),n.lastSelectedViewDesc=t)}else Kl(n)}function Kl(n){n.lastSelectedViewDesc&&(n.lastSelectedViewDesc.parent&&n.lastSelectedViewDesc.deselectNode(),n.lastSelectedViewDesc=void 0)}function Eo(n,e,t,r){return n.someProp("createSelectionBetween",i=>i(n,e,t))||P.between(e,t,r)}function Ul(n){return n.editable&&!n.hasFocus()?!1:Oa(n)}function Oa(n){let e=n.domSelectionRange();if(!e.anchorNode)return!1;try{return n.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(n.editable||n.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function kd(n){let e=n.docView.domFromPos(n.state.selection.anchor,0),t=n.domSelectionRange();return Ut(e.node,e.offset,t.anchorNode,t.anchorOffset)}function yo(n,e){let{$anchor:t,$head:r}=n.selection,i=e>0?t.max(r):t.min(r),o=i.parent.inlineContent?i.depth?n.doc.resolve(e>0?i.after():i.before()):null:i;return o&&D.findFrom(o,e)}function wt(n,e){return n.dispatch(n.state.tr.setSelection(e).scrollIntoView()),!0}function Gl(n,e,t){let r=n.state.selection;if(r instanceof P)if(t.indexOf("s")>-1){let{$head:i}=r,o=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let s=n.state.doc.resolve(i.pos+o.nodeSize*(e<0?-1:1));return wt(n,new P(r.$anchor,s))}else if(r.empty){if(n.endOfTextblock(e>0?"forward":"backward")){let i=yo(n.state,e);return i&&i instanceof A?wt(n,i):!1}else if(!(Le&&t.indexOf("m")>-1)){let i=r.$head,o=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter,s;if(!o||o.isText)return!1;let l=e<0?i.pos-o.nodeSize:i.pos;return o.isAtom||(s=n.docView.descAt(l))&&!s.contentDOM?A.isSelectable(o)?wt(n,new A(e<0?n.state.doc.resolve(i.pos-o.nodeSize):i)):ar?wt(n,new P(n.state.doc.resolve(e<0?l:l+o.nodeSize))):!1:!1}}else return!1;else{if(r instanceof A&&r.node.isInline)return wt(n,new P(e>0?r.$to:r.$from));{let i=yo(n.state,e);return i?wt(n,i):!1}}}function Xr(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function nr(n,e){let t=n.pmViewDesc;return t&&t.size==0&&(e<0||n.nextSibling||n.nodeName!="BR")}function xn(n,e){return e<0?Sd(n):wd(n)}function Sd(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i,o,s=!1;for(Je&&t.nodeType==1&&r<Xr(t)&&nr(t.childNodes[r],-1)&&(s=!0);;)if(r>0){if(t.nodeType!=1)break;{let l=t.childNodes[r-1];if(nr(l,-1))i=t,o=--r;else if(l.nodeType==3)t=l,r=t.nodeValue.length;else break}}else{if(Ta(t))break;{let l=t.previousSibling;for(;l&&nr(l,-1);)i=t.parentNode,o=pe(l),l=l.previousSibling;if(l)t=l,r=Xr(t);else{if(t=t.parentNode,t==n.dom)break;r=0}}}s?bo(n,t,r):i&&bo(n,i,o)}function wd(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i=Xr(t),o,s;for(;;)if(r<i){if(t.nodeType!=1)break;let l=t.childNodes[r];if(nr(l,1))o=t,s=++r;else break}else{if(Ta(t))break;{let l=t.nextSibling;for(;l&&nr(l,1);)o=l.parentNode,s=pe(l)+1,l=l.nextSibling;if(l)t=l,r=0,i=Xr(t);else{if(t=t.parentNode,t==n.dom)break;r=i=0}}}o&&bo(n,o,s)}function Ta(n){let e=n.pmViewDesc;return e&&e.node&&e.node.isBlock}function Md(n,e){for(;n&&e==n.childNodes.length&&!lr(n);)e=pe(n)+1,n=n.parentNode;for(;n&&e<n.childNodes.length;){let t=n.childNodes[e];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=0}}function Cd(n,e){for(;n&&!e&&!lr(n);)e=pe(n),n=n.parentNode;for(;n&&e;){let t=n.childNodes[e-1];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=n.childNodes.length}}function bo(n,e,t){if(e.nodeType!=3){let o,s;(s=Md(e,t))?(e=s,t=0):(o=Cd(e,t))&&(e=o,t=o.nodeValue.length)}let r=n.domSelection();if(!r)return;if(ii(r)){let o=document.createRange();o.setEnd(e,t),o.setStart(e,t),r.removeAllRanges(),r.addRange(o)}else r.extend&&r.extend(e,t);n.domObserver.setCurSelection();let{state:i}=n;setTimeout(()=>{n.state==i&&mt(n)},50)}function Yl(n,e){let t=n.state.doc.resolve(e);if(!(xe||Yu)&&t.parent.inlineContent){let i=n.coordsAtPos(e);if(e>t.start()){let o=n.coordsAtPos(e-1),s=(o.top+o.bottom)/2;if(s>i.top&&s<i.bottom&&Math.abs(o.left-i.left)>1)return o.left<i.left?"ltr":"rtl"}if(e<t.end()){let o=n.coordsAtPos(e+1),s=(o.top+o.bottom)/2;if(s>i.top&&s<i.bottom&&Math.abs(o.left-i.left)>1)return o.left>i.left?"ltr":"rtl"}}return getComputedStyle(n.dom).direction=="rtl"?"rtl":"ltr"}function Xl(n,e,t){let r=n.state.selection;if(r instanceof P&&!r.empty||t.indexOf("s")>-1||Le&&t.indexOf("m")>-1)return!1;let{$from:i,$to:o}=r;if(!i.parent.inlineContent||n.endOfTextblock(e<0?"up":"down")){let s=yo(n.state,e);if(s&&s instanceof A)return wt(n,s)}if(!i.parent.inlineContent){let s=e<0?i:o,l=r instanceof we?D.near(s,e):D.findFrom(s,e);return l?wt(n,l):!1}return!1}function Zl(n,e){if(!(n.state.selection instanceof P))return!0;let{$head:t,$anchor:r,empty:i}=n.state.selection;if(!t.sameParent(r))return!0;if(!i)return!1;if(n.endOfTextblock(e>0?"forward":"backward"))return!0;let o=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(o&&!o.isText){let s=n.state.tr;return e<0?s.delete(t.pos-o.nodeSize,t.pos):s.delete(t.pos,t.pos+o.nodeSize),n.dispatch(s),!0}return!1}function Ql(n,e,t){n.domObserver.stop(),e.contentEditable=t,n.domObserver.start()}function Od(n){if(!Me||n.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=n.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let r=e.firstChild;Ql(n,r,"true"),setTimeout(()=>Ql(n,r,"false"),20)}return!1}function Td(n){let e="";return n.ctrlKey&&(e+="c"),n.metaKey&&(e+="m"),n.altKey&&(e+="a"),n.shiftKey&&(e+="s"),e}function Ed(n,e){let t=e.keyCode,r=Td(e);if(t==8||Le&&t==72&&r=="c")return Zl(n,-1)||xn(n,-1);if(t==46&&!e.shiftKey||Le&&t==68&&r=="c")return Zl(n,1)||xn(n,1);if(t==13||t==27)return!0;if(t==37||Le&&t==66&&r=="c"){let i=t==37?Yl(n,n.state.selection.from)=="ltr"?-1:1:-1;return Gl(n,i,r)||xn(n,i)}else if(t==39||Le&&t==70&&r=="c"){let i=t==39?Yl(n,n.state.selection.from)=="ltr"?1:-1:1;return Gl(n,i,r)||xn(n,i)}else{if(t==38||Le&&t==80&&r=="c")return Xl(n,-1,r)||xn(n,-1);if(t==40||Le&&t==78&&r=="c")return Od(n)||Xl(n,1,r)||xn(n,1);if(r==(Le?"m":"c")&&(t==66||t==73||t==89||t==90))return!0}return!1}function Ea(n,e){n.someProp("transformCopied",p=>{e=p(e,n)});let t=[],{content:r,openStart:i,openEnd:o}=e;for(;i>1&&o>1&&r.childCount==1&&r.firstChild.childCount==1;){i--,o--;let p=r.firstChild;t.push(p.type.name,p.attrs!=p.type.defaultAttrs?p.attrs:null),r=p.content}let s=n.someProp("clipboardSerializer")||ct.fromSchema(n.state.schema),l=Ra(),a=l.createElement("div");a.appendChild(s.serializeFragment(r,{document:l}));let c=a.firstChild,f,u=0;for(;c&&c.nodeType==1&&(f=Ia[c.nodeName.toLowerCase()]);){for(let p=f.length-1;p>=0;p--){let h=l.createElement(f[p]);for(;a.firstChild;)h.appendChild(a.firstChild);a.appendChild(h),u++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${i} ${o}${u?` -${u}`:""} ${JSON.stringify(t)}`);let d=n.someProp("clipboardTextSerializer",p=>p(e,n))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:d,slice:e}}function Aa(n,e,t,r,i){let o=i.parent.type.spec.code,s,l;if(!t&&!e)return null;let a=e&&(r||o||!t);if(a){if(n.someProp("transformPastedText",d=>{e=d(e,o||r,n)}),o)return e?new M(S.from(n.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):M.empty;let u=n.someProp("clipboardTextParser",d=>d(e,i,r,n));if(u)l=u;else{let d=i.marks(),{schema:p}=n.state,h=ct.fromSchema(p);s=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=s.appendChild(document.createElement("p"));m&&g.appendChild(h.serializeNode(p.text(m,d)))})}}else n.someProp("transformPastedHTML",u=>{t=u(t,n)}),s=Pd(t),ar&&Id(s);let c=s&&s.querySelector("[data-pm-slice]"),f=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(f&&f[3])for(let u=+f[3];u>0;u--){let d=s.firstChild;for(;d&&d.nodeType!=1;)d=d.nextSibling;if(!d)break;s=d}if(l||(l=(n.someProp("clipboardParser")||n.someProp("domParser")||at.fromSchema(n.state.schema)).parseSlice(s,{preserveWhitespace:!!(a||f),context:i,ruleFromNode(d){return d.nodeName=="BR"&&!d.nextSibling&&d.parentNode&&!Ad.test(d.parentNode.nodeName)?{ignore:!0}:null}})),f)l=Rd(ea(l,+f[1],+f[2]),f[4]);else if(l=M.maxOpen(Nd(l.content,i),!0),l.openStart||l.openEnd){let u=0,d=0;for(let p=l.content.firstChild;u<l.openStart&&!p.type.spec.isolating;u++,p=p.firstChild);for(let p=l.content.lastChild;d<l.openEnd&&!p.type.spec.isolating;d++,p=p.lastChild);l=ea(l,u,d)}return n.someProp("transformPasted",u=>{l=u(l,n)}),l}var Ad=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Nd(n,e){if(n.childCount<2)return n;for(let t=e.depth;t>=0;t--){let i=e.node(t).contentMatchAt(e.index(t)),o,s=[];if(n.forEach(l=>{if(!s)return;let a=i.findWrapping(l.type),c;if(!a)return s=null;if(c=s.length&&o.length&&Da(a,o,l,s[s.length-1],0))s[s.length-1]=c;else{s.length&&(s[s.length-1]=Pa(s[s.length-1],o.length));let f=Na(l,a);s.push(f),i=i.matchType(f.type),o=a}}),s)return S.from(s)}return n}function Na(n,e,t=0){for(let r=e.length-1;r>=t;r--)n=e[r].create(null,S.from(n));return n}function Da(n,e,t,r,i){if(i<n.length&&i<e.length&&n[i]==e[i]){let o=Da(n,e,t,r.lastChild,i+1);if(o)return r.copy(r.content.replaceChild(r.childCount-1,o));if(r.contentMatchAt(r.childCount).matchType(i==n.length-1?t.type:n[i+1]))return r.copy(r.content.append(S.from(Na(t,n,i+1))))}}function Pa(n,e){if(e==0)return n;let t=n.content.replaceChild(n.childCount-1,Pa(n.lastChild,e-1)),r=n.contentMatchAt(n.childCount).fillBefore(S.empty,!0);return n.copy(t.append(r))}function vo(n,e,t,r,i,o){let s=e<0?n.firstChild:n.lastChild,l=s.content;return n.childCount>1&&(o=0),i<r-1&&(l=vo(l,e,t,r,i+1,o)),i>=t&&(l=e<0?s.contentMatchAt(0).fillBefore(l,o<=i).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(S.empty,!0))),n.replaceChild(e<0?0:n.childCount-1,s.copy(l))}function ea(n,e,t){return e<n.openStart&&(n=new M(vo(n.content,-1,e,n.openStart,0,n.openEnd),e,n.openEnd)),t<n.openEnd&&(n=new M(vo(n.content,1,t,n.openEnd,0,0),n.openStart,t)),n}var Ia={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},ta=null;function Ra(){return ta||(ta=document.implementation.createHTMLDocument("title"))}var io=null;function Dd(n){let e=window.trustedTypes;return e?(io||(io=e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t})),io.createHTML(n)):n}function Pd(n){let e=/^(\s*<meta [^>]*>)*/.exec(n);e&&(n=n.slice(e[0].length));let t=Ra().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(n),i;if((i=r&&Ia[r[1].toLowerCase()])&&(n=i.map(o=>"<"+o+">").join("")+n+i.map(o=>"</"+o+">").reverse().join("")),t.innerHTML=Dd(n),i)for(let o=0;o<i.length;o++)t=t.querySelector(i[o])||t;return t}function Id(n){let e=n.querySelectorAll(xe?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let r=e[t];r.childNodes.length==1&&r.textContent=="\xA0"&&r.parentNode&&r.parentNode.replaceChild(n.ownerDocument.createTextNode(" "),r)}}function Rd(n,e){if(!n.size)return n;let t=n.content.firstChild.type.schema,r;try{r=JSON.parse(e)}catch{return n}let{content:i,openStart:o,openEnd:s}=n;for(let l=r.length-2;l>=0;l-=2){let a=t.nodes[r[l]];if(!a||a.hasRequiredAttrs())break;i=S.from(a.create(r[l+1],i)),o++,s++}return new M(i,o,s)}var Ce={},Oe={},Bd={touchstart:!0,touchmove:!0},xo=class{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}};function Ld(n){for(let e in Ce){let t=Ce[e];n.dom.addEventListener(e,n.input.eventHandlers[e]=r=>{zd(n,r)&&!Ao(n,r)&&(n.editable||!(r.type in Oe))&&t(n,r)},Bd[e]?{passive:!0}:void 0)}Me&&n.dom.addEventListener("input",()=>null),ko(n)}function Mt(n,e){n.input.lastSelectionOrigin=e,n.input.lastSelectionTime=Date.now()}function Fd(n){n.domObserver.stop();for(let e in n.input.eventHandlers)n.dom.removeEventListener(e,n.input.eventHandlers[e]);clearTimeout(n.input.composingTimeout),clearTimeout(n.input.lastIOSEnterFallbackTimeout)}function ko(n){n.someProp("handleDOMEvents",e=>{for(let t in e)n.input.eventHandlers[t]||n.dom.addEventListener(t,n.input.eventHandlers[t]=r=>Ao(n,r))})}function Ao(n,e){return n.someProp("handleDOMEvents",t=>{let r=t[e.type];return r?r(n,e)||e.defaultPrevented:!1})}function zd(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=n.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function Vd(n,e){!Ao(n,e)&&Ce[e.type]&&(n.editable||!(e.type in Oe))&&Ce[e.type](n,e)}Oe.keydown=(n,e)=>{let t=e;if(n.input.shiftKey=t.keyCode==16||t.shiftKey,!La(n,t)&&(n.input.lastKeyCode=t.keyCode,n.input.lastKeyCodeTime=Date.now(),!(ht&&xe&&t.keyCode==13)))if(t.keyCode!=229&&n.domObserver.forceFlush(),wn&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let r=Date.now();n.input.lastIOSEnter=r,n.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{n.input.lastIOSEnter==r&&(n.someProp("handleKeyDown",i=>i(n,Wt(13,"Enter"))),n.input.lastIOSEnter=0)},200)}else n.someProp("handleKeyDown",r=>r(n,t))||Ed(n,t)?t.preventDefault():Mt(n,"key")};Oe.keyup=(n,e)=>{e.keyCode==16&&(n.input.shiftKey=!1)};Oe.keypress=(n,e)=>{let t=e;if(La(n,t)||!t.charCode||t.ctrlKey&&!t.altKey||Le&&t.metaKey)return;if(n.someProp("handleKeyPress",i=>i(n,t))){t.preventDefault();return}let r=n.state.selection;if(!(r instanceof P)||!r.$from.sameParent(r.$to)){let i=String.fromCharCode(t.charCode);!/[\r\n]/.test(i)&&!n.someProp("handleTextInput",o=>o(n,r.$from.pos,r.$to.pos,i))&&n.dispatch(n.state.tr.insertText(i).scrollIntoView()),t.preventDefault()}};function oi(n){return{left:n.clientX,top:n.clientY}}function $d(n,e){let t=e.x-n.clientX,r=e.y-n.clientY;return t*t+r*r<100}function No(n,e,t,r,i){if(r==-1)return!1;let o=n.state.doc.resolve(r);for(let s=o.depth+1;s>0;s--)if(n.someProp(e,l=>s>o.depth?l(n,t,o.nodeAfter,o.before(s),i,!0):l(n,t,o.node(s),o.before(s),i,!1)))return!0;return!1}function Sn(n,e,t){if(n.focused||n.focus(),n.state.selection.eq(e))return;let r=n.state.tr.setSelection(e);t=="pointer"&&r.setMeta("pointer",!0),n.dispatch(r)}function Hd(n,e){if(e==-1)return!1;let t=n.state.doc.resolve(e),r=t.nodeAfter;return r&&r.isAtom&&A.isSelectable(r)?(Sn(n,new A(t),"pointer"),!0):!1}function Wd(n,e){if(e==-1)return!1;let t=n.state.selection,r,i;t instanceof A&&(r=t.node);let o=n.state.doc.resolve(e);for(let s=o.depth+1;s>0;s--){let l=s>o.depth?o.nodeAfter:o.node(s);if(A.isSelectable(l)){r&&t.$from.depth>0&&s>=t.$from.depth&&o.before(t.$from.depth+1)==t.$from.pos?i=o.before(t.$from.depth):i=o.before(s);break}}return i!=null?(Sn(n,A.create(n.state.doc,i),"pointer"),!0):!1}function jd(n,e,t,r,i){return No(n,"handleClickOn",e,t,r)||n.someProp("handleClick",o=>o(n,e,r))||(i?Wd(n,t):Hd(n,t))}function qd(n,e,t,r){return No(n,"handleDoubleClickOn",e,t,r)||n.someProp("handleDoubleClick",i=>i(n,e,r))}function Jd(n,e,t,r){return No(n,"handleTripleClickOn",e,t,r)||n.someProp("handleTripleClick",i=>i(n,e,r))||_d(n,t,r)}function _d(n,e,t){if(t.button!=0)return!1;let r=n.state.doc;if(e==-1)return r.inlineContent?(Sn(n,P.create(r,0,r.content.size),"pointer"),!0):!1;let i=r.resolve(e);for(let o=i.depth+1;o>0;o--){let s=o>i.depth?i.nodeAfter:i.node(o),l=i.before(o);if(s.inlineContent)Sn(n,P.create(r,l+1,l+1+s.content.size),"pointer");else if(A.isSelectable(s))Sn(n,A.create(r,l),"pointer");else continue;return!0}}function Do(n){return Zr(n)}var Ba=Le?"metaKey":"ctrlKey";Ce.mousedown=(n,e)=>{let t=e;n.input.shiftKey=t.shiftKey;let r=Do(n),i=Date.now(),o="singleClick";i-n.input.lastClick.time<500&&$d(t,n.input.lastClick)&&!t[Ba]&&(n.input.lastClick.type=="singleClick"?o="doubleClick":n.input.lastClick.type=="doubleClick"&&(o="tripleClick")),n.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:o};let s=n.posAtCoords(oi(t));s&&(o=="singleClick"?(n.input.mouseDown&&n.input.mouseDown.done(),n.input.mouseDown=new So(n,s,t,!!r)):(o=="doubleClick"?qd:Jd)(n,s.pos,s.inside,t)?t.preventDefault():Mt(n,"pointer"))};var So=class{constructor(e,t,r,i){this.view=e,this.pos=t,this.event=r,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!r[Ba],this.allowDefault=r.shiftKey;let o,s;if(t.inside>-1)o=e.state.doc.nodeAt(t.inside),s=t.inside;else{let f=e.state.doc.resolve(t.pos);o=f.parent,s=f.depth?f.before():0}let l=i?null:r.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(r.button==0&&o.type.spec.draggable&&o.type.spec.selectable!==!1||c instanceof A&&c.from<=s&&c.to>s)&&(this.mightDrag={node:o,pos:s,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&Je&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),Mt(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>mt(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(oi(e))),this.updateAllowDefault(e),this.allowDefault||!t?Mt(this.view,"pointer"):jd(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||Me&&this.mightDrag&&!this.mightDrag.node.isAtom||xe&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(Sn(this.view,D.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):Mt(this.view,"pointer")}move(e){this.updateAllowDefault(e),Mt(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}};Ce.touchstart=n=>{n.input.lastTouch=Date.now(),Do(n),Mt(n,"pointer")};Ce.touchmove=n=>{n.input.lastTouch=Date.now(),Mt(n,"pointer")};Ce.contextmenu=n=>Do(n);function La(n,e){return n.composing?!0:Me&&Math.abs(e.timeStamp-n.input.compositionEndedAt)<500?(n.input.compositionEndedAt=-2e8,!0):!1}var Kd=ht?5e3:-1;Oe.compositionstart=Oe.compositionupdate=n=>{if(!n.composing){n.domObserver.flush();let{state:e}=n,t=e.selection.$to;if(e.selection instanceof P&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(r=>r.type.spec.inclusive===!1)))n.markCursor=n.state.storedMarks||t.marks(),Zr(n,!0),n.markCursor=null;else if(Zr(n,!e.selection.empty),Je&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let r=n.domSelectionRange();for(let i=r.focusNode,o=r.focusOffset;i&&i.nodeType==1&&o!=0;){let s=o<0?i.lastChild:i.childNodes[o-1];if(!s)break;if(s.nodeType==3){let l=n.domSelection();l&&l.collapse(s,s.nodeValue.length);break}else i=s,o=-1}}n.input.composing=!0}Fa(n,Kd)};Oe.compositionend=(n,e)=>{n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=e.timeStamp,n.input.compositionPendingChanges=n.domObserver.pendingRecords().length?n.input.compositionID:0,n.input.compositionNode=null,n.input.compositionPendingChanges&&Promise.resolve().then(()=>n.domObserver.flush()),n.input.compositionID++,Fa(n,20))};function Fa(n,e){clearTimeout(n.input.composingTimeout),e>-1&&(n.input.composingTimeout=setTimeout(()=>Zr(n),e))}function za(n){for(n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=Gd());n.input.compositionNodes.length>0;)n.input.compositionNodes.pop().markParentsDirty()}function Ud(n){let e=n.domSelectionRange();if(!e.focusNode)return null;let t=Ju(e.focusNode,e.focusOffset),r=_u(e.focusNode,e.focusOffset);if(t&&r&&t!=r){let i=r.pmViewDesc,o=n.domObserver.lastChangedTextNode;if(t==o||r==o)return o;if(!i||!i.isText(r.nodeValue))return r;if(n.input.compositionNode==r){let s=t.pmViewDesc;if(!(!s||!s.isText(t.nodeValue)))return r}}return t||r}function Gd(){let n=document.createEvent("Event");return n.initEvent("event",!0,!0),n.timeStamp}function Zr(n,e=!1){if(!(ht&&n.domObserver.flushingSoon>=0)){if(n.domObserver.forceFlush(),za(n),e||n.docView&&n.docView.dirty){let t=To(n);return t&&!t.eq(n.state.selection)?n.dispatch(n.state.tr.setSelection(t)):(n.markCursor||e)&&!n.state.selection.empty?n.dispatch(n.state.tr.deleteSelection()):n.updateState(n.state),!0}return!1}}function Yd(n,e){if(!n.dom.parentNode)return;let t=n.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(e),n.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),n.focus()},50)}var ir=Ee&&Ct<15||wn&&Xu<604;Ce.copy=Oe.cut=(n,e)=>{let t=e,r=n.state.selection,i=t.type=="cut";if(r.empty)return;let o=ir?null:t.clipboardData,s=r.content(),{dom:l,text:a}=Ea(n,s);o?(t.preventDefault(),o.clearData(),o.setData("text/html",l.innerHTML),o.setData("text/plain",a)):Yd(n,l),i&&n.dispatch(n.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function Xd(n){return n.openStart==0&&n.openEnd==0&&n.content.childCount==1?n.content.firstChild:null}function Zd(n,e){if(!n.dom.parentNode)return;let t=n.input.shiftKey||n.state.selection.$from.parent.type.spec.code,r=n.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=n.input.shiftKey&&n.input.lastKeyCode!=45;setTimeout(()=>{n.focus(),r.parentNode&&r.parentNode.removeChild(r),t?or(n,r.value,null,i,e):or(n,r.textContent,r.innerHTML,i,e)},50)}function or(n,e,t,r,i){let o=Aa(n,e,t,r,n.state.selection.$from);if(n.someProp("handlePaste",a=>a(n,i,o||M.empty)))return!0;if(!o)return!1;let s=Xd(o),l=s?n.state.tr.replaceSelectionWith(s,r):n.state.tr.replaceSelection(o);return n.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Va(n){let e=n.getData("text/plain")||n.getData("Text");if(e)return e;let t=n.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}Oe.paste=(n,e)=>{let t=e;if(n.composing&&!ht)return;let r=ir?null:t.clipboardData,i=n.input.shiftKey&&n.input.lastKeyCode!=45;r&&or(n,Va(r),r.getData("text/html"),i,t)?t.preventDefault():Zd(n,t)};var Qr=class{constructor(e,t,r){this.slice=e,this.move=t,this.node=r}},$a=Le?"altKey":"ctrlKey";Ce.dragstart=(n,e)=>{let t=e,r=n.input.mouseDown;if(r&&r.done(),!t.dataTransfer)return;let i=n.state.selection,o=i.empty?null:n.posAtCoords(oi(t)),s;if(!(o&&o.pos>=i.from&&o.pos<=(i instanceof A?i.to-1:i.to))){if(r&&r.mightDrag)s=A.create(n.state.doc,r.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let u=n.docView.nearestDesc(t.target,!0);u&&u.node.type.spec.draggable&&u!=n.docView&&(s=A.create(n.state.doc,u.posBefore))}}let l=(s||n.state.selection).content(),{dom:a,text:c,slice:f}=Ea(n,l);(!t.dataTransfer.files.length||!xe||ha>120)&&t.dataTransfer.clearData(),t.dataTransfer.setData(ir?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",ir||t.dataTransfer.setData("text/plain",c),n.dragging=new Qr(f,!t[$a],s)};Ce.dragend=n=>{let e=n.dragging;window.setTimeout(()=>{n.dragging==e&&(n.dragging=null)},50)};Oe.dragover=Oe.dragenter=(n,e)=>e.preventDefault();Oe.drop=(n,e)=>{let t=e,r=n.dragging;if(n.dragging=null,!t.dataTransfer)return;let i=n.posAtCoords(oi(t));if(!i)return;let o=n.state.doc.resolve(i.pos),s=r&&r.slice;s?n.someProp("transformPasted",h=>{s=h(s,n)}):s=Aa(n,Va(t.dataTransfer),ir?null:t.dataTransfer.getData("text/html"),!1,o);let l=!!(r&&!t[$a]);if(n.someProp("handleDrop",h=>h(n,t,s||M.empty,l))){t.preventDefault();return}if(!s)return;t.preventDefault();let a=s?Wr(n.state.doc,o.pos,s):o.pos;a==null&&(a=o.pos);let c=n.state.tr;if(l){let{node:h}=r;h?h.replace(c):c.deleteSelection()}let f=c.mapping.map(a),u=s.openStart==0&&s.openEnd==0&&s.content.childCount==1,d=c.doc;if(u?c.replaceRangeWith(f,f,s.content.firstChild):c.replaceRange(f,f,s),c.doc.eq(d))return;let p=c.doc.resolve(f);if(u&&A.isSelectable(s.content.firstChild)&&p.nodeAfter&&p.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new A(p));else{let h=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,b,x)=>h=x),c.setSelection(Eo(n,p,c.doc.resolve(h)))}n.focus(),n.dispatch(c.setMeta("uiEvent","drop"))};Ce.focus=n=>{n.input.lastFocus=Date.now(),n.focused||(n.domObserver.stop(),n.dom.classList.add("ProseMirror-focused"),n.domObserver.start(),n.focused=!0,setTimeout(()=>{n.docView&&n.hasFocus()&&!n.domObserver.currentSelection.eq(n.domSelectionRange())&&mt(n)},20))};Ce.blur=(n,e)=>{let t=e;n.focused&&(n.domObserver.stop(),n.dom.classList.remove("ProseMirror-focused"),n.domObserver.start(),t.relatedTarget&&n.dom.contains(t.relatedTarget)&&n.domObserver.currentSelection.clear(),n.focused=!1)};Ce.beforeinput=(n,e)=>{if(xe&&ht&&e.inputType=="deleteContentBackward"){n.domObserver.flushSoon();let{domChangeCount:r}=n.input;setTimeout(()=>{if(n.input.domChangeCount!=r||(n.dom.blur(),n.focus(),n.someProp("handleKeyDown",o=>o(n,Wt(8,"Backspace")))))return;let{$cursor:i}=n.state.selection;i&&i.pos>0&&n.dispatch(n.state.tr.delete(i.pos-1,i.pos).scrollIntoView())},50)}};for(let n in Oe)Ce[n]=Oe[n];function sr(n,e){if(n==e)return!0;for(let t in n)if(n[t]!==e[t])return!1;for(let t in e)if(!(t in n))return!1;return!0}var ei=class n{constructor(e,t){this.toDOM=e,this.spec=t||_t,this.side=this.spec.side||0}map(e,t,r,i){let{pos:o,deleted:s}=e.mapResult(t.from+i,this.side<0?-1:1);return s?null:new Ae(o-r,o-r,this)}valid(){return!0}eq(e){return this==e||e instanceof n&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&sr(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}},Jt=class n{constructor(e,t){this.attrs=e,this.spec=t||_t}map(e,t,r,i){let o=e.map(t.from+i,this.spec.inclusiveStart?-1:1)-r,s=e.map(t.to+i,this.spec.inclusiveEnd?1:-1)-r;return o>=s?null:new Ae(o,s,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof n&&sr(this.attrs,e.attrs)&&sr(this.spec,e.spec)}static is(e){return e.type instanceof n}destroy(){}},wo=class n{constructor(e,t){this.attrs=e,this.spec=t||_t}map(e,t,r,i){let o=e.mapResult(t.from+i,1);if(o.deleted)return null;let s=e.mapResult(t.to+i,-1);return s.deleted||s.pos<=o.pos?null:new Ae(o.pos-r,s.pos-r,this)}valid(e,t){let{index:r,offset:i}=e.content.findIndex(t.from),o;return i==t.from&&!(o=e.child(r)).isText&&i+o.nodeSize==t.to}eq(e){return this==e||e instanceof n&&sr(this.attrs,e.attrs)&&sr(this.spec,e.spec)}destroy(){}},Ae=class n{constructor(e,t,r){this.from=e,this.to=t,this.type=r}copy(e,t){return new n(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,r){return this.type.map(e,this,t,r)}static widget(e,t,r){return new n(e,e,new ei(t,r))}static inline(e,t,r,i){return new n(e,t,new Jt(r,i))}static node(e,t,r,i){return new n(e,t,new wo(r,i))}get spec(){return this.type.spec}get inline(){return this.type instanceof Jt}get widget(){return this.type instanceof ei}},kn=[],_t={},se=class n{constructor(e,t){this.local=e.length?e:kn,this.children=t.length?t:kn}static create(e,t){return t.length?ni(t,e,0,_t):ve}find(e,t,r){let i=[];return this.findInner(e??0,t??1e9,i,0,r),i}findInner(e,t,r,i,o){for(let s=0;s<this.local.length;s++){let l=this.local[s];l.from<=t&&l.to>=e&&(!o||o(l.spec))&&r.push(l.copy(l.from+i,l.to+i))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<t&&this.children[s+1]>e){let l=this.children[s]+1;this.children[s+2].findInner(e-l,t-l,r,i+l,o)}}map(e,t,r){return this==ve||e.maps.length==0?this:this.mapInner(e,t,0,0,r||_t)}mapInner(e,t,r,i,o){let s;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,r,i);a&&a.type.valid(t,a)?(s||(s=[])).push(a):o.onRemove&&o.onRemove(this.local[l].spec)}return this.children.length?Qd(this.children,s||[],e,t,r,i,o):s?new n(s.sort(Kt),kn):ve}add(e,t){return t.length?this==ve?n.create(e,t):this.addInner(e,t,0):this}addInner(e,t,r){let i,o=0;e.forEach((l,a)=>{let c=a+r,f;if(f=Wa(t,l,c)){for(i||(i=this.children.slice());o<i.length&&i[o]<a;)o+=3;i[o]==a?i[o+2]=i[o+2].addInner(l,f,c+1):i.splice(o,0,a,a+l.nodeSize,ni(f,l,c+1,_t)),o+=3}});let s=Ha(o?ja(t):t,-r);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||s.splice(l--,1);return new n(s.length?this.local.concat(s).sort(Kt):this.local,i||this.children)}remove(e){return e.length==0||this==ve?this:this.removeInner(e,0)}removeInner(e,t){let r=this.children,i=this.local;for(let o=0;o<r.length;o+=3){let s,l=r[o]+t,a=r[o+1]+t;for(let f=0,u;f<e.length;f++)(u=e[f])&&u.from>l&&u.to<a&&(e[f]=null,(s||(s=[])).push(u));if(!s)continue;r==this.children&&(r=this.children.slice());let c=r[o+2].removeInner(s,l+1);c!=ve?r[o+2]=c:(r.splice(o,3),o-=3)}if(i.length){for(let o=0,s;o<e.length;o++)if(s=e[o])for(let l=0;l<i.length;l++)i[l].eq(s,t)&&(i==this.local&&(i=this.local.slice()),i.splice(l--,1))}return r==this.children&&i==this.local?this:i.length||r.length?new n(i,r):ve}forChild(e,t){if(this==ve)return this;if(t.isLeaf)return n.empty;let r,i;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(r=this.children[l+2]);break}let o=e+1,s=o+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<s&&a.to>o&&a.type instanceof Jt){let c=Math.max(o,a.from)-o,f=Math.min(s,a.to)-o;c<f&&(i||(i=[])).push(a.copy(c,f))}}if(i){let l=new n(i.sort(Kt),kn);return r?new ti([l,r]):l}return r||ve}eq(e){if(this==e)return!0;if(!(e instanceof n)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return Po(this.localsInner(e))}localsInner(e){if(this==ve)return kn;if(e.inlineContent||!this.local.some(Jt.is))return this.local;let t=[];for(let r=0;r<this.local.length;r++)this.local[r].type instanceof Jt||t.push(this.local[r]);return t}forEachSet(e){e(this)}};se.empty=new se([],[]);se.removeOverlap=Po;var ve=se.empty,ti=class n{constructor(e){this.members=e}map(e,t){let r=this.members.map(i=>i.map(e,t,_t));return n.from(r)}forChild(e,t){if(t.isLeaf)return se.empty;let r=[];for(let i=0;i<this.members.length;i++){let o=this.members[i].forChild(e,t);o!=ve&&(o instanceof n?r=r.concat(o.members):r.push(o))}return n.from(r)}eq(e){if(!(e instanceof n)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,r=!0;for(let i=0;i<this.members.length;i++){let o=this.members[i].localsInner(e);if(o.length)if(!t)t=o;else{r&&(t=t.slice(),r=!1);for(let s=0;s<o.length;s++)t.push(o[s])}}return t?Po(r?t:t.sort(Kt)):kn}static from(e){switch(e.length){case 0:return ve;case 1:return e[0];default:return new n(e.every(t=>t instanceof se)?e:e.reduce((t,r)=>t.concat(r instanceof se?r:r.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}};function Qd(n,e,t,r,i,o,s){let l=n.slice();for(let c=0,f=o;c<t.maps.length;c++){let u=0;t.maps[c].forEach((d,p,h,m)=>{let g=m-h-(p-d);for(let b=0;b<l.length;b+=3){let x=l[b+1];if(x<0||d>x+f-u)continue;let w=l[b]+f-u;p>=w?l[b+1]=d<=w?-2:-1:d>=f&&g&&(l[b]+=g,l[b+1]+=g)}u+=g}),f=t.maps[c].map(f,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let f=t.map(n[c]+o),u=f-i;if(u<0||u>=r.content.size){a=!0;continue}let d=t.map(n[c+1]+o,-1),p=d-i,{index:h,offset:m}=r.content.findIndex(u),g=r.maybeChild(h);if(g&&m==u&&m+g.nodeSize==p){let b=l[c+2].mapInner(t,g,f+1,n[c]+o+1,s);b!=ve?(l[c]=u,l[c+1]=p,l[c+2]=b):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=ep(l,n,e,t,i,o,s),f=ni(c,r,0,s);e=f.local;for(let u=0;u<l.length;u+=3)l[u+1]<0&&(l.splice(u,3),u-=3);for(let u=0,d=0;u<f.children.length;u+=3){let p=f.children[u];for(;d<l.length&&l[d]<p;)d+=3;l.splice(d,0,f.children[u],f.children[u+1],f.children[u+2])}}return new se(e.sort(Kt),l)}function Ha(n,e){if(!e||!n.length)return n;let t=[];for(let r=0;r<n.length;r++){let i=n[r];t.push(new Ae(i.from+e,i.to+e,i.type))}return t}function ep(n,e,t,r,i,o,s){function l(a,c){for(let f=0;f<a.local.length;f++){let u=a.local[f].map(r,i,c);u?t.push(u):s.onRemove&&s.onRemove(a.local[f].spec)}for(let f=0;f<a.children.length;f+=3)l(a.children[f+2],a.children[f]+c+1)}for(let a=0;a<n.length;a+=3)n[a+1]==-1&&l(n[a+2],e[a]+o+1);return t}function Wa(n,e,t){if(e.isLeaf)return null;let r=t+e.nodeSize,i=null;for(let o=0,s;o<n.length;o++)(s=n[o])&&s.from>t&&s.to<r&&((i||(i=[])).push(s),n[o]=null);return i}function ja(n){let e=[];for(let t=0;t<n.length;t++)n[t]!=null&&e.push(n[t]);return e}function ni(n,e,t,r){let i=[],o=!1;e.forEach((l,a)=>{let c=Wa(n,l,a+t);if(c){o=!0;let f=ni(c,l,t+a+1,r);f!=ve&&i.push(a,a+l.nodeSize,f)}});let s=Ha(o?ja(n):n,-t).sort(Kt);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||(r.onRemove&&r.onRemove(s[l].spec),s.splice(l--,1));return s.length||i.length?new se(s,i):ve}function Kt(n,e){return n.from-e.from||n.to-e.to}function Po(n){let e=n;for(let t=0;t<e.length-1;t++){let r=e[t];if(r.from!=r.to)for(let i=t+1;i<e.length;i++){let o=e[i];if(o.from==r.from){o.to!=r.to&&(e==n&&(e=n.slice()),e[i]=o.copy(o.from,r.to),na(e,i+1,o.copy(r.to,o.to)));continue}else{o.from<r.to&&(e==n&&(e=n.slice()),e[t]=r.copy(r.from,o.from),na(e,i,r.copy(o.from,r.to)));break}}}return e}function na(n,e,t){for(;e<n.length&&Kt(t,n[e])>0;)e++;n.splice(e,0,t)}function oo(n){let e=[];return n.someProp("decorations",t=>{let r=t(n.state);r&&r!=ve&&e.push(r)}),n.cursorWrapper&&e.push(se.create(n.state.doc,[n.cursorWrapper.deco])),ti.from(e)}var tp={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},np=Ee&&Ct<=11,Mo=class{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}},Co=class{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Mo,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(r=>{for(let i=0;i<r.length;i++)this.queue.push(r[i]);Ee&&Ct<=11&&r.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),np&&(this.onCharData=r=>{this.queue.push({target:r.target,type:"characterData",oldValue:r.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,tp)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(Ul(this.view)){if(this.suppressingSelectionUpdates)return mt(this.view);if(Ee&&Ct<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Ut(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,r;for(let o=e.focusNode;o;o=rr(o))t.add(o);for(let o=e.anchorNode;o;o=rr(o))if(t.has(o)){r=o;break}let i=r&&this.view.docView.nearestDesc(r);if(i&&i.ignoreMutation({type:"selection",target:r.nodeType==3?r.parentNode:r}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let r=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&Ul(e)&&!this.ignoreSelectionChange(r),o=-1,s=-1,l=!1,a=[];if(e.editable)for(let f=0;f<t.length;f++){let u=this.registerMutation(t[f],a);u&&(o=o<0?u.from:Math.min(u.from,o),s=s<0?u.to:Math.max(u.to,s),u.typeOver&&(l=!0))}if(Je&&a.length){let f=a.filter(u=>u.nodeName=="BR");if(f.length==2){let[u,d]=f;u.parentNode&&u.parentNode.parentNode==d.parentNode?d.remove():u.remove()}else{let{focusNode:u}=this.currentSelection;for(let d of f){let p=d.parentNode;p&&p.nodeName=="LI"&&(!u||op(e,u)!=p)&&d.remove()}}}let c=null;o<0&&i&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&ii(r)&&(c=To(e))&&c.eq(D.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,mt(e),this.currentSelection.set(r),e.scrollToSelection()):(o>-1||i)&&(o>-1&&(e.docView.markDirty(o,s),rp(e)),this.handleDOMChange(o,s,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(r)||mt(e),this.currentSelection.set(r))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let r=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(r==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!r||r.ignoreMutation(e))return null;if(e.type=="childList"){for(let f=0;f<e.addedNodes.length;f++){let u=e.addedNodes[f];t.push(u),u.nodeType==3&&(this.lastChangedTextNode=u)}if(r.contentDOM&&r.contentDOM!=r.dom&&!r.contentDOM.contains(e.target))return{from:r.posBefore,to:r.posAfter};let i=e.previousSibling,o=e.nextSibling;if(Ee&&Ct<=11&&e.addedNodes.length)for(let f=0;f<e.addedNodes.length;f++){let{previousSibling:u,nextSibling:d}=e.addedNodes[f];(!u||Array.prototype.indexOf.call(e.addedNodes,u)<0)&&(i=u),(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(o=d)}let s=i&&i.parentNode==e.target?pe(i)+1:0,l=r.localPosFromDOM(e.target,s,-1),a=o&&o.parentNode==e.target?pe(o):e.target.childNodes.length,c=r.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:r.posAtStart-r.border,to:r.posAtEnd+r.border}:(this.lastChangedTextNode=e.target,{from:r.posAtStart,to:r.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}},ra=new WeakMap,ia=!1;function rp(n){if(!ra.has(n)&&(ra.set(n,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(n.dom).whiteSpace)!==-1)){if(n.requiresGeckoHackNode=Je,ia)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),ia=!0}}function oa(n,e){let t=e.startContainer,r=e.startOffset,i=e.endContainer,o=e.endOffset,s=n.domAtPos(n.state.selection.anchor);return Ut(s.node,s.offset,i,o)&&([t,r,i,o]=[i,o,t,r]),{anchorNode:t,anchorOffset:r,focusNode:i,focusOffset:o}}function ip(n,e){if(e.getComposedRanges){let i=e.getComposedRanges(n.root)[0];if(i)return oa(n,i)}let t;function r(i){i.preventDefault(),i.stopImmediatePropagation(),t=i.getTargetRanges()[0]}return n.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),n.dom.removeEventListener("beforeinput",r,!0),t?oa(n,t):null}function op(n,e){for(let t=e.parentNode;t&&t!=n.dom;t=t.parentNode){let r=n.docView.nearestDesc(t,!0);if(r&&r.node.isBlock)return t}return null}function sp(n,e,t){let{node:r,fromOffset:i,toOffset:o,from:s,to:l}=n.docView.parseRange(e,t),a=n.domSelectionRange(),c,f=a.anchorNode;if(f&&n.dom.contains(f.nodeType==1?f:f.parentNode)&&(c=[{node:f,offset:a.anchorOffset}],ii(a)||c.push({node:a.focusNode,offset:a.focusOffset})),xe&&n.input.lastKeyCode===8)for(let g=o;g>i;g--){let b=r.childNodes[g-1],x=b.pmViewDesc;if(b.nodeName=="BR"&&!x){o=g;break}if(!x||x.size)break}let u=n.state.doc,d=n.someProp("domParser")||at.fromSchema(n.state.schema),p=u.resolve(s),h=null,m=d.parse(r,{topNode:p.parent,topMatch:p.parent.contentMatchAt(p.index()),topOpen:!0,from:i,to:o,preserveWhitespace:p.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:lp,context:p});if(c&&c[0].pos!=null){let g=c[0].pos,b=c[1]&&c[1].pos;b==null&&(b=g),h={anchor:g+s,head:b+s}}return{doc:m,sel:h,from:s,to:l}}function lp(n){let e=n.pmViewDesc;if(e)return e.parseRule();if(n.nodeName=="BR"&&n.parentNode){if(Me&&/^(ul|ol)$/i.test(n.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(n.parentNode.lastChild==n||Me&&/^(tr|table)$/i.test(n.parentNode.nodeName))return{ignore:!0}}else if(n.nodeName=="IMG"&&n.getAttribute("mark-placeholder"))return{ignore:!0};return null}var ap=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function cp(n,e,t,r,i){let o=n.input.compositionPendingChanges||(n.composing?n.input.compositionID:0);if(n.input.compositionPendingChanges=0,e<0){let T=n.input.lastSelectionTime>Date.now()-50?n.input.lastSelectionOrigin:null,N=To(n,T);if(N&&!n.state.selection.eq(N)){if(xe&&ht&&n.input.lastKeyCode===13&&Date.now()-100<n.input.lastKeyCodeTime&&n.someProp("handleKeyDown",H=>H(n,Wt(13,"Enter"))))return;let F=n.state.tr.setSelection(N);T=="pointer"?F.setMeta("pointer",!0):T=="key"&&F.scrollIntoView(),o&&F.setMeta("composition",o),n.dispatch(F)}return}let s=n.state.doc.resolve(e),l=s.sharedDepth(t);e=s.before(l+1),t=n.state.doc.resolve(t).after(l+1);let a=n.state.selection,c=sp(n,e,t),f=n.state.doc,u=f.slice(c.from,c.to),d,p;n.input.lastKeyCode===8&&Date.now()-100<n.input.lastKeyCodeTime?(d=n.state.selection.to,p="end"):(d=n.state.selection.from,p="start"),n.input.lastKeyCode=null;let h=dp(u.content,c.doc.content,c.from,d,p);if(h&&n.input.domChangeCount++,(wn&&n.input.lastIOSEnter>Date.now()-225||ht)&&i.some(T=>T.nodeType==1&&!ap.test(T.nodeName))&&(!h||h.endA>=h.endB)&&n.someProp("handleKeyDown",T=>T(n,Wt(13,"Enter")))){n.input.lastIOSEnter=0;return}if(!h)if(r&&a instanceof P&&!a.empty&&a.$head.sameParent(a.$anchor)&&!n.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))h={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let T=sa(n,n.state.doc,c.sel);if(T&&!T.eq(n.state.selection)){let N=n.state.tr.setSelection(T);o&&N.setMeta("composition",o),n.dispatch(N)}}return}n.state.selection.from<n.state.selection.to&&h.start==h.endB&&n.state.selection instanceof P&&(h.start>n.state.selection.from&&h.start<=n.state.selection.from+2&&n.state.selection.from>=c.from?h.start=n.state.selection.from:h.endA<n.state.selection.to&&h.endA>=n.state.selection.to-2&&n.state.selection.to<=c.to&&(h.endB+=n.state.selection.to-h.endA,h.endA=n.state.selection.to)),Ee&&Ct<=11&&h.endB==h.start+1&&h.endA==h.start&&h.start>c.from&&c.doc.textBetween(h.start-c.from-1,h.start-c.from+1)==" \xA0"&&(h.start--,h.endA--,h.endB--);let m=c.doc.resolveNoCache(h.start-c.from),g=c.doc.resolveNoCache(h.endB-c.from),b=f.resolve(h.start),x=m.sameParent(g)&&m.parent.inlineContent&&b.end()>=h.endA,w;if((wn&&n.input.lastIOSEnter>Date.now()-225&&(!x||i.some(T=>T.nodeName=="DIV"||T.nodeName=="P"))||!x&&m.pos<c.doc.content.size&&!m.sameParent(g)&&(w=D.findFrom(c.doc.resolve(m.pos+1),1,!0))&&w.head==g.pos)&&n.someProp("handleKeyDown",T=>T(n,Wt(13,"Enter")))){n.input.lastIOSEnter=0;return}if(n.state.selection.anchor>h.start&&up(f,h.start,h.endA,m,g)&&n.someProp("handleKeyDown",T=>T(n,Wt(8,"Backspace")))){ht&&xe&&n.domObserver.suppressSelectionUpdates();return}xe&&h.endB==h.start&&(n.input.lastChromeDelete=Date.now()),ht&&!x&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==h.endA&&(h.endB-=2,g=c.doc.resolveNoCache(h.endB-c.from),setTimeout(()=>{n.someProp("handleKeyDown",function(T){return T(n,Wt(13,"Enter"))})},20));let y=h.start,C=h.endA,k,I,B;if(x){if(m.pos==g.pos)Ee&&Ct<=11&&m.parentOffset==0&&(n.domObserver.suppressSelectionUpdates(),setTimeout(()=>mt(n),20)),k=n.state.tr.delete(y,C),I=f.resolve(h.start).marksAcross(f.resolve(h.endA));else if(h.endA==h.endB&&(B=fp(m.parent.content.cut(m.parentOffset,g.parentOffset),b.parent.content.cut(b.parentOffset,h.endA-b.start()))))k=n.state.tr,B.type=="add"?k.addMark(y,C,B.mark):k.removeMark(y,C,B.mark);else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let T=m.parent.textBetween(m.parentOffset,g.parentOffset);if(n.someProp("handleTextInput",N=>N(n,y,C,T)))return;k=n.state.tr.insertText(T,y,C)}}if(k||(k=n.state.tr.replace(y,C,c.doc.slice(h.start-c.from,h.endB-c.from))),c.sel){let T=sa(n,k.doc,c.sel);T&&!(xe&&n.composing&&T.empty&&(h.start!=h.endB||n.input.lastChromeDelete<Date.now()-100)&&(T.head==y||T.head==k.mapping.map(C)-1)||Ee&&T.empty&&T.head==y)&&k.setSelection(T)}I&&k.ensureMarks(I),o&&k.setMeta("composition",o),n.dispatch(k.scrollIntoView())}function sa(n,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:Eo(n,e.resolve(t.anchor),e.resolve(t.head))}function fp(n,e){let t=n.firstChild.marks,r=e.firstChild.marks,i=t,o=r,s,l,a;for(let f=0;f<r.length;f++)i=r[f].removeFromSet(i);for(let f=0;f<t.length;f++)o=t[f].removeFromSet(o);if(i.length==1&&o.length==0)l=i[0],s="add",a=f=>f.mark(l.addToSet(f.marks));else if(i.length==0&&o.length==1)l=o[0],s="remove",a=f=>f.mark(l.removeFromSet(f.marks));else return null;let c=[];for(let f=0;f<e.childCount;f++)c.push(a(e.child(f)));if(S.from(c).eq(n))return{mark:l,type:s}}function up(n,e,t,r,i){if(t-e<=i.pos-r.pos||so(r,!0,!1)<i.pos)return!1;let o=n.resolve(e);if(!r.parent.isTextblock){let l=o.nodeAfter;return l!=null&&t==e+l.nodeSize}if(o.parentOffset<o.parent.content.size||!o.parent.isTextblock)return!1;let s=n.resolve(so(o,!0,!0));return!s.parent.isTextblock||s.pos>t||so(s,!0,!1)<t?!1:r.parent.content.cut(r.parentOffset).eq(s.parent.content)}function so(n,e,t){let r=n.depth,i=e?n.end():n.pos;for(;r>0&&(e||n.indexAfter(r)==n.node(r).childCount);)r--,i++,e=!1;if(t){let o=n.node(r).maybeChild(n.indexAfter(r));for(;o&&!o.isLeaf;)o=o.firstChild,i++}return i}function dp(n,e,t,r,i){let o=n.findDiffStart(e,t);if(o==null)return null;let{a:s,b:l}=n.findDiffEnd(e,t+n.size,t+e.size);if(i=="end"){let a=Math.max(0,o-Math.min(s,l));r-=s+a-o}if(s<o&&n.size<e.size){let a=r<=o&&r>=s?o-r:0;o-=a,o&&o<e.size&&la(e.textBetween(o-1,o+1))&&(o+=a?1:-1),l=o+(l-s),s=o}else if(l<o){let a=r<=o&&r>=l?o-r:0;o-=a,o&&o<n.size&&la(n.textBetween(o-1,o+1))&&(o+=a?1:-1),s=o+(s-l),l=o}return{start:o,endA:s,endB:l}}function la(n){if(n.length!=2)return!1;let e=n.charCodeAt(0),t=n.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}var ri=class{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new xo,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(da),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=fa(this),ca(this),this.nodeViews=ua(this),this.docView=Wl(this.state.doc,aa(this),oo(this),this.dom,this),this.domObserver=new Co(this,(r,i,o,s)=>cp(this,r,i,o,s)),this.domObserver.start(),Ld(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&ko(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(da),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let r in this._props)t[r]=this._props[r];t.state=this.state;for(let r in e)t[r]=e[r];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var r;let i=this.state,o=!1,s=!1;e.storedMarks&&this.composing&&(za(this),s=!0),this.state=e;let l=i.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let p=ua(this);hp(p,this.nodeViews)&&(this.nodeViews=p,o=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&ko(this),this.editable=fa(this),ca(this);let a=oo(this),c=aa(this),f=i.plugins!=e.plugins&&!i.doc.eq(e.doc)?"reset":e.scrollToSelection>i.scrollToSelection?"to selection":"preserve",u=o||!this.docView.matchesNode(e.doc,c,a);(u||!e.selection.eq(i.selection))&&(s=!0);let d=f=="preserve"&&s&&this.dom.style.overflowAnchor==null&&ed(this);if(s){this.domObserver.stop();let p=u&&(Ee||xe)&&!this.composing&&!i.selection.empty&&!e.selection.empty&&pp(i.selection,e.selection);if(u){let h=xe?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=Ud(this)),(o||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=Wl(e.doc,c,a,this.dom,this)),h&&!this.trackWrites&&(p=!0)}p||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&kd(this))?mt(this,p):(Ca(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(i),!((r=this.dragging)===null||r===void 0)&&r.node&&!i.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,i),f=="reset"?this.dom.scrollTop=0:f=="to selection"?this.scrollToSelection():d&&td(d)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof A){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&Ll(this,t.getBoundingClientRect(),e)}else Ll(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let r=this.directPlugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let r=this.state.plugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let r=this.pluginViews[t];r.update&&r.update(this,e)}}updateDraggedNode(e,t){let r=e.node,i=-1;if(this.state.doc.nodeAt(r.from)==r.node)i=r.from;else{let o=r.from+(this.state.doc.content.size-t.doc.content.size);(o>0&&this.state.doc.nodeAt(o))==r.node&&(i=o)}this.dragging=new Qr(e.slice,e.move,i<0?void 0:A.create(this.state.doc,i))}someProp(e,t){let r=this._props&&this._props[e],i;if(r!=null&&(i=t?t(r):r))return i;for(let s=0;s<this.directPlugins.length;s++){let l=this.directPlugins[s].props[e];if(l!=null&&(i=t?t(l):l))return i}let o=this.state.plugins;if(o)for(let s=0;s<o.length;s++){let l=o[s].props[e];if(l!=null&&(i=t?t(l):l))return i}}hasFocus(){if(Ee){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&nd(this.dom),mt(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return ld(this,e)}coordsAtPos(e,t=1){return va(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,r=-1){let i=this.docView.posFromDOM(e,t,r);if(i==null)throw new RangeError("DOM position not inside the editor");return i}endOfTextblock(e,t){return dd(this,t||this.state,e)}pasteHTML(e,t){return or(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return or(this,e,null,!0,t||new ClipboardEvent("paste"))}destroy(){this.docView&&(Fd(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],oo(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,ju())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return Vd(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return e?Me&&this.root.nodeType===11&&Uu(this.dom.ownerDocument)==this.dom&&ip(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}};function aa(n){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(n.editable),n.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(n.state)),t)for(let r in t)r=="class"?e.class+=" "+t[r]:r=="style"?e.style=(e.style?e.style+";":"")+t[r]:!e[r]&&r!="contenteditable"&&r!="nodeName"&&(e[r]=String(t[r]))}),e.translate||(e.translate="no"),[Ae.node(0,n.state.doc.content.size,e)]}function ca(n){if(n.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),n.cursorWrapper={dom:e,deco:Ae.widget(n.state.selection.from,e,{raw:!0,marks:n.markCursor})}}else n.cursorWrapper=null}function fa(n){return!n.someProp("editable",e=>e(n.state)===!1)}function pp(n,e){let t=Math.min(n.$anchor.sharedDepth(n.head),e.$anchor.sharedDepth(e.head));return n.$anchor.start(t)!=e.$anchor.start(t)}function ua(n){let e=Object.create(null);function t(r){for(let i in r)Object.prototype.hasOwnProperty.call(e,i)||(e[i]=r[i])}return n.someProp("nodeViews",t),n.someProp("markViews",t),e}function hp(n,e){let t=0,r=0;for(let i in n){if(n[i]!=e[i])return!0;t++}for(let i in e)r++;return t!=r}function da(n){if(n.spec.state||n.spec.filterTransaction||n.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var gt={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},li={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},mp=typeof navigator<"u"&&/Mac/.test(navigator.platform),gp=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(ee=0;ee<10;ee++)gt[48+ee]=gt[96+ee]=String(ee);var ee;for(ee=1;ee<=24;ee++)gt[ee+111]="F"+ee;var ee;for(ee=65;ee<=90;ee++)gt[ee]=String.fromCharCode(ee+32),li[ee]=String.fromCharCode(ee);var ee;for(si in gt)li.hasOwnProperty(si)||(li[si]=gt[si]);var si;function qa(n){var e=mp&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||gp&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?li:gt)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}var yp=typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):!1;function bp(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let r,i,o,s;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))s=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))o=!0;else if(/^mod$/i.test(a))yp?s=!0:i=!0;else throw new Error("Unrecognized modifier name: "+a)}return r&&(t="Alt-"+t),i&&(t="Ctrl-"+t),s&&(t="Meta-"+t),o&&(t="Shift-"+t),t}function vp(n){let e=Object.create(null);for(let t in n)e[bp(t)]=n[t];return e}function Io(n,e,t=!0){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t&&e.shiftKey&&(n="Shift-"+n),n}function Ja(n){return new q({props:{handleKeyDown:Ro(n)}})}function Ro(n){let e=vp(n);return function(t,r){let i=qa(r),o,s=e[Io(i,r)];if(s&&s(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(r.shiftKey){let l=e[Io(i,r,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((r.shiftKey||r.altKey||r.metaKey||i.charCodeAt(0)>127)&&(o=gt[r.keyCode])&&o!=i){let l=e[Io(o,r)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}var ai=(n,e)=>n.selection.empty?!1:(e&&e(n.tr.deleteSelection().scrollIntoView()),!0);function Ka(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("backward",n):t.parentOffset>0)?null:t}var Lo=(n,e,t)=>{let r=Ka(n,t);if(!r)return!1;let i=zo(r);if(!i){let s=r.blockRange(),l=s&&ut(s);return l==null?!1:(e&&e(n.tr.lift(s,l).scrollIntoView()),!0)}let o=i.nodeBefore;if(nc(n,i,e,-1))return!0;if(r.parent.content.size==0&&(Cn(o,"end")||A.isSelectable(o)))for(let s=r.depth;;s--){let l=Zn(n.doc,r.before(s),r.after(s),M.empty);if(l&&l.slice.size<l.to-l.from){if(e){let a=n.tr.step(l);a.setSelection(Cn(o,"end")?D.findFrom(a.doc.resolve(a.mapping.map(i.pos,-1)),-1):A.create(a.doc,i.pos-o.nodeSize)),e(a.scrollIntoView())}return!0}if(s==1||r.node(s-1).childCount>1)break}return o.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos-o.nodeSize,i.pos).scrollIntoView()),!0):!1},Ua=(n,e,t)=>{let r=Ka(n,t);if(!r)return!1;let i=zo(r);return i?Ya(n,i,e):!1},Ga=(n,e,t)=>{let r=Xa(n,t);if(!r)return!1;let i=Ho(r);return i?Ya(n,i,e):!1};function Ya(n,e,t){let r=e.nodeBefore,i=r,o=e.pos-1;for(;!i.isTextblock;o--){if(i.type.spec.isolating)return!1;let f=i.lastChild;if(!f)return!1;i=f}let s=e.nodeAfter,l=s,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let f=l.firstChild;if(!f)return!1;l=f}let c=Zn(n.doc,o,a,M.empty);if(!c||c.from!=o||c instanceof be&&c.slice.size>=a-o)return!1;if(t){let f=n.tr.step(c);f.setSelection(P.create(f.doc,o)),t(f.scrollIntoView())}return!0}function Cn(n,e,t=!1){for(let r=n;r;r=e=="start"?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(t&&r.childCount!=1)return!1}return!1}var Fo=(n,e,t)=>{let{$head:r,empty:i}=n.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("backward",n):r.parentOffset>0)return!1;o=zo(r)}let s=o&&o.nodeBefore;return!s||!A.isSelectable(s)?!1:(e&&e(n.tr.setSelection(A.create(n.doc,o.pos-s.nodeSize)).scrollIntoView()),!0)};function zo(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){if(n.index(e)>0)return n.doc.resolve(n.before(e+1));if(n.node(e).type.spec.isolating)break}return null}function Xa(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("forward",n):t.parentOffset<t.parent.content.size)?null:t}var Vo=(n,e,t)=>{let r=Xa(n,t);if(!r)return!1;let i=Ho(r);if(!i)return!1;let o=i.nodeAfter;if(nc(n,i,e,1))return!0;if(r.parent.content.size==0&&(Cn(o,"start")||A.isSelectable(o))){let s=Zn(n.doc,r.before(),r.after(),M.empty);if(s&&s.slice.size<s.to-s.from){if(e){let l=n.tr.step(s);l.setSelection(Cn(o,"start")?D.findFrom(l.doc.resolve(l.mapping.map(i.pos)),1):A.create(l.doc,l.mapping.map(i.pos))),e(l.scrollIntoView())}return!0}}return o.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos,i.pos+o.nodeSize).scrollIntoView()),!0):!1},$o=(n,e,t)=>{let{$head:r,empty:i}=n.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("forward",n):r.parentOffset<r.parent.content.size)return!1;o=Ho(r)}let s=o&&o.nodeAfter;return!s||!A.isSelectable(s)?!1:(e&&e(n.tr.setSelection(A.create(n.doc,o.pos)).scrollIntoView()),!0)};function Ho(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){let t=n.node(e);if(n.index(e)+1<t.childCount)return n.doc.resolve(n.after(e+1));if(t.type.spec.isolating)break}return null}var Za=(n,e)=>{let t=n.selection,r=t instanceof A,i;if(r){if(t.node.isTextblock||!qe(n.doc,t.from))return!1;i=t.from}else if(i=yn(n.doc,t.from,-1),i==null)return!1;if(e){let o=n.tr.join(i);r&&o.setSelection(A.create(o.doc,i-n.doc.resolve(i).nodeBefore.nodeSize)),e(o.scrollIntoView())}return!0},Qa=(n,e)=>{let t=n.selection,r;if(t instanceof A){if(t.node.isTextblock||!qe(n.doc,t.to))return!1;r=t.to}else if(r=yn(n.doc,t.to,1),r==null)return!1;return e&&e(n.tr.join(r).scrollIntoView()),!0},ec=(n,e)=>{let{$from:t,$to:r}=n.selection,i=t.blockRange(r),o=i&&ut(i);return o==null?!1:(e&&e(n.tr.lift(i,o).scrollIntoView()),!0)},Wo=(n,e)=>{let{$head:t,$anchor:r}=n.selection;return!t.parent.type.spec.code||!t.sameParent(r)?!1:(e&&e(n.tr.insertText(`
`).scrollIntoView()),!0)};function jo(n){for(let e=0;e<n.edgeCount;e++){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}var qo=(n,e)=>{let{$head:t,$anchor:r}=n.selection;if(!t.parent.type.spec.code||!t.sameParent(r))return!1;let i=t.node(-1),o=t.indexAfter(-1),s=jo(i.contentMatchAt(o));if(!s||!i.canReplaceWith(o,o,s))return!1;if(e){let l=t.after(),a=n.tr.replaceWith(l,l,s.createAndFill());a.setSelection(D.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},Jo=(n,e)=>{let t=n.selection,{$from:r,$to:i}=t;if(t instanceof we||r.parent.inlineContent||i.parent.inlineContent)return!1;let o=jo(i.parent.contentMatchAt(i.indexAfter()));if(!o||!o.isTextblock)return!1;if(e){let s=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=n.tr.insert(s,o.createAndFill());l.setSelection(P.create(l.doc,s+1)),e(l.scrollIntoView())}return!0},_o=(n,e)=>{let{$cursor:t}=n.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let o=t.before();if(Be(n.doc,o))return e&&e(n.tr.split(o).scrollIntoView()),!0}let r=t.blockRange(),i=r&&ut(r);return i==null?!1:(e&&e(n.tr.lift(r,i).scrollIntoView()),!0)};function xp(n){return(e,t)=>{let{$from:r,$to:i}=e.selection;if(e.selection instanceof A&&e.selection.node.isBlock)return!r.parentOffset||!Be(e.doc,r.pos)?!1:(t&&t(e.tr.split(r.pos).scrollIntoView()),!0);if(!r.depth)return!1;let o=[],s,l,a=!1,c=!1;for(let p=r.depth;;p--)if(r.node(p).isBlock){a=r.end(p)==r.pos+(r.depth-p),c=r.start(p)==r.pos-(r.depth-p),l=jo(r.node(p-1).contentMatchAt(r.indexAfter(p-1)));let m=n&&n(i.parent,a,r);o.unshift(m||(a&&l?{type:l}:null)),s=p;break}else{if(p==1)return!1;o.unshift(null)}let f=e.tr;(e.selection instanceof P||e.selection instanceof we)&&f.deleteSelection();let u=f.mapping.map(r.pos),d=Be(f.doc,u,o.length,o);if(d||(o[0]=l?{type:l}:null,d=Be(f.doc,u,o.length,o)),f.split(u,o.length,o),!a&&c&&r.node(s).type!=l){let p=f.mapping.map(r.before(s)),h=f.doc.resolve(p);l&&r.node(s-1).canReplaceWith(h.index(),h.index()+1,l)&&f.setNodeMarkup(f.mapping.map(r.before(s)),l)}return t&&t(f.scrollIntoView()),!0}}var kp=xp();var tc=(n,e)=>{let{$from:t,to:r}=n.selection,i,o=t.sharedDepth(r);return o==0?!1:(i=t.before(o),e&&e(n.tr.setSelection(A.create(n.doc,i))),!0)},Sp=(n,e)=>(e&&e(n.tr.setSelection(new we(n.doc))),!0);function wp(n,e,t){let r=e.nodeBefore,i=e.nodeAfter,o=e.index();return!r||!i||!r.type.compatibleContent(i.type)?!1:!r.content.size&&e.parent.canReplace(o-1,o)?(t&&t(n.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(o,o+1)||!(i.isTextblock||qe(n.doc,e.pos))?!1:(t&&t(n.tr.join(e.pos).scrollIntoView()),!0)}function nc(n,e,t,r){let i=e.nodeBefore,o=e.nodeAfter,s,l,a=i.type.spec.isolating||o.type.spec.isolating;if(!a&&wp(n,e,t))return!0;let c=!a&&e.parent.canReplace(e.index(),e.index()+1);if(c&&(s=(l=i.contentMatchAt(i.childCount)).findWrapping(o.type))&&l.matchType(s[0]||o.type).validEnd){if(t){let p=e.pos+o.nodeSize,h=S.empty;for(let b=s.length-1;b>=0;b--)h=S.from(s[b].create(null,h));h=S.from(i.copy(h));let m=n.tr.step(new Q(e.pos-1,p,e.pos,p,new M(h,1,0),s.length,!0)),g=m.doc.resolve(p+2*s.length);g.nodeAfter&&g.nodeAfter.type==i.type&&qe(m.doc,g.pos)&&m.join(g.pos),t(m.scrollIntoView())}return!0}let f=o.type.spec.isolating||r>0&&a?null:D.findFrom(e,1),u=f&&f.$from.blockRange(f.$to),d=u&&ut(u);if(d!=null&&d>=e.depth)return t&&t(n.tr.lift(u,d).scrollIntoView()),!0;if(c&&Cn(o,"start",!0)&&Cn(i,"end")){let p=i,h=[];for(;h.push(p),!p.isTextblock;)p=p.lastChild;let m=o,g=1;for(;!m.isTextblock;m=m.firstChild)g++;if(p.canReplace(p.childCount,p.childCount,m.content)){if(t){let b=S.empty;for(let w=h.length-1;w>=0;w--)b=S.from(h[w].copy(b));let x=n.tr.step(new Q(e.pos-h.length,e.pos+o.nodeSize,e.pos+g,e.pos+o.nodeSize-g,new M(b,h.length,0),0,!0));t(x.scrollIntoView())}return!0}}return!1}function rc(n){return function(e,t){let r=e.selection,i=n<0?r.$from:r.$to,o=i.depth;for(;i.node(o).isInline;){if(!o)return!1;o--}return i.node(o).isTextblock?(t&&t(e.tr.setSelection(P.create(e.doc,n<0?i.start(o):i.end(o)))),!0):!1}}var Ko=rc(-1),Uo=rc(1);function ic(n,e=null){return function(t,r){let{$from:i,$to:o}=t.selection,s=i.blockRange(o),l=s&&gn(s,n,e);return l?(r&&r(t.tr.wrap(s,l).scrollIntoView()),!0):!1}}function Go(n,e=null){return function(t,r){let i=!1;for(let o=0;o<t.selection.ranges.length&&!i;o++){let{$from:{pos:s},$to:{pos:l}}=t.selection.ranges[o];t.doc.nodesBetween(s,l,(a,c)=>{if(i)return!1;if(!(!a.isTextblock||a.hasMarkup(n,e)))if(a.type==n)i=!0;else{let f=t.doc.resolve(c),u=f.index();i=f.parent.canReplaceWith(u,u+1,n)}})}if(!i)return!1;if(r){let o=t.tr;for(let s=0;s<t.selection.ranges.length;s++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[s];o.setBlockType(l,a,n,e)}r(o.scrollIntoView())}return!0}}function Yo(...n){return function(e,t,r){for(let i=0;i<n.length;i++)if(n[i](e,t,r))return!0;return!1}}var Bo=Yo(ai,Lo,Fo),_a=Yo(ai,Vo,$o),Et={Enter:Yo(Wo,Jo,_o,kp),"Mod-Enter":qo,Backspace:Bo,"Mod-Backspace":Bo,"Shift-Backspace":Bo,Delete:_a,"Mod-Delete":_a,"Mod-a":Sp},Mp={"Ctrl-h":Et.Backspace,"Alt-Backspace":Et["Mod-Backspace"],"Ctrl-d":Et.Delete,"Ctrl-Alt-Backspace":Et["Mod-Delete"],"Alt-Delete":Et["Mod-Delete"],"Alt-d":Et["Mod-Delete"],"Ctrl-a":Ko,"Ctrl-e":Uo};for(let n in Et)Mp[n]=Et[n];var Ey=typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform?os.platform()=="darwin":!1;function oc(n,e=null){return function(t,r){let{$from:i,$to:o}=t.selection,s=i.blockRange(o);if(!s)return!1;let l=r?t.tr:null;return Cp(l,s,n,e)?(r&&r(l.scrollIntoView()),!0):!1}}function Cp(n,e,t,r=null){let i=!1,o=e,s=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(t)&&e.startIndex==0){if(e.$from.index(e.depth-1)==0)return!1;let a=s.resolve(e.start-2);o=new zt(a,a,e.depth),e.endIndex<e.parent.childCount&&(e=new zt(e.$from,s.resolve(e.$to.end(e.depth)),e.depth)),i=!0}let l=gn(o,t,r,e);return l?(n&&Op(n,e,l,i,t),!0):!1}function Op(n,e,t,r,i){let o=S.empty;for(let f=t.length-1;f>=0;f--)o=S.from(t[f].type.create(t[f].attrs,o));n.step(new Q(e.start-(r?2:0),e.end,e.start,e.end,new M(o,0,0),t.length,!0));let s=0;for(let f=0;f<t.length;f++)t[f].type==i&&(s=f+1);let l=t.length-s,a=e.start+t.length-(r?2:0),c=e.parent;for(let f=e.startIndex,u=e.endIndex,d=!0;f<u;f++,d=!1)!d&&Be(n.doc,a,l)&&(n.split(a,l),a+=2*l),a+=c.child(f).nodeSize;return n}function sc(n){return function(e,t){let{$from:r,$to:i}=e.selection,o=r.blockRange(i,s=>s.childCount>0&&s.firstChild.type==n);return o?t?r.node(o.depth-1).type==n?Tp(e,t,n,o):Ep(e,t,o):!0:!1}}function Tp(n,e,t,r){let i=n.tr,o=r.end,s=r.$to.end(r.depth);o<s&&(i.step(new Q(o-1,s,o,s,new M(S.from(t.create(null,r.parent.copy())),1,0),1,!0)),r=new zt(i.doc.resolve(r.$from.pos),i.doc.resolve(s),r.depth));let l=ut(r);if(l==null)return!1;i.lift(r,l);let a=i.mapping.map(o,-1)-1;return qe(i.doc,a)&&i.join(a),e(i.scrollIntoView()),!0}function Ep(n,e,t){let r=n.tr,i=t.parent;for(let p=t.end,h=t.endIndex-1,m=t.startIndex;h>m;h--)p-=i.child(h).nodeSize,r.delete(p-1,p+1);let o=r.doc.resolve(t.start),s=o.nodeAfter;if(r.mapping.map(t.end)!=t.start+o.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==i.childCount,c=o.node(-1),f=o.index(-1);if(!c.canReplace(f+(l?0:1),f+1,s.content.append(a?S.empty:S.from(i))))return!1;let u=o.pos,d=u+s.nodeSize;return r.step(new Q(u-(l?1:0),d+(a?1:0),u+1,d-1,new M((l?S.empty:S.from(i.copy(S.empty))).append(a?S.empty:S.from(i.copy(S.empty))),l?0:1,a?0:1),l?0:1)),e(r.scrollIntoView()),!0}function lc(n){return function(e,t){let{$from:r,$to:i}=e.selection,o=r.blockRange(i,c=>c.childCount>0&&c.firstChild.type==n);if(!o)return!1;let s=o.startIndex;if(s==0)return!1;let l=o.parent,a=l.child(s-1);if(a.type!=n)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,f=S.from(c?n.create():null),u=new M(S.from(n.create(null,S.from(l.type.create(null,f)))),c?3:1,0),d=o.start,p=o.end;t(e.tr.step(new Q(d-(c?3:1),p,d,p,u,1,!0)).scrollIntoView())}return!0}}function yi(n){let{state:e,transaction:t}=n,{selection:r}=t,{doc:i}=t,{storedMarks:o}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return o},get selection(){return r},get doc(){return i},get tr(){return r=t.selection,i=t.doc,o=t.storedMarks,t}}}var On=class{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:e,editor:t,state:r}=this,{view:i}=t,{tr:o}=r,s=this.buildProps(o);return Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...f)=>{let u=a(...f)(s);return!o.getMeta("preventDispatch")&&!this.hasCustomState&&i.dispatch(o),u}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){let{rawCommands:r,editor:i,state:o}=this,{view:s}=i,l=[],a=!!e,c=e||o.tr,f=()=>(!a&&t&&!c.getMeta("preventDispatch")&&!this.hasCustomState&&s.dispatch(c),l.every(d=>d===!0)),u={...Object.fromEntries(Object.entries(r).map(([d,p])=>[d,(...m)=>{let g=this.buildProps(c,t),b=p(...m)(g);return l.push(b),u}])),run:f};return u}createCan(e){let{rawCommands:t,state:r}=this,i=!1,o=e||r.tr,s=this.buildProps(o,i);return{...Object.fromEntries(Object.entries(t).map(([a,c])=>[a,(...f)=>c(...f)({...s,dispatch:void 0})])),chain:()=>this.createChain(o,i)}}buildProps(e,t=!0){let{rawCommands:r,editor:i,state:o}=this,{view:s}=i,l={tr:e,editor:i,view:s,state:yi({state:o,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(r).map(([a,c])=>[a,(...f)=>c(...f)(l)]))}};return l}},es=class{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){let r=this.callbacks[e];return r&&r.forEach(i=>i.apply(this,t)),this}off(e,t){let r=this.callbacks[e];return r&&(t?this.callbacks[e]=r.filter(i=>i!==t):delete this.callbacks[e]),this}once(e,t){let r=(...i)=>{this.off(e,r),t.apply(this,i)};return this.on(e,r)}removeAllListeners(){this.callbacks={}}};function O(n,e,t){return n.config[e]===void 0&&n.parent?O(n.parent,e,t):typeof n.config[e]=="function"?n.config[e].bind({...t,parent:n.parent?O(n.parent,e,t):null}):n.config[e]}function bi(n){let e=n.filter(i=>i.type==="extension"),t=n.filter(i=>i.type==="node"),r=n.filter(i=>i.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:r}}function mc(n){let e=[],{nodeExtensions:t,markExtensions:r}=bi(n),i=[...t,...r],o={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return n.forEach(s=>{let l={name:s.name,options:s.options,storage:s.storage,extensions:i},a=O(s,"addGlobalAttributes",l);if(!a)return;a().forEach(f=>{f.types.forEach(u=>{Object.entries(f.attributes).forEach(([d,p])=>{e.push({type:u,name:d,attribute:{...o,...p}})})})})}),i.forEach(s=>{let l={name:s.name,options:s.options,storage:s.storage},a=O(s,"addAttributes",l);if(!a)return;let c=a();Object.entries(c).forEach(([f,u])=>{let d={...o,...u};typeof d?.default=="function"&&(d.default=d.default()),d?.isRequired&&d?.default===void 0&&delete d.default,e.push({type:s.name,name:f,attribute:d})})}),e}function le(n,e){if(typeof n=="string"){if(!e.nodes[n])throw Error(`There is no node type named '${n}'. Maybe you forgot to add the extension?`);return e.nodes[n]}return n}function V(...n){return n.filter(e=>!!e).reduce((e,t)=>{let r={...e};return Object.entries(t).forEach(([i,o])=>{if(!r[i]){r[i]=o;return}if(i==="class"){let l=o?String(o).split(" "):[],a=r[i]?r[i].split(" "):[],c=l.filter(f=>!a.includes(f));r[i]=[...a,...c].join(" ")}else if(i==="style"){let l=o?o.split(";").map(f=>f.trim()).filter(Boolean):[],a=r[i]?r[i].split(";").map(f=>f.trim()).filter(Boolean):[],c=new Map;a.forEach(f=>{let[u,d]=f.split(":").map(p=>p.trim());c.set(u,d)}),l.forEach(f=>{let[u,d]=f.split(":").map(p=>p.trim());c.set(u,d)}),r[i]=Array.from(c.entries()).map(([f,u])=>`${f}: ${u}`).join("; ")}else r[i]=o}),r},{})}function ts(n,e){return e.filter(t=>t.type===n.type.name).filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(n.attrs)||{}:{[t.name]:n.attrs[t.name]}).reduce((t,r)=>V(t,r),{})}function gc(n){return typeof n=="function"}function L(n,e=void 0,...t){return gc(n)?e?n.bind(e)(...t):n(...t):n}function Ap(n={}){return Object.keys(n).length===0&&n.constructor===Object}function Np(n){return typeof n!="string"?n:n.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(n):n==="true"?!0:n==="false"?!1:n}function ac(n,e){return"style"in n?n:{...n,getAttrs:t=>{let r=n.getAttrs?n.getAttrs(t):n.attrs;if(r===!1)return!1;let i=e.reduce((o,s)=>{let l=s.attribute.parseHTML?s.attribute.parseHTML(t):Np(t.getAttribute(s.name));return l==null?o:{...o,[s.name]:l}},{});return{...r,...i}}}}function cc(n){return Object.fromEntries(Object.entries(n).filter(([e,t])=>e==="attrs"&&Ap(t)?!1:t!=null))}function Dp(n,e){var t;let r=mc(n),{nodeExtensions:i,markExtensions:o}=bi(n),s=(t=i.find(c=>O(c,"topNode")))===null||t===void 0?void 0:t.name,l=Object.fromEntries(i.map(c=>{let f=r.filter(b=>b.type===c.name),u={name:c.name,options:c.options,storage:c.storage,editor:e},d=n.reduce((b,x)=>{let w=O(x,"extendNodeSchema",u);return{...b,...w?w(c):{}}},{}),p=cc({...d,content:L(O(c,"content",u)),marks:L(O(c,"marks",u)),group:L(O(c,"group",u)),inline:L(O(c,"inline",u)),atom:L(O(c,"atom",u)),selectable:L(O(c,"selectable",u)),draggable:L(O(c,"draggable",u)),code:L(O(c,"code",u)),whitespace:L(O(c,"whitespace",u)),linebreakReplacement:L(O(c,"linebreakReplacement",u)),defining:L(O(c,"defining",u)),isolating:L(O(c,"isolating",u)),attrs:Object.fromEntries(f.map(b=>{var x;return[b.name,{default:(x=b?.attribute)===null||x===void 0?void 0:x.default}]}))}),h=L(O(c,"parseHTML",u));h&&(p.parseDOM=h.map(b=>ac(b,f)));let m=O(c,"renderHTML",u);m&&(p.toDOM=b=>m({node:b,HTMLAttributes:ts(b,f)}));let g=O(c,"renderText",u);return g&&(p.toText=g),[c.name,p]})),a=Object.fromEntries(o.map(c=>{let f=r.filter(g=>g.type===c.name),u={name:c.name,options:c.options,storage:c.storage,editor:e},d=n.reduce((g,b)=>{let x=O(b,"extendMarkSchema",u);return{...g,...x?x(c):{}}},{}),p=cc({...d,inclusive:L(O(c,"inclusive",u)),excludes:L(O(c,"excludes",u)),group:L(O(c,"group",u)),spanning:L(O(c,"spanning",u)),code:L(O(c,"code",u)),attrs:Object.fromEntries(f.map(g=>{var b;return[g.name,{default:(b=g?.attribute)===null||b===void 0?void 0:b.default}]}))}),h=L(O(c,"parseHTML",u));h&&(p.parseDOM=h.map(g=>ac(g,f)));let m=O(c,"renderHTML",u);return m&&(p.toDOM=g=>m({mark:g,HTMLAttributes:ts(g,f)})),[c.name,p]}));return new Wn({topNode:s,nodes:l,marks:a})}function Xo(n,e){return e.nodes[n]||e.marks[n]||null}function fc(n,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===n.name):e}function as(n,e){let t=ct.fromSchema(e).serializeFragment(n),i=document.implementation.createHTMLDocument().createElement("div");return i.appendChild(t),i.innerHTML}var Pp=(n,e=500)=>{let t="",r=n.parentOffset;return n.parent.nodesBetween(Math.max(0,r-e),r,(i,o,s,l)=>{var a,c;let f=((c=(a=i.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:i,pos:o,parent:s,index:l}))||i.textContent||"%leaf%";t+=i.isAtom&&!i.isText?f:f.slice(0,Math.max(0,r-o))}),t};function cs(n){return Object.prototype.toString.call(n)==="[object RegExp]"}var Tn=class{constructor(e){this.find=e.find,this.handler=e.handler}},Ip=(n,e)=>{if(cs(e))return e.exec(n);let t=e(n);if(!t)return null;let r=[t.text];return r.index=t.index,r.input=n,r.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(t.replaceWith)),r};function ci(n){var e;let{editor:t,from:r,to:i,text:o,rules:s,plugin:l}=n,{view:a}=t;if(a.composing)return!1;let c=a.state.doc.resolve(r);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(d=>d.type.spec.code))return!1;let f=!1,u=Pp(c)+o;return s.forEach(d=>{if(f)return;let p=Ip(u,d.find);if(!p)return;let h=a.state.tr,m=yi({state:a.state,transaction:h}),g={from:r-(p[0].length-o.length),to:i},{commands:b,chain:x,can:w}=new On({editor:t,state:m});d.handler({state:m,range:g,match:p,commands:b,chain:x,can:w})===null||!h.steps.length||(h.setMeta(l,{transform:h,from:r,to:i,text:o}),a.dispatch(h),f=!0)}),f}function Rp(n){let{editor:e,rules:t}=n,r=new q({state:{init(){return null},apply(i,o,s){let l=i.getMeta(r);if(l)return l;let a=i.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:f}=a;typeof f=="string"?f=f:f=as(S.from(f),s.schema);let{from:u}=a,d=u+f.length;ci({editor:e,from:u,to:d,text:f,rules:t,plugin:r})}),i.selectionSet||i.docChanged?null:o}},props:{handleTextInput(i,o,s,l){return ci({editor:e,from:o,to:s,text:l,rules:t,plugin:r})},handleDOMEvents:{compositionend:i=>(setTimeout(()=>{let{$cursor:o}=i.state.selection;o&&ci({editor:e,from:o.pos,to:o.pos,text:"",rules:t,plugin:r})}),!1)},handleKeyDown(i,o){if(o.key!=="Enter")return!1;let{$cursor:s}=i.state.selection;return s?ci({editor:e,from:s.pos,to:s.pos,text:`
`,rules:t,plugin:r}):!1}},isInputRules:!0});return r}function Bp(n){return Object.prototype.toString.call(n).slice(8,-1)}function fi(n){return Bp(n)!=="Object"?!1:n.constructor===Object&&Object.getPrototypeOf(n)===Object.prototype}function vi(n,e){let t={...n};return fi(n)&&fi(e)&&Object.keys(e).forEach(r=>{fi(e[r])&&fi(n[r])?t[r]=vi(n[r],e[r]):t[r]=e[r]}),t}var _e=class n{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=L(O(this,"addOptions",{name:this.name}))),this.storage=L(O(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new n(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>vi(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new n(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=L(O(t,"addOptions",{name:t.name})),t.storage=L(O(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){let{tr:r}=e.state,i=e.state.selection.$from;if(i.pos===i.end()){let s=i.marks();if(!!!s.find(c=>c?.type.name===t.name))return!1;let a=s.find(c=>c?.type.name===t.name);return a&&r.removeStoredMark(a),r.insertText(" ",i.pos),e.view.dispatch(r),!0}return!1}};function Lp(n){return typeof n=="number"}var ns=class{constructor(e){this.find=e.find,this.handler=e.handler}},Fp=(n,e,t)=>{if(cs(e))return[...n.matchAll(e)];let r=e(n,t);return r?r.map(i=>{let o=[i.text];return o.index=i.index,o.input=n,o.data=i.data,i.replaceWith&&(i.text.includes(i.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),o.push(i.replaceWith)),o}):[]};function zp(n){let{editor:e,state:t,from:r,to:i,rule:o,pasteEvent:s,dropEvent:l}=n,{commands:a,chain:c,can:f}=new On({editor:e,state:t}),u=[];return t.doc.nodesBetween(r,i,(p,h)=>{if(!p.isTextblock||p.type.spec.code)return;let m=Math.max(r,h),g=Math.min(i,h+p.content.size),b=p.textBetween(m-h,g-h,void 0,"\uFFFC");Fp(b,o.find,s).forEach(w=>{if(w.index===void 0)return;let y=m+w.index+1,C=y+w[0].length,k={from:t.tr.mapping.map(y),to:t.tr.mapping.map(C)},I=o.handler({state:t,range:k,match:w,commands:a,chain:c,can:f,pasteEvent:s,dropEvent:l});u.push(I)})}),u.every(p=>p!==null)}var ui=null,Vp=n=>{var e;let t=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=t.clipboardData)===null||e===void 0||e.setData("text/html",n),t};function $p(n){let{editor:e,rules:t}=n,r=null,i=!1,o=!1,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}let a=({state:f,from:u,to:d,rule:p,pasteEvt:h})=>{let m=f.tr,g=yi({state:f,transaction:m});if(!(!zp({editor:e,state:g,from:Math.max(u-1,0),to:d.b-1,rule:p,pasteEvent:h,dropEvent:l})||!m.steps.length)){try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}return s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m}};return t.map(f=>new q({view(u){let d=h=>{var m;r=!((m=u.dom.parentElement)===null||m===void 0)&&m.contains(h.target)?u.dom.parentElement:null,r&&(ui=e)},p=()=>{ui&&(ui=null)};return window.addEventListener("dragstart",d),window.addEventListener("dragend",p),{destroy(){window.removeEventListener("dragstart",d),window.removeEventListener("dragend",p)}}},props:{handleDOMEvents:{drop:(u,d)=>{if(o=r===u.dom.parentElement,l=d,!o){let p=ui;p&&setTimeout(()=>{let h=p.state.selection;h&&p.commands.deleteRange({from:h.from,to:h.to})},10)}return!1},paste:(u,d)=>{var p;let h=(p=d.clipboardData)===null||p===void 0?void 0:p.getData("text/html");return s=d,i=!!h?.includes("data-pm-slice"),!1}}},appendTransaction:(u,d,p)=>{let h=u[0],m=h.getMeta("uiEvent")==="paste"&&!i,g=h.getMeta("uiEvent")==="drop"&&!o,b=h.getMeta("applyPasteRules"),x=!!b;if(!m&&!g&&!x)return;if(x){let{text:C}=b;typeof C=="string"?C=C:C=as(S.from(C),p.schema);let{from:k}=b,I=k+C.length,B=Vp(C);return a({rule:f,state:p,from:k,to:{b:I},pasteEvt:B})}let w=d.doc.content.findDiffStart(p.doc.content),y=d.doc.content.findDiffEnd(p.doc.content);if(!(!Lp(w)||!y||w===y.b))return a({rule:f,state:p,from:w,to:y,pasteEvt:s})}}))}function Hp(n){let e=n.filter((t,r)=>n.indexOf(t)!==r);return Array.from(new Set(e))}var rs=class n{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=n.resolve(e),this.schema=Dp(this.extensions,t),this.setupExtensions()}static resolve(e){let t=n.sort(n.flatten(e)),r=Hp(t.map(i=>i.name));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(i=>`'${i}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{let r={name:t.name,options:t.options,storage:t.storage},i=O(t,"addExtensions",r);return i?[t,...this.flatten(i())]:t}).flat(10)}static sort(e){return e.sort((r,i)=>{let o=O(r,"priority")||100,s=O(i,"priority")||100;return o>s?-1:o<s?1:0})}get commands(){return this.extensions.reduce((e,t)=>{let r={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:Xo(t.name,this.schema)},i=O(t,"addCommands",r);return i?{...e,...i()}:e},{})}get plugins(){let{editor:e}=this,t=n.sort([...this.extensions].reverse()),r=[],i=[],o=t.map(s=>{let l={name:s.name,options:s.options,storage:s.storage,editor:e,type:Xo(s.name,this.schema)},a=[],c=O(s,"addKeyboardShortcuts",l),f={};if(s.type==="mark"&&O(s,"exitable",l)&&(f.ArrowRight=()=>_e.handleExit({editor:e,mark:s})),c){let m=Object.fromEntries(Object.entries(c()).map(([g,b])=>[g,()=>b({editor:e})]));f={...f,...m}}let u=Ja(f);a.push(u);let d=O(s,"addInputRules",l);fc(s,e.options.enableInputRules)&&d&&r.push(...d());let p=O(s,"addPasteRules",l);fc(s,e.options.enablePasteRules)&&p&&i.push(...p());let h=O(s,"addProseMirrorPlugins",l);if(h){let m=h();a.push(...m)}return a}).flat();return[Rp({editor:e,rules:r}),...$p({editor:e,rules:i}),...o]}get attributes(){return mc(this.extensions)}get nodeViews(){let{editor:e}=this,{nodeExtensions:t}=bi(this.extensions);return Object.fromEntries(t.filter(r=>!!O(r,"addNodeView")).map(r=>{let i=this.attributes.filter(a=>a.type===r.name),o={name:r.name,options:r.options,storage:r.storage,editor:e,type:le(r.name,this.schema)},s=O(r,"addNodeView",o);if(!s)return[];let l=(a,c,f,u,d)=>{let p=ts(a,i);return s()({node:a,view:c,getPos:f,decorations:u,innerDecorations:d,editor:e,extension:r,HTMLAttributes:p})};return[r.name,l]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;let r={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:Xo(e.name,this.schema)};e.type==="mark"&&(!((t=L(O(e,"keepOnSplit",r)))!==null&&t!==void 0)||t)&&this.splittableMarks.push(e.name);let i=O(e,"onBeforeCreate",r),o=O(e,"onCreate",r),s=O(e,"onUpdate",r),l=O(e,"onSelectionUpdate",r),a=O(e,"onTransaction",r),c=O(e,"onFocus",r),f=O(e,"onBlur",r),u=O(e,"onDestroy",r);i&&this.editor.on("beforeCreate",i),o&&this.editor.on("create",o),s&&this.editor.on("update",s),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),f&&this.editor.on("blur",f),u&&this.editor.on("destroy",u)})}},te=class n{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=L(O(this,"addOptions",{name:this.name}))),this.storage=L(O(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new n(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>vi(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new n({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=L(O(t,"addOptions",{name:t.name})),t.storage=L(O(t,"addStorage",{name:t.name,options:t.options})),t}};function yc(n,e,t){let{from:r,to:i}=e,{blockSeparator:o=`

`,textSerializers:s={}}=t||{},l="";return n.nodesBetween(r,i,(a,c,f,u)=>{var d;a.isBlock&&c>r&&(l+=o);let p=s?.[a.type.name];if(p)return f&&(l+=p({node:a,pos:c,parent:f,index:u,range:e})),!1;a.isText&&(l+=(d=a?.text)===null||d===void 0?void 0:d.slice(Math.max(r,c)-c,i-c))}),l}function bc(n){return Object.fromEntries(Object.entries(n.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}var Wp=te.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new q({key:new X("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:n}=this,{state:e,schema:t}=n,{doc:r,selection:i}=e,{ranges:o}=i,s=Math.min(...o.map(f=>f.$from.pos)),l=Math.max(...o.map(f=>f.$to.pos)),a=bc(t);return yc(r,{from:s,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),jp=()=>({editor:n,view:e})=>(requestAnimationFrame(()=>{var t;n.isDestroyed||(e.dom.blur(),(t=window?.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),qp=(n=!1)=>({commands:e})=>e.setContent("",n),Jp=()=>({state:n,tr:e,dispatch:t})=>{let{selection:r}=e,{ranges:i}=r;return t&&i.forEach(({$from:o,$to:s})=>{n.doc.nodesBetween(o.pos,s.pos,(l,a)=>{if(l.type.isText)return;let{doc:c,mapping:f}=e,u=c.resolve(f.map(a)),d=c.resolve(f.map(a+l.nodeSize)),p=u.blockRange(d);if(!p)return;let h=ut(p);if(l.type.isTextblock){let{defaultType:m}=u.parent.contentMatchAt(u.index());e.setNodeMarkup(p.start,m)}(h||h===0)&&e.lift(p,h)})}),!0},_p=n=>e=>n(e),Kp=()=>({state:n,dispatch:e})=>Jo(n,e),Up=(n,e)=>({editor:t,tr:r})=>{let{state:i}=t,o=i.doc.slice(n.from,n.to);r.deleteRange(n.from,n.to);let s=r.mapping.map(e);return r.insert(s,o.content),r.setSelection(new P(r.doc.resolve(s-1))),!0},Gp=()=>({tr:n,dispatch:e})=>{let{selection:t}=n,r=t.$anchor.node();if(r.content.size>0)return!1;let i=n.selection.$anchor;for(let o=i.depth;o>0;o-=1)if(i.node(o).type===r.type){if(e){let l=i.before(o),a=i.after(o);n.delete(l,a).scrollIntoView()}return!0}return!1},Yp=n=>({tr:e,state:t,dispatch:r})=>{let i=le(n,t.schema),o=e.selection.$anchor;for(let s=o.depth;s>0;s-=1)if(o.node(s).type===i){if(r){let a=o.before(s),c=o.after(s);e.delete(a,c).scrollIntoView()}return!0}return!1},Xp=n=>({tr:e,dispatch:t})=>{let{from:r,to:i}=n;return t&&e.delete(r,i),!0},Zp=()=>({state:n,dispatch:e})=>ai(n,e),Qp=()=>({commands:n})=>n.keyboardShortcut("Enter"),eh=()=>({state:n,dispatch:e})=>qo(n,e);function hi(n,e,t={strict:!0}){let r=Object.keys(e);return r.length?r.every(i=>t.strict?e[i]===n[i]:cs(e[i])?e[i].test(n[i]):e[i]===n[i]):!0}function vc(n,e,t={}){return n.find(r=>r.type===e&&hi(Object.fromEntries(Object.keys(t).map(i=>[i,r.attrs[i]])),t))}function uc(n,e,t={}){return!!vc(n,e,t)}function fs(n,e,t){var r;if(!n||!e)return;let i=n.parent.childAfter(n.parentOffset);if((!i.node||!i.node.marks.some(f=>f.type===e))&&(i=n.parent.childBefore(n.parentOffset)),!i.node||!i.node.marks.some(f=>f.type===e)||(t=t||((r=i.node.marks[0])===null||r===void 0?void 0:r.attrs),!vc([...i.node.marks],e,t)))return;let s=i.index,l=n.start()+i.offset,a=s+1,c=l+i.node.nodeSize;for(;s>0&&uc([...n.parent.child(s-1).marks],e,t);)s-=1,l-=n.parent.child(s).nodeSize;for(;a<n.parent.childCount&&uc([...n.parent.child(a).marks],e,t);)c+=n.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function At(n,e){if(typeof n=="string"){if(!e.marks[n])throw Error(`There is no mark type named '${n}'. Maybe you forgot to add the extension?`);return e.marks[n]}return n}var th=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let o=At(n,r.schema),{doc:s,selection:l}=t,{$from:a,from:c,to:f}=l;if(i){let u=fs(a,o,e);if(u&&u.from<=c&&u.to>=f){let d=P.create(s,u.from,u.to);t.setSelection(d)}}return!0},nh=n=>e=>{let t=typeof n=="function"?n(e):n;for(let r=0;r<t.length;r+=1)if(t[r](e))return!0;return!1};function xc(n){return n instanceof P}function Yt(n=0,e=0,t=0){return Math.min(Math.max(n,e),t)}function kc(n,e=null){if(!e)return null;let t=D.atStart(n),r=D.atEnd(n);if(e==="start"||e===!0)return t;if(e==="end")return r;let i=t.from,o=r.to;return e==="all"?P.create(n,Yt(0,i,o),Yt(n.content.size,i,o)):P.create(n,Yt(e,i,o),Yt(e,i,o))}var rh=(n=null,e={})=>({editor:t,view:r,tr:i,dispatch:o})=>{e={scrollIntoView:!0,...e};let s=()=>{r.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(r.focus(),e?.scrollIntoView&&t.commands.scrollIntoView())})};if(r.hasFocus()&&n===null||n===!1)return!0;if(o&&n===null&&!xc(t.state.selection))return s(),!0;let l=kc(i.doc,n)||t.state.selection,a=t.state.selection.eq(l);return o&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),s()),!0},ih=(n,e)=>t=>n.every((r,i)=>e(r,{...t,index:i})),oh=(n,e)=>({tr:t,commands:r})=>r.insertContentAt({from:t.selection.from,to:t.selection.to},n,e),Sc=n=>{let e=n.childNodes;for(let t=e.length-1;t>=0;t-=1){let r=e[t];r.nodeType===3&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?n.removeChild(r):r.nodeType===1&&Sc(r)}return n};function di(n){let e=`<body>${n}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return Sc(t)}function mi(n,e,t){if(n instanceof Re||n instanceof S)return n;t={slice:!0,parseOptions:{},...t};let r=typeof n=="object"&&n!==null,i=typeof n=="string";if(r)try{if(Array.isArray(n)&&n.length>0)return S.fromArray(n.map(l=>e.nodeFromJSON(l)));let s=e.nodeFromJSON(n);return t.errorOnInvalidContent&&s.check(),s}catch(o){if(t.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:o});return console.warn("[tiptap warn]: Invalid content.","Passed value:",n,"Error:",o),mi("",e,t)}if(i){if(t.errorOnInvalidContent){let s=!1,l="",a=new Wn({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(s=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(t.slice?at.fromSchema(a).parseSlice(di(n),t.parseOptions):at.fromSchema(a).parse(di(n),t.parseOptions),t.errorOnInvalidContent&&s)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}let o=at.fromSchema(e);return t.slice?o.parseSlice(di(n),t.parseOptions).content:o.parse(di(n),t.parseOptions)}return mi("",e,t)}function sh(n,e,t){let r=n.steps.length-1;if(r<e)return;let i=n.steps[r];if(!(i instanceof be||i instanceof Q))return;let o=n.mapping.maps[r],s=0;o.forEach((l,a,c,f)=>{s===0&&(s=f)}),n.setSelection(D.near(n.doc.resolve(s),t))}var lh=n=>!("type"in n),ah=(n,e,t)=>({tr:r,dispatch:i,editor:o})=>{var s;if(i){t={parseOptions:o.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};let l;try{l=mi(e,o.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions},errorOnInvalidContent:(s=t.errorOnInvalidContent)!==null&&s!==void 0?s:o.options.enableContentCheck})}catch(h){return o.emit("contentError",{editor:o,error:h,disableCollaboration:()=>{o.storage.collaboration&&(o.storage.collaboration.isDisabled=!0)}}),!1}let{from:a,to:c}=typeof n=="number"?{from:n,to:n}:{from:n.from,to:n.to},f=!0,u=!0;if((lh(l)?l:[l]).forEach(h=>{h.check(),f=f?h.isText&&h.marks.length===0:!1,u=u?h.isBlock:!1}),a===c&&u){let{parent:h}=r.doc.resolve(a);h.isTextblock&&!h.type.spec.code&&!h.childCount&&(a-=1,c+=1)}let p;if(f){if(Array.isArray(e))p=e.map(h=>h.text||"").join("");else if(e instanceof S){let h="";e.forEach(m=>{m.text&&(h+=m.text)}),p=h}else typeof e=="object"&&e&&e.text?p=e.text:p=e;r.insertText(p,a,c)}else p=l,r.replaceWith(a,c,p);t.updateSelection&&sh(r,r.steps.length-1,-1),t.applyInputRules&&r.setMeta("applyInputRules",{from:a,text:p}),t.applyPasteRules&&r.setMeta("applyPasteRules",{from:a,text:p})}return!0},ch=()=>({state:n,dispatch:e})=>Za(n,e),fh=()=>({state:n,dispatch:e})=>Qa(n,e),uh=()=>({state:n,dispatch:e})=>Lo(n,e),dh=()=>({state:n,dispatch:e})=>Vo(n,e),ph=()=>({state:n,dispatch:e,tr:t})=>{try{let r=yn(n.doc,n.selection.$from.pos,-1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch{return!1}},hh=()=>({state:n,dispatch:e,tr:t})=>{try{let r=yn(n.doc,n.selection.$from.pos,1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch{return!1}},mh=()=>({state:n,dispatch:e})=>Ua(n,e),gh=()=>({state:n,dispatch:e})=>Ga(n,e);function wc(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}function Mc(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function yh(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t==="Space"&&(t=" ");let r,i,o,s;for(let l=0;l<e.length-1;l+=1){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))s=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))o=!0;else if(/^mod$/i.test(a))wc()||Mc()?s=!0:i=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return r&&(t=`Alt-${t}`),i&&(t=`Ctrl-${t}`),s&&(t=`Meta-${t}`),o&&(t=`Shift-${t}`),t}var bh=n=>({editor:e,view:t,tr:r,dispatch:i})=>{let o=yh(n).split(/-(?!$)/),s=o.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:s==="Space"?" ":s,altKey:o.includes("Alt"),ctrlKey:o.includes("Ctrl"),metaKey:o.includes("Meta"),shiftKey:o.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a?.steps.forEach(c=>{let f=c.map(r.mapping);f&&i&&r.maybeStep(f)}),!0};function cr(n,e,t={}){let{from:r,to:i,empty:o}=n.selection,s=e?le(e,n.schema):null,l=[];n.doc.nodesBetween(r,i,(u,d)=>{if(u.isText)return;let p=Math.max(r,d),h=Math.min(i,d+u.nodeSize);l.push({node:u,from:p,to:h})});let a=i-r,c=l.filter(u=>s?s.name===u.node.type.name:!0).filter(u=>hi(u.node.attrs,t,{strict:!1}));return o?!!c.length:c.reduce((u,d)=>u+d.to-d.from,0)>=a}var vh=(n,e={})=>({state:t,dispatch:r})=>{let i=le(n,t.schema);return cr(t,i,e)?ec(t,r):!1},xh=()=>({state:n,dispatch:e})=>_o(n,e),kh=n=>({state:e,dispatch:t})=>{let r=le(n,e.schema);return sc(r)(e,t)},Sh=()=>({state:n,dispatch:e})=>Wo(n,e);function xi(n,e){return e.nodes[n]?"node":e.marks[n]?"mark":null}function dc(n,e){let t=typeof e=="string"?[e]:e;return Object.keys(n).reduce((r,i)=>(t.includes(i)||(r[i]=n[i]),r),{})}var wh=(n,e)=>({tr:t,state:r,dispatch:i})=>{let o=null,s=null,l=xi(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(o=le(n,r.schema)),l==="mark"&&(s=At(n,r.schema)),i&&t.selection.ranges.forEach(a=>{r.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,f)=>{o&&o===c.type&&t.setNodeMarkup(f,void 0,dc(c.attrs,e)),s&&c.marks.length&&c.marks.forEach(u=>{s===u.type&&t.addMark(f,f+c.nodeSize,s.create(dc(u.attrs,e)))})})}),!0):!1},Mh=()=>({tr:n,dispatch:e})=>(e&&n.scrollIntoView(),!0),Ch=()=>({tr:n,dispatch:e})=>{if(e){let t=new we(n.doc);n.setSelection(t)}return!0},Oh=()=>({state:n,dispatch:e})=>Fo(n,e),Th=()=>({state:n,dispatch:e})=>$o(n,e),Eh=()=>({state:n,dispatch:e})=>tc(n,e),Ah=()=>({state:n,dispatch:e})=>Uo(n,e),Nh=()=>({state:n,dispatch:e})=>Ko(n,e);function is(n,e,t={},r={}){return mi(n,e,{slice:!1,parseOptions:t,errorOnInvalidContent:r.errorOnInvalidContent})}var Dh=(n,e=!1,t={},r={})=>({editor:i,tr:o,dispatch:s,commands:l})=>{var a,c;let{doc:f}=o;if(t.preserveWhitespace!=="full"){let u=is(n,i.schema,t,{errorOnInvalidContent:(a=r.errorOnInvalidContent)!==null&&a!==void 0?a:i.options.enableContentCheck});return s&&o.replaceWith(0,f.content.size,u).setMeta("preventUpdate",!e),!0}return s&&o.setMeta("preventUpdate",!e),l.insertContentAt({from:0,to:f.content.size},n,{parseOptions:t,errorOnInvalidContent:(c=r.errorOnInvalidContent)!==null&&c!==void 0?c:i.options.enableContentCheck})};function Cc(n,e){let t=At(e,n.schema),{from:r,to:i,empty:o}=n.selection,s=[];o?(n.storedMarks&&s.push(...n.storedMarks),s.push(...n.selection.$head.marks())):n.doc.nodesBetween(r,i,a=>{s.push(...a.marks)});let l=s.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function Ph(n){for(let e=0;e<n.edgeCount;e+=1){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function Ih(n,e){for(let t=n.depth;t>0;t-=1){let r=n.node(t);if(e(r))return{pos:t>0?n.before(t):0,start:n.start(t),depth:t,node:r}}}function us(n){return e=>Ih(e.$from,n)}function Rh(n,e){let t={from:0,to:n.content.size};return yc(n,t,e)}function Bh(n,e){let t=le(e,n.schema),{from:r,to:i}=n.selection,o=[];n.doc.nodesBetween(r,i,l=>{o.push(l)});let s=o.reverse().find(l=>l.type.name===t.name);return s?{...s.attrs}:{}}function Lh(n,e){let t=xi(typeof e=="string"?e:e.name,n.schema);return t==="node"?Bh(n,e):t==="mark"?Cc(n,e):{}}function Oc(n,e,t){let r=[];return n===e?t.resolve(n).marks().forEach(i=>{let o=t.resolve(n),s=fs(o,i.type);s&&r.push({mark:i,...s})}):t.nodesBetween(n,e,(i,o)=>{!i||i?.nodeSize===void 0||r.push(...i.marks.map(s=>({from:o,to:o+i.nodeSize,mark:s})))}),r}function pi(n,e,t){return Object.fromEntries(Object.entries(t).filter(([r])=>{let i=n.find(o=>o.type===e&&o.name===r);return i?i.attribute.keepOnSplit:!1}))}function ss(n,e,t={}){let{empty:r,ranges:i}=n.selection,o=e?At(e,n.schema):null;if(r)return!!(n.storedMarks||n.selection.$from.marks()).filter(u=>o?o.name===u.type.name:!0).find(u=>hi(u.attrs,t,{strict:!1}));let s=0,l=[];if(i.forEach(({$from:u,$to:d})=>{let p=u.pos,h=d.pos;n.doc.nodesBetween(p,h,(m,g)=>{if(!m.isText&&!m.marks.length)return;let b=Math.max(p,g),x=Math.min(h,g+m.nodeSize),w=x-b;s+=w,l.push(...m.marks.map(y=>({mark:y,from:b,to:x})))})}),s===0)return!1;let a=l.filter(u=>o?o.name===u.mark.type.name:!0).filter(u=>hi(u.mark.attrs,t,{strict:!1})).reduce((u,d)=>u+d.to-d.from,0),c=l.filter(u=>o?u.mark.type!==o&&u.mark.type.excludes(o):!0).reduce((u,d)=>u+d.to-d.from,0);return(a>0?a+c:a)>=s}function Fh(n,e,t={}){if(!e)return cr(n,null,t)||ss(n,null,t);let r=xi(e,n.schema);return r==="node"?cr(n,e,t):r==="mark"?ss(n,e,t):!1}function pc(n,e){let{nodeExtensions:t}=bi(e),r=t.find(s=>s.name===n);if(!r)return!1;let i={name:r.name,options:r.options,storage:r.storage},o=L(O(r,"group",i));return typeof o!="string"?!1:o.split(" ").includes("list")}function fr(n,{checkChildren:e=!0,ignoreWhitespace:t=!1}={}){var r;if(t){if(n.type.name==="hardBreak")return!0;if(n.isText)return/^\s*$/m.test((r=n.text)!==null&&r!==void 0?r:"")}if(n.isText)return!n.text;if(n.isAtom||n.isLeaf)return!1;if(n.content.childCount===0)return!0;if(e){let i=!0;return n.content.forEach(o=>{i!==!1&&(fr(o,{ignoreWhitespace:t,checkChildren:e})||(i=!1))}),i}return!1}function Tc(n){return n instanceof A}function zh(n,e,t){var r;let{selection:i}=e,o=null;if(xc(i)&&(o=i.$cursor),o){let l=(r=n.storedMarks)!==null&&r!==void 0?r:o.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}let{ranges:s}=i;return s.some(({$from:l,$to:a})=>{let c=l.depth===0?n.doc.inlineContent&&n.doc.type.allowsMarkType(t):!1;return n.doc.nodesBetween(l.pos,a.pos,(f,u,d)=>{if(c)return!1;if(f.isInline){let p=!d||d.type.allowsMarkType(t),h=!!t.isInSet(f.marks)||!f.marks.some(m=>m.type.excludes(t));c=p&&h}return!c}),c})}var Vh=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let{selection:o}=t,{empty:s,ranges:l}=o,a=At(n,r.schema);if(i)if(s){let c=Cc(r,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{let f=c.$from.pos,u=c.$to.pos;r.doc.nodesBetween(f,u,(d,p)=>{let h=Math.max(p,f),m=Math.min(p+d.nodeSize,u);d.marks.find(b=>b.type===a)?d.marks.forEach(b=>{a===b.type&&t.addMark(h,m,a.create({...b.attrs,...e}))}):t.addMark(h,m,a.create(e))})});return zh(r,t,a)},$h=(n,e)=>({tr:t})=>(t.setMeta(n,e),!0),Hh=(n,e={})=>({state:t,dispatch:r,chain:i})=>{let o=le(n,t.schema),s;return t.selection.$anchor.sameParent(t.selection.$head)&&(s=t.selection.$anchor.parent.attrs),o.isTextblock?i().command(({commands:l})=>Go(o,{...s,...e})(t)?!0:l.clearNodes()).command(({state:l})=>Go(o,{...s,...e})(l,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},Wh=n=>({tr:e,dispatch:t})=>{if(t){let{doc:r}=e,i=Yt(n,0,r.content.size),o=A.create(r,i);e.setSelection(o)}return!0},jh=n=>({tr:e,dispatch:t})=>{if(t){let{doc:r}=e,{from:i,to:o}=typeof n=="number"?{from:n,to:n}:n,s=P.atStart(r).from,l=P.atEnd(r).to,a=Yt(i,s,l),c=Yt(o,s,l),f=P.create(r,a,c);e.setSelection(f)}return!0},qh=n=>({state:e,dispatch:t})=>{let r=le(n,e.schema);return lc(r)(e,t)};function hc(n,e){let t=n.storedMarks||n.selection.$to.parentOffset&&n.selection.$from.marks();if(t){let r=t.filter(i=>e?.includes(i.type.name));n.tr.ensureMarks(r)}}var Jh=({keepMarks:n=!0}={})=>({tr:e,state:t,dispatch:r,editor:i})=>{let{selection:o,doc:s}=e,{$from:l,$to:a}=o,c=i.extensionManager.attributes,f=pi(c,l.node().type.name,l.node().attrs);if(o instanceof A&&o.node.isBlock)return!l.parentOffset||!Be(s,l.pos)?!1:(r&&(n&&hc(t,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;let u=a.parentOffset===a.parent.content.size,d=l.depth===0?void 0:Ph(l.node(-1).contentMatchAt(l.indexAfter(-1))),p=u&&d?[{type:d,attrs:f}]:void 0,h=Be(e.doc,e.mapping.map(l.pos),1,p);if(!p&&!h&&Be(e.doc,e.mapping.map(l.pos),1,d?[{type:d}]:void 0)&&(h=!0,p=d?[{type:d,attrs:f}]:void 0),r){if(h&&(o instanceof P&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,p),d&&!u&&!l.parentOffset&&l.parent.type!==d)){let m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,d)&&e.setNodeMarkup(e.mapping.map(l.before()),d)}n&&hc(t,i.extensionManager.splittableMarks),e.scrollIntoView()}return h},_h=(n,e={})=>({tr:t,state:r,dispatch:i,editor:o})=>{var s;let l=le(n,r.schema),{$from:a,$to:c}=r.selection,f=r.selection.node;if(f&&f.isBlock||a.depth<2||!a.sameParent(c))return!1;let u=a.node(-1);if(u.type!==l)return!1;let d=o.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(i){let b=S.empty,x=a.index(-1)?1:a.index(-2)?2:3;for(let B=a.depth-x;B>=a.depth-3;B-=1)b=S.from(a.node(B).copy(b));let w=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,y={...pi(d,a.node().type.name,a.node().attrs),...e},C=((s=l.contentMatch.defaultType)===null||s===void 0?void 0:s.createAndFill(y))||void 0;b=b.append(S.from(l.createAndFill(null,C)||void 0));let k=a.before(a.depth-(x-1));t.replace(k,a.after(-w),new M(b,4-x,0));let I=-1;t.doc.nodesBetween(k,t.doc.content.size,(B,T)=>{if(I>-1)return!1;B.isTextblock&&B.content.size===0&&(I=T+1)}),I>-1&&t.setSelection(P.near(t.doc.resolve(I))),t.scrollIntoView()}return!0}let p=c.pos===a.end()?u.contentMatchAt(0).defaultType:null,h={...pi(d,u.type.name,u.attrs),...e},m={...pi(d,a.node().type.name,a.node().attrs),...e};t.delete(a.pos,c.pos);let g=p?[{type:l,attrs:h},{type:p,attrs:m}]:[{type:l,attrs:h}];if(!Be(t.doc,a.pos,2))return!1;if(i){let{selection:b,storedMarks:x}=r,{splittableMarks:w}=o.extensionManager,y=x||b.$to.parentOffset&&b.$from.marks();if(t.split(a.pos,2,g).scrollIntoView(),!y||!i)return!0;let C=y.filter(k=>w.includes(k.type.name));t.ensureMarks(C)}return!0},Zo=(n,e)=>{let t=us(s=>s.type===e)(n.selection);if(!t)return!0;let r=n.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(r===void 0)return!0;let i=n.doc.nodeAt(r);return t.node.type===i?.type&&qe(n.doc,t.pos)&&n.join(t.pos),!0},Qo=(n,e)=>{let t=us(s=>s.type===e)(n.selection);if(!t)return!0;let r=n.doc.resolve(t.start).after(t.depth);if(r===void 0)return!0;let i=n.doc.nodeAt(r);return t.node.type===i?.type&&qe(n.doc,r)&&n.join(r),!0},Kh=(n,e,t,r={})=>({editor:i,tr:o,state:s,dispatch:l,chain:a,commands:c,can:f})=>{let{extensions:u,splittableMarks:d}=i.extensionManager,p=le(n,s.schema),h=le(e,s.schema),{selection:m,storedMarks:g}=s,{$from:b,$to:x}=m,w=b.blockRange(x),y=g||m.$to.parentOffset&&m.$from.marks();if(!w)return!1;let C=us(k=>pc(k.type.name,u))(m);if(w.depth>=1&&C&&w.depth-C.depth<=1){if(C.node.type===p)return c.liftListItem(h);if(pc(C.node.type.name,u)&&p.validContent(C.node.content)&&l)return a().command(()=>(o.setNodeMarkup(C.pos,p),!0)).command(()=>Zo(o,p)).command(()=>Qo(o,p)).run()}return!t||!y||!l?a().command(()=>f().wrapInList(p,r)?!0:c.clearNodes()).wrapInList(p,r).command(()=>Zo(o,p)).command(()=>Qo(o,p)).run():a().command(()=>{let k=f().wrapInList(p,r),I=y.filter(B=>d.includes(B.type.name));return o.ensureMarks(I),k?!0:c.clearNodes()}).wrapInList(p,r).command(()=>Zo(o,p)).command(()=>Qo(o,p)).run()},Uh=(n,e={},t={})=>({state:r,commands:i})=>{let{extendEmptyMarkRange:o=!1}=t,s=At(n,r.schema);return ss(r,s,e)?i.unsetMark(s,{extendEmptyMarkRange:o}):i.setMark(s,e)},Gh=(n,e,t={})=>({state:r,commands:i})=>{let o=le(n,r.schema),s=le(e,r.schema),l=cr(r,o,t),a;return r.selection.$anchor.sameParent(r.selection.$head)&&(a=r.selection.$anchor.parent.attrs),l?i.setNode(s,a):i.setNode(o,{...a,...t})},Yh=(n,e={})=>({state:t,commands:r})=>{let i=le(n,t.schema);return cr(t,i,e)?r.lift(i):r.wrapIn(i,e)},Xh=()=>({state:n,dispatch:e})=>{let t=n.plugins;for(let r=0;r<t.length;r+=1){let i=t[r],o;if(i.spec.isInputRules&&(o=i.getState(n))){if(e){let s=n.tr,l=o.transform;for(let a=l.steps.length-1;a>=0;a-=1)s.step(l.steps[a].invert(l.docs[a]));if(o.text){let a=s.doc.resolve(o.from).marks();s.replaceWith(o.from,o.to,n.schema.text(o.text,a))}else s.delete(o.from,o.to)}return!0}}return!1},Zh=()=>({tr:n,dispatch:e})=>{let{selection:t}=n,{empty:r,ranges:i}=t;return r||e&&i.forEach(o=>{n.removeMark(o.$from.pos,o.$to.pos)}),!0},Qh=(n,e={})=>({tr:t,state:r,dispatch:i})=>{var o;let{extendEmptyMarkRange:s=!1}=e,{selection:l}=t,a=At(n,r.schema),{$from:c,empty:f,ranges:u}=l;if(!i)return!0;if(f&&s){let{from:d,to:p}=l,h=(o=c.marks().find(g=>g.type===a))===null||o===void 0?void 0:o.attrs,m=fs(c,a,h);m&&(d=m.from,p=m.to),t.removeMark(d,p,a)}else u.forEach(d=>{t.removeMark(d.$from.pos,d.$to.pos,a)});return t.removeStoredMark(a),!0},em=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let o=null,s=null,l=xi(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(o=le(n,r.schema)),l==="mark"&&(s=At(n,r.schema)),i&&t.selection.ranges.forEach(a=>{let c=a.$from.pos,f=a.$to.pos,u,d,p,h;t.selection.empty?r.doc.nodesBetween(c,f,(m,g)=>{o&&o===m.type&&(p=Math.max(g,c),h=Math.min(g+m.nodeSize,f),u=g,d=m)}):r.doc.nodesBetween(c,f,(m,g)=>{g<c&&o&&o===m.type&&(p=Math.max(g,c),h=Math.min(g+m.nodeSize,f),u=g,d=m),g>=c&&g<=f&&(o&&o===m.type&&t.setNodeMarkup(g,void 0,{...m.attrs,...e}),s&&m.marks.length&&m.marks.forEach(b=>{if(s===b.type){let x=Math.max(g,c),w=Math.min(g+m.nodeSize,f);t.addMark(x,w,s.create({...b.attrs,...e}))}}))}),d&&(u!==void 0&&t.setNodeMarkup(u,void 0,{...d.attrs,...e}),s&&d.marks.length&&d.marks.forEach(m=>{s===m.type&&t.addMark(p,h,s.create({...m.attrs,...e}))}))}),!0):!1},tm=(n,e={})=>({state:t,dispatch:r})=>{let i=le(n,t.schema);return ic(i,e)(t,r)},nm=(n,e={})=>({state:t,dispatch:r})=>{let i=le(n,t.schema);return oc(i,e)(t,r)},rm=Object.freeze({__proto__:null,blur:jp,clearContent:qp,clearNodes:Jp,command:_p,createParagraphNear:Kp,cut:Up,deleteCurrentNode:Gp,deleteNode:Yp,deleteRange:Xp,deleteSelection:Zp,enter:Qp,exitCode:eh,extendMarkRange:th,first:nh,focus:rh,forEach:ih,insertContent:oh,insertContentAt:ah,joinBackward:uh,joinDown:fh,joinForward:dh,joinItemBackward:ph,joinItemForward:hh,joinTextblockBackward:mh,joinTextblockForward:gh,joinUp:ch,keyboardShortcut:bh,lift:vh,liftEmptyBlock:xh,liftListItem:kh,newlineInCode:Sh,resetAttributes:wh,scrollIntoView:Mh,selectAll:Ch,selectNodeBackward:Oh,selectNodeForward:Th,selectParentNode:Eh,selectTextblockEnd:Ah,selectTextblockStart:Nh,setContent:Dh,setMark:Vh,setMeta:$h,setNode:Hh,setNodeSelection:Wh,setTextSelection:jh,sinkListItem:qh,splitBlock:Jh,splitListItem:_h,toggleList:Kh,toggleMark:Uh,toggleNode:Gh,toggleWrap:Yh,undoInputRule:Xh,unsetAllMarks:Zh,unsetMark:Qh,updateAttributes:em,wrapIn:tm,wrapInList:nm}),im=te.create({name:"commands",addCommands(){return{...rm}}}),om=te.create({name:"drop",addProseMirrorPlugins(){return[new q({key:new X("tiptapDrop"),props:{handleDrop:(n,e,t,r)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:t,moved:r})}}})]}}),sm=te.create({name:"editable",addProseMirrorPlugins(){return[new q({key:new X("editable"),props:{editable:()=>this.editor.options.editable}})]}}),lm=te.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:n}=this;return[new q({key:new X("focusEvents"),props:{handleDOMEvents:{focus:(e,t)=>{n.isFocused=!0;let r=n.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,t)=>{n.isFocused=!1;let r=n.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),am=te.create({name:"keymap",addKeyboardShortcuts(){let n=()=>this.editor.commands.first(({commands:s})=>[()=>s.undoInputRule(),()=>s.command(({tr:l})=>{let{selection:a,doc:c}=l,{empty:f,$anchor:u}=a,{pos:d,parent:p}=u,h=u.parent.isTextblock&&d>0?l.doc.resolve(d-1):u,m=h.parent.type.spec.isolating,g=u.pos-u.parentOffset,b=m&&h.parent.childCount===1?g===u.pos:D.atStart(c).from===d;return!f||!p.type.isTextblock||p.textContent.length||!b||b&&u.parent.type.name==="paragraph"?!1:s.clearNodes()}),()=>s.deleteSelection(),()=>s.joinBackward(),()=>s.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:s})=>[()=>s.deleteSelection(),()=>s.deleteCurrentNode(),()=>s.joinForward(),()=>s.selectNodeForward()]),r={Enter:()=>this.editor.commands.first(({commands:s})=>[()=>s.newlineInCode(),()=>s.createParagraphNear(),()=>s.liftEmptyBlock(),()=>s.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:n,"Mod-Backspace":n,"Shift-Backspace":n,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},i={...r},o={...r,"Ctrl-h":n,"Alt-Backspace":n,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return wc()||Mc()?o:i},addProseMirrorPlugins(){return[new q({key:new X("clearDocument"),appendTransaction:(n,e,t)=>{let r=n.some(m=>m.docChanged)&&!e.doc.eq(t.doc),i=n.some(m=>m.getMeta("preventClearDocument"));if(!r||i)return;let{empty:o,from:s,to:l}=e.selection,a=D.atStart(e.doc).from,c=D.atEnd(e.doc).to;if(o||!(s===a&&l===c)||!fr(t.doc))return;let d=t.tr,p=yi({state:t,transaction:d}),{commands:h}=new On({editor:this.editor,state:p});if(h.clearNodes(),!!d.steps.length)return d}})]}}),cm=te.create({name:"paste",addProseMirrorPlugins(){return[new q({key:new X("tiptapPaste"),props:{handlePaste:(n,e,t)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:t})}}})]}}),fm=te.create({name:"tabindex",addProseMirrorPlugins(){return[new q({key:new X("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});var ls=class n{get name(){return this.node.type.name}constructor(e,t,r=!1,i=null){this.currentNode=null,this.actualDepth=null,this.isBlock=r,this.resolvedPos=e,this.editor=t,this.currentNode=i}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,r=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can\u2019t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}t=this.from+1,r=this.to-1}this.editor.commands.insertContentAt({from:t,to:r},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;let e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new n(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new n(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new n(e,this.editor)}get children(){let e=[];return this.node.content.forEach((t,r)=>{let i=t.isBlock&&!t.isTextblock,o=t.isAtom&&!t.isText,s=this.pos+r+(o?0:1),l=this.resolvedPos.doc.resolve(s);if(!i&&l.depth<=this.depth)return;let a=new n(l,this.editor,i,i?t:null);i&&(a.actualDepth=this.depth+1),e.push(new n(l,this.editor,i,i?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,t={}){let r=null,i=this.parent;for(;i&&!r;){if(i.node.type.name===e)if(Object.keys(t).length>0){let o=i.node.attrs,s=Object.keys(t);for(let l=0;l<s.length;l+=1){let a=s[l];if(o[a]!==t[a])break}}else r=i;i=i.parent}return r}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},r=!1){let i=[];if(!this.children||this.children.length===0)return i;let o=Object.keys(t);return this.children.forEach(s=>{r&&i.length>0||(s.node.type.name===e&&o.every(a=>t[a]===s.node.attrs[a])&&i.push(s),!(r&&i.length>0)&&(i=i.concat(s.querySelectorAll(e,t,r))))}),i}setAttribute(e){let{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}},um=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;function dm(n,e,t){let r=document.querySelector(`style[data-tiptap-style${t?`-${t}`:""}]`);if(r!==null)return r;let i=document.createElement("style");return e&&i.setAttribute("nonce",e),i.setAttribute(`data-tiptap-style${t?`-${t}`:""}`,""),i.innerHTML=n,document.getElementsByTagName("head")[0].appendChild(i),i}var gi=class extends es{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:t})=>{throw t},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:t,slice:r,moved:i})=>this.options.onDrop(t,r,i)),this.on("paste",({event:t,slice:r})=>this.options.onPaste(t,r)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=dm(um,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},!(!this.view||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){let r=gc(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],i=this.state.reconfigure({plugins:r});return this.view.updateState(i),i}unregisterPlugin(e){if(this.isDestroyed)return;let t=this.state.plugins,r=t;if([].concat(e).forEach(o=>{let s=typeof o=="string"?`${o}$`:o.key;r=t.filter(l=>!l.key.startsWith(s))}),t.length===r.length)return;let i=this.state.reconfigure({plugins:r});return this.view.updateState(i),i}createExtensionManager(){var e,t;let i=[...this.options.enableCoreExtensions?[sm,Wp.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),im,lm,am,fm,om,cm].filter(o=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[o.name]!==!1:!0):[],...this.options.extensions].filter(o=>["extension","node","mark"].includes(o?.type));this.extensionManager=new rs(i,this)}createCommandManager(){this.commandManager=new On({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=is(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(s){if(!(s instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(s.message))throw s;this.emit("contentError",{editor:this,error:s,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(l=>l.name!=="collaboration"),this.createExtensionManager()}}),t=is(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}let r=kc(t,this.options.autofocus);this.view=new ri(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...(e=this.options.editorProps)===null||e===void 0?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:_r.create({doc:t,selection:r||void 0})});let i=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(i),this.createNodeViews(),this.prependClass();let o=this.view.dom;o.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;let t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(s=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(s)});return}let t=this.state.apply(e),r=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),r&&this.emit("selectionUpdate",{editor:this,transaction:e});let i=e.getMeta("focus"),o=e.getMeta("blur");i&&this.emit("focus",{editor:this,event:i.event,transaction:e}),o&&this.emit("blur",{editor:this,event:o.event,transaction:e}),!(!e.docChanged||e.getMeta("preventUpdate"))&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return Lh(this.state,e)}isActive(e,t){let r=typeof e=="string"?e:null,i=typeof e=="string"?t:e;return Fh(this.state,r,i)}getJSON(){return this.state.doc.toJSON()}getHTML(){return as(this.state.doc.content,this.schema)}getText(e){let{blockSeparator:t=`

`,textSerializers:r={}}=e||{};return Rh(this.state.doc,{blockSeparator:t,textSerializers:{...bc(this.schema),...r}})}get isEmpty(){return fr(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){let e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var r;return((r=this.$doc)===null||r===void 0?void 0:r.querySelector(e,t))||null}$nodes(e,t){var r;return((r=this.$doc)===null||r===void 0?void 0:r.querySelectorAll(e,t))||null}$pos(e){let t=this.state.doc.resolve(e);return new ls(t,this)}get $doc(){return this.$pos(0)}};function Qe(n){return new Tn({find:n.find,handler:({state:e,range:t,match:r})=>{let i=L(n.getAttributes,void 0,r);if(i===!1||i===null)return null;let{tr:o}=e,s=r[r.length-1],l=r[0];if(s){let a=l.search(/\S/),c=t.from+l.indexOf(s),f=c+s.length;if(Oc(t.from,t.to,e.doc).filter(p=>p.mark.type.excluded.find(m=>m===n.type&&m!==p.mark.type)).filter(p=>p.to>c).length)return null;f<t.to&&o.delete(f,t.to),c>t.from&&o.delete(t.from+a,c);let d=t.from+a+s.length;o.addMark(t.from+a,d,n.type.create(i||{})),o.removeStoredMark(n.type)}}})}function Ec(n){return new Tn({find:n.find,handler:({state:e,range:t,match:r})=>{let i=L(n.getAttributes,void 0,r)||{},{tr:o}=e,s=t.from,l=t.to,a=n.type.create(i);if(r[1]){let c=r[0].lastIndexOf(r[1]),f=s+c;f>l?f=l:l=f+r[1].length;let u=r[0][r[0].length-1];o.insertText(u,s+r[0].length-1),o.replaceWith(f,l,a)}else if(r[0]){let c=n.type.isInline?s:s-1;o.insert(c,n.type.create(i)).delete(o.mapping.map(s),o.mapping.map(l))}o.scrollIntoView()}})}function ur(n){return new Tn({find:n.find,handler:({state:e,range:t,match:r})=>{let i=e.doc.resolve(t.from),o=L(n.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),n.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,n.type,o)}})}function Nt(n){return new Tn({find:n.find,handler:({state:e,range:t,match:r,chain:i})=>{let o=L(n.getAttributes,void 0,r)||{},s=e.tr.delete(t.from,t.to),a=s.doc.resolve(t.from).blockRange(),c=a&&gn(a,n.type,o);if(!c)return null;if(s.wrap(a,c),n.keepMarks&&n.editor){let{selection:u,storedMarks:d}=e,{splittableMarks:p}=n.editor.extensionManager,h=d||u.$to.parentOffset&&u.$from.marks();if(h){let m=h.filter(g=>p.includes(g.type.name));s.ensureMarks(m)}}if(n.keepAttributes){let u=n.type.name==="bulletList"||n.type.name==="orderedList"?"listItem":"taskList";i().updateAttributes(u,o).run()}let f=s.doc.resolve(t.from-1).nodeBefore;f&&f.type===n.type&&qe(s.doc,t.from-1)&&(!n.joinPredicate||n.joinPredicate(r,f))&&s.join(t.from-1)}})}var J=class n{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=L(O(this,"addOptions",{name:this.name}))),this.storage=L(O(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new n(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>vi(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new n(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=L(O(t,"addOptions",{name:t.name})),t.storage=L(O(t,"addStorage",{name:t.name,options:t.options})),t}};function et(n){return new ns({find:n.find,handler:({state:e,range:t,match:r,pasteEvent:i})=>{let o=L(n.getAttributes,void 0,r,i);if(o===!1||o===null)return null;let{tr:s}=e,l=r[r.length-1],a=r[0],c=t.to;if(l){let f=a.search(/\S/),u=t.from+a.indexOf(l),d=u+l.length;if(Oc(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(g=>g===n.type&&g!==h.mark.type)).filter(h=>h.to>u).length)return null;d<t.to&&s.delete(d,t.to),u>t.from&&s.delete(t.from+f,u),c=t.from+f+l.length,s.addMark(t.from+f,c,n.type.create(o||{})),s.removeStoredMark(n.type)}}})}function Ac(n){return n.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")}var pm=/^\s*>\s$/,Nc=J.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:n}){return["blockquote",V(this.options.HTMLAttributes,n),0]},addCommands(){return{setBlockquote:()=>({commands:n})=>n.wrapIn(this.name),toggleBlockquote:()=>({commands:n})=>n.toggleWrap(this.name),unsetBlockquote:()=>({commands:n})=>n.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[Nt({find:pm,type:this.type})]}});var hm=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,mm=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,gm=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,ym=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,Dc=_e.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:n=>n.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:n=>n.type.name===this.name},{style:"font-weight",getAttrs:n=>/^(bold(er)?|[5-9]\d{2,})$/.test(n)&&null}]},renderHTML({HTMLAttributes:n}){return["strong",V(this.options.HTMLAttributes,n),0]},addCommands(){return{setBold:()=>({commands:n})=>n.setMark(this.name),toggleBold:()=>({commands:n})=>n.toggleMark(this.name),unsetBold:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[Qe({find:hm,type:this.type}),Qe({find:gm,type:this.type})]},addPasteRules(){return[et({find:mm,type:this.type}),et({find:ym,type:this.type})]}});var bm="listItem",Pc="textStyle",Ic=/^\s*([-+*])\s$/,Rc=J.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:n}){return["ul",V(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleBulletList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(bm,this.editor.getAttributes(Pc)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let n=Nt({find:Ic,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(n=Nt({find:Ic,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(Pc),editor:this.editor})),[n]}});var vm=/(^|[^`])`([^`]+)`(?!`)/,xm=/(^|[^`])`([^`]+)`(?!`)/g,Bc=_e.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:n}){return["code",V(this.options.HTMLAttributes,n),0]},addCommands(){return{setCode:()=>({commands:n})=>n.setMark(this.name),toggleCode:()=>({commands:n})=>n.toggleMark(this.name),unsetCode:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[Qe({find:vm,type:this.type})]},addPasteRules(){return[et({find:xm,type:this.type})]}});var km=/^```([a-z]+)?[\s\n]$/,Sm=/^~~~([a-z]+)?[\s\n]$/,Lc=J.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:n=>{var e;let{languageClassPrefix:t}=this.options,o=[...((e=n.firstElementChild)===null||e===void 0?void 0:e.classList)||[]].filter(s=>s.startsWith(t)).map(s=>s.replace(t,""))[0];return o||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:n,HTMLAttributes:e}){return["pre",V(this.options.HTMLAttributes,e),["code",{class:n.attrs.language?this.options.languageClassPrefix+n.attrs.language:null},0]]},addCommands(){return{setCodeBlock:n=>({commands:e})=>e.setNode(this.name,n),toggleCodeBlock:n=>({commands:e})=>e.toggleNode(this.name,"paragraph",n)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:n,$anchor:e}=this.editor.state.selection,t=e.pos===1;return!n||e.parent.type.name!==this.name?!1:t||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:n})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:e}=n,{selection:t}=e,{$from:r,empty:i}=t;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith(`

`);return!o||!s?!1:n.chain().command(({tr:l})=>(l.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:n})=>{if(!this.options.exitOnArrowDown)return!1;let{state:e}=n,{selection:t,doc:r}=e,{$from:i,empty:o}=t;if(!o||i.parent.type!==this.type||!(i.parentOffset===i.parent.nodeSize-2))return!1;let l=i.after();return l===void 0?!1:r.nodeAt(l)?n.commands.command(({tr:c})=>(c.setSelection(D.near(r.resolve(l))),!0)):n.commands.exitCode()}}},addInputRules(){return[ur({find:km,type:this.type,getAttributes:n=>({language:n[1]})}),ur({find:Sm,type:this.type,getAttributes:n=>({language:n[1]})})]},addProseMirrorPlugins(){return[new q({key:new X("codeBlockVSCodeHandler"),props:{handlePaste:(n,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;let t=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,o=i?.mode;if(!t||!o)return!1;let{tr:s,schema:l}=n.state,a=l.text(t.replace(/\r\n?/g,`
`));return s.replaceSelectionWith(this.type.create({language:o},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(P.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),n.dispatch(s),!0}}})]}});var Fc=J.create({name:"doc",topNode:!0,content:"block+"});function zc(n={}){return new q({view(e){return new ds(e,n)}})}var ds=class{constructor(e,t){var r;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(r=t.width)!==null&&r!==void 0?r:1,this.color=t.color===!1?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(i=>{let o=s=>{this[i](s)};return e.dom.addEventListener(i,o),{name:i,handler:o}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){this.cursorPos!=null&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),t=!e.parent.inlineContent,r;if(t){let l=e.nodeBefore,a=e.nodeAfter;if(l||a){let c=this.editorView.nodeDOM(this.cursorPos-(l?l.nodeSize:0));if(c){let f=c.getBoundingClientRect(),u=l?f.bottom:f.top;l&&a&&(u=(u+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2),r={left:f.left,right:f.right,top:u-this.width/2,bottom:u+this.width/2}}}}if(!r){let l=this.editorView.coordsAtPos(this.cursorPos);r={left:l.left-this.width/2,right:l.left+this.width/2,top:l.top,bottom:l.bottom}}let i=this.editorView.dom.offsetParent;this.element||(this.element=i.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",t),this.element.classList.toggle("prosemirror-dropcursor-inline",!t);let o,s;if(!i||i==document.body&&getComputedStyle(i).position=="static")o=-pageXOffset,s=-pageYOffset;else{let l=i.getBoundingClientRect();o=l.left-i.scrollLeft,s=l.top-i.scrollTop}this.element.style.left=r.left-o+"px",this.element.style.top=r.top-s+"px",this.element.style.width=r.right-r.left+"px",this.element.style.height=r.bottom-r.top+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),r=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),i=r&&r.type.spec.disableDropCursor,o=typeof i=="function"?i(this.editorView,t,e):i;if(t&&!o){let s=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=Wr(this.editorView.state.doc,s,this.editorView.dragging.slice);l!=null&&(s=l)}this.setCursor(s),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){(e.target==this.editorView.dom||!this.editorView.dom.contains(e.relatedTarget))&&this.setCursor(null)}};var Vc=te.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[zc(this.options)]}});var ke=class n extends D{constructor(e){super(e,e)}map(e,t){let r=e.resolve(t.map(this.head));return n.valid(r)?new n(r):D.near(r)}content(){return M.empty}eq(e){return e instanceof n&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new n(e.resolve(t.pos))}getBookmark(){return new ps(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!wm(e)||!Mm(e))return!1;let r=t.type.spec.allowGapCursor;if(r!=null)return r;let i=t.contentMatchAt(e.index()).defaultType;return i&&i.isTextblock}static findGapCursorFrom(e,t,r=!1){e:for(;;){if(!r&&n.valid(e))return e;let i=e.pos,o=null;for(let s=e.depth;;s--){let l=e.node(s);if(t>0?e.indexAfter(s)<l.childCount:e.index(s)>0){o=l.child(t>0?e.indexAfter(s):e.index(s)-1);break}else if(s==0)return null;i+=t;let a=e.doc.resolve(i);if(n.valid(a))return a}for(;;){let s=t>0?o.firstChild:o.lastChild;if(!s){if(o.isAtom&&!o.isText&&!A.isSelectable(o)){e=e.doc.resolve(i+o.nodeSize*t),r=!1;continue e}break}o=s,i+=t;let l=e.doc.resolve(i);if(n.valid(l))return l}return null}}};ke.prototype.visible=!1;ke.findFrom=ke.findGapCursorFrom;D.jsonID("gapcursor",ke);var ps=class n{constructor(e){this.pos=e}map(e){return new n(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return ke.valid(t)?new ke(t):D.near(t)}};function wm(n){for(let e=n.depth;e>=0;e--){let t=n.index(e),r=n.node(e);if(t==0){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t-1);;i=i.lastChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function Mm(n){for(let e=n.depth;e>=0;e--){let t=n.indexAfter(e),r=n.node(e);if(t==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t);;i=i.firstChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function $c(){return new q({props:{decorations:Em,createSelectionBetween(n,e,t){return e.pos==t.pos&&ke.valid(t)?new ke(t):null},handleClick:Om,handleKeyDown:Cm,handleDOMEvents:{beforeinput:Tm}}})}var Cm=Ro({ArrowLeft:ki("horiz",-1),ArrowRight:ki("horiz",1),ArrowUp:ki("vert",-1),ArrowDown:ki("vert",1)});function ki(n,e){let t=n=="vert"?e>0?"down":"up":e>0?"right":"left";return function(r,i,o){let s=r.selection,l=e>0?s.$to:s.$from,a=s.empty;if(s instanceof P){if(!o.endOfTextblock(t)||l.depth==0)return!1;a=!1,l=r.doc.resolve(e>0?l.after():l.before())}let c=ke.findGapCursorFrom(l,e,a);return c?(i&&i(r.tr.setSelection(new ke(c))),!0):!1}}function Om(n,e,t){if(!n||!n.editable)return!1;let r=n.state.doc.resolve(e);if(!ke.valid(r))return!1;let i=n.posAtCoords({left:t.clientX,top:t.clientY});return i&&i.inside>-1&&A.isSelectable(n.state.doc.nodeAt(i.inside))?!1:(n.dispatch(n.state.tr.setSelection(new ke(r))),!0)}function Tm(n,e){if(e.inputType!="insertCompositionText"||!(n.state.selection instanceof ke))return!1;let{$from:t}=n.state.selection,r=t.parent.contentMatchAt(t.index()).findWrapping(n.state.schema.nodes.text);if(!r)return!1;let i=S.empty;for(let s=r.length-1;s>=0;s--)i=S.from(r[s].createAndFill(null,i));let o=n.state.tr.replace(t.pos,t.pos,new M(i,0,0));return o.setSelection(P.near(o.doc.resolve(t.pos+1))),n.dispatch(o),!1}function Em(n){if(!(n.selection instanceof ke))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",se.create(n.doc,[Ae.widget(n.selection.head,e,{key:"gapcursor"})])}var Hc=te.create({name:"gapCursor",addProseMirrorPlugins(){return[$c()]},extendNodeSchema(n){var e;let t={name:n.name,options:n.options,storage:n.storage};return{allowGapCursor:(e=L(O(n,"allowGapCursor",t)))!==null&&e!==void 0?e:null}}});var Wc=J.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:n}){return["br",V(this.options.HTMLAttributes,n)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:n,chain:e,state:t,editor:r})=>n.first([()=>n.exitCode(),()=>n.command(()=>{let{selection:i,storedMarks:o}=t;if(i.$from.parent.type.spec.isolating)return!1;let{keepMarks:s}=this.options,{splittableMarks:l}=r.extensionManager,a=o||i.$to.parentOffset&&i.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:f})=>{if(f&&a&&s){let u=a.filter(d=>l.includes(d.type.name));c.ensureMarks(u)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}});var jc=J.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(n=>({tag:`h${n}`,attrs:{level:n}}))},renderHTML({node:n,HTMLAttributes:e}){return[`h${this.options.levels.includes(n.attrs.level)?n.attrs.level:this.options.levels[0]}`,V(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.setNode(this.name,n):!1,toggleHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.toggleNode(this.name,"paragraph",n):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((n,e)=>({...n,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(n=>ur({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${n}})\\s$`),type:this.type,getAttributes:{level:n}}))}});var Si=200,he=function(){};he.prototype.append=function(e){return e.length?(e=he.from(e),!this.length&&e||e.length<Si&&this.leafAppend(e)||this.length<Si&&e.leafPrepend(this)||this.appendInner(e)):this};he.prototype.prepend=function(e){return e.length?he.from(e).append(this):this};he.prototype.appendInner=function(e){return new Am(this,e)};he.prototype.slice=function(e,t){return e===void 0&&(e=0),t===void 0&&(t=this.length),e>=t?he.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))};he.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};he.prototype.forEach=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length),t<=r?this.forEachInner(e,t,r,0):this.forEachInvertedInner(e,t,r,0)};he.prototype.map=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length);var i=[];return this.forEach(function(o,s){return i.push(e(o,s))},t,r),i};he.from=function(e){return e instanceof he?e:e&&e.length?new qc(e):he.empty};var qc=function(n){function e(r){n.call(this),this.values=r}n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(i,o){return i==0&&o==this.length?this:new e(this.values.slice(i,o))},e.prototype.getInner=function(i){return this.values[i]},e.prototype.forEachInner=function(i,o,s,l){for(var a=o;a<s;a++)if(i(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(i,o,s,l){for(var a=o-1;a>=s;a--)if(i(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(i){if(this.length+i.length<=Si)return new e(this.values.concat(i.flatten()))},e.prototype.leafPrepend=function(i){if(this.length+i.length<=Si)return new e(i.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(he);he.empty=new qc([]);var Am=function(n){function e(t,r){n.call(this),this.left=t,this.right=r,this.length=t.length+r.length,this.depth=Math.max(t.depth,r.depth)+1}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(r){return r<this.left.length?this.left.get(r):this.right.get(r-this.left.length)},e.prototype.forEachInner=function(r,i,o,s){var l=this.left.length;if(i<l&&this.left.forEachInner(r,i,Math.min(o,l),s)===!1||o>l&&this.right.forEachInner(r,Math.max(i-l,0),Math.min(this.length,o)-l,s+l)===!1)return!1},e.prototype.forEachInvertedInner=function(r,i,o,s){var l=this.left.length;if(i>l&&this.right.forEachInvertedInner(r,i-l,Math.max(o,l)-l,s+l)===!1||o<l&&this.left.forEachInvertedInner(r,Math.min(i,l),o,s)===!1)return!1},e.prototype.sliceInner=function(r,i){if(r==0&&i==this.length)return this;var o=this.left.length;return i<=o?this.left.slice(r,i):r>=o?this.right.slice(r-o,i-o):this.left.slice(r,o).append(this.right.slice(0,i-o))},e.prototype.leafAppend=function(r){var i=this.right.leafAppend(r);if(i)return new e(this.left,i)},e.prototype.leafPrepend=function(r){var i=this.left.leafPrepend(r);if(i)return new e(i,this.right)},e.prototype.appendInner=function(r){return this.left.depth>=Math.max(this.right.depth,r.depth)+1?new e(this.left,new e(this.right,r)):new e(this,r)},e}(he),hs=he;var Nm=500,Zt=class n{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let r=this.items.length;for(;;r--)if(this.items.get(r-1).selection){--r;break}let i,o;t&&(i=this.remapping(r,this.items.length),o=i.maps.length);let s=e.tr,l,a,c=[],f=[];return this.items.forEach((u,d)=>{if(!u.step){i||(i=this.remapping(r,d+1),o=i.maps.length),o--,f.push(u);return}if(i){f.push(new tt(u.map));let p=u.step.map(i.slice(o)),h;p&&s.maybeStep(p).doc&&(h=s.mapping.maps[s.mapping.maps.length-1],c.push(new tt(h,void 0,void 0,c.length+f.length))),o--,h&&i.appendMap(h,o)}else s.maybeStep(u.step);if(u.selection)return l=i?u.selection.map(i.slice(o)):u.selection,a=new n(this.items.slice(0,r).append(f.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:s,selection:l}}addTransform(e,t,r,i){let o=[],s=this.eventCount,l=this.items,a=!i&&l.length?l.get(l.length-1):null;for(let f=0;f<e.steps.length;f++){let u=e.steps[f].invert(e.docs[f]),d=new tt(e.mapping.maps[f],u,t),p;(p=a&&a.merge(d))&&(d=p,f?o.pop():l=l.slice(0,l.length-1)),o.push(d),t&&(s++,t=void 0),i||(a=d)}let c=s-r.depth;return c>Pm&&(l=Dm(l,c),s-=c),new n(l.append(o),s)}remapping(e,t){let r=new Kn;return this.items.forEach((i,o)=>{let s=i.mirrorOffset!=null&&o-i.mirrorOffset>=e?r.maps.length-i.mirrorOffset:void 0;r.appendMap(i.map,s)},e,t),r}addMaps(e){return this.eventCount==0?this:new n(this.items.append(e.map(t=>new tt(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let r=[],i=Math.max(0,this.items.length-t),o=e.mapping,s=e.steps.length,l=this.eventCount;this.items.forEach(d=>{d.selection&&l--},i);let a=t;this.items.forEach(d=>{let p=o.getMirror(--a);if(p==null)return;s=Math.min(s,p);let h=o.maps[p];if(d.step){let m=e.steps[p].invert(e.docs[p]),g=d.selection&&d.selection.map(o.slice(a+1,p));g&&l++,r.push(new tt(h,m,g))}else r.push(new tt(h))},i);let c=[];for(let d=t;d<s;d++)c.push(new tt(o.maps[d]));let f=this.items.slice(0,i).append(c).append(r),u=new n(f,l);return u.emptyItemCount()>Nm&&(u=u.compress(this.items.length-r.length)),u}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),r=t.maps.length,i=[],o=0;return this.items.forEach((s,l)=>{if(l>=e)i.push(s),s.selection&&o++;else if(s.step){let a=s.step.map(t.slice(r)),c=a&&a.getMap();if(r--,c&&t.appendMap(c,r),a){let f=s.selection&&s.selection.map(t.slice(r));f&&o++;let u=new tt(c.invert(),a,f),d,p=i.length-1;(d=i.length&&i[p].merge(u))?i[p]=d:i.push(u)}}else s.map&&r--},this.items.length,0),new n(hs.from(i.reverse()),o)}};Zt.empty=new Zt(hs.empty,0);function Dm(n,e){let t;return n.forEach((r,i)=>{if(r.selection&&e--==0)return t=i,!1}),n.slice(t)}var tt=class n{constructor(e,t,r,i){this.map=e,this.step=t,this.selection=r,this.mirrorOffset=i}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new n(t.getMap().invert(),t,this.selection)}}},nt=class{constructor(e,t,r,i,o){this.done=e,this.undone=t,this.prevRanges=r,this.prevTime=i,this.prevComposition=o}},Pm=20;function Im(n,e,t,r){let i=t.getMeta(Xt),o;if(i)return i.historyState;t.getMeta(Lm)&&(n=new nt(n.done,n.undone,null,0,-1));let s=t.getMeta("appendedTransaction");if(t.steps.length==0)return n;if(s&&s.getMeta(Xt))return s.getMeta(Xt).redo?new nt(n.done.addTransform(t,void 0,r,wi(e)),n.undone,Jc(t.mapping.maps),n.prevTime,n.prevComposition):new nt(n.done,n.undone.addTransform(t,void 0,r,wi(e)),null,n.prevTime,n.prevComposition);if(t.getMeta("addToHistory")!==!1&&!(s&&s.getMeta("addToHistory")===!1)){let l=t.getMeta("composition"),a=n.prevTime==0||!s&&n.prevComposition!=l&&(n.prevTime<(t.time||0)-r.newGroupDelay||!Rm(t,n.prevRanges)),c=s?ms(n.prevRanges,t.mapping):Jc(t.mapping.maps);return new nt(n.done.addTransform(t,a?e.selection.getBookmark():void 0,r,wi(e)),Zt.empty,c,t.time,l??n.prevComposition)}else return(o=t.getMeta("rebased"))?new nt(n.done.rebased(t,o),n.undone.rebased(t,o),ms(n.prevRanges,t.mapping),n.prevTime,n.prevComposition):new nt(n.done.addMaps(t.mapping.maps),n.undone.addMaps(t.mapping.maps),ms(n.prevRanges,t.mapping),n.prevTime,n.prevComposition)}function Rm(n,e){if(!e)return!1;if(!n.docChanged)return!0;let t=!1;return n.mapping.maps[0].forEach((r,i)=>{for(let o=0;o<e.length;o+=2)r<=e[o+1]&&i>=e[o]&&(t=!0)}),t}function Jc(n){let e=[];for(let t=n.length-1;t>=0&&e.length==0;t--)n[t].forEach((r,i,o,s)=>e.push(o,s));return e}function ms(n,e){if(!n)return null;let t=[];for(let r=0;r<n.length;r+=2){let i=e.map(n[r],1),o=e.map(n[r+1],-1);i<=o&&t.push(i,o)}return t}function Bm(n,e,t){let r=wi(e),i=Xt.get(e).spec.config,o=(t?n.undone:n.done).popEvent(e,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(t?n.done:n.undone).addTransform(o.transform,e.selection.getBookmark(),i,r),a=new nt(t?l:o.remaining,t?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(Xt,{redo:t,historyState:a})}var gs=!1,_c=null;function wi(n){let e=n.plugins;if(_c!=e){gs=!1,_c=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){gs=!0;break}}return gs}var Xt=new X("history"),Lm=new X("closeHistory");function Kc(n={}){return n={depth:n.depth||100,newGroupDelay:n.newGroupDelay||500},new q({key:Xt,state:{init(){return new nt(Zt.empty,Zt.empty,null,0,-1)},apply(e,t,r){return Im(t,r,e,n)}},config:n,props:{handleDOMEvents:{beforeinput(e,t){let r=t.inputType,i=r=="historyUndo"?ys:r=="historyRedo"?bs:null;return i?(t.preventDefault(),i(e.state,e.dispatch)):!1}}}})}function Mi(n,e){return(t,r)=>{let i=Xt.getState(t);if(!i||(n?i.undone:i.done).eventCount==0)return!1;if(r){let o=Bm(i,t,n);o&&r(e?o.scrollIntoView():o)}return!0}}var ys=Mi(!1,!0),bs=Mi(!0,!0),Nb=Mi(!1,!1),Db=Mi(!0,!1);var Uc=te.create({name:"history",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:n,dispatch:e})=>ys(n,e),redo:()=>({state:n,dispatch:e})=>bs(n,e)}},addProseMirrorPlugins(){return[Kc(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-\u044F":()=>this.editor.commands.undo(),"Shift-Mod-\u044F":()=>this.editor.commands.redo()}}});var Gc=J.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:n}){return["hr",V(this.options.HTMLAttributes,n)]},addCommands(){return{setHorizontalRule:()=>({chain:n,state:e})=>{let{selection:t}=e,{$from:r,$to:i}=t,o=n();return r.parentOffset===0?o.insertContentAt({from:Math.max(r.pos-1,0),to:i.pos},{type:this.name}):Tc(t)?o.insertContentAt(i.pos,{type:this.name}):o.insertContent({type:this.name}),o.command(({tr:s,dispatch:l})=>{var a;if(l){let{$to:c}=s.selection,f=c.end();if(c.nodeAfter)c.nodeAfter.isTextblock?s.setSelection(P.create(s.doc,c.pos+1)):c.nodeAfter.isBlock?s.setSelection(A.create(s.doc,c.pos)):s.setSelection(P.create(s.doc,c.pos));else{let u=(a=c.parent.type.contentMatch.defaultType)===null||a===void 0?void 0:a.create();u&&(s.insert(f,u),s.setSelection(P.create(s.doc,f+1)))}s.scrollIntoView()}return!0}).run()}}},addInputRules(){return[Ec({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}});var Fm=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,zm=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,Vm=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,$m=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,Yc=_e.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:n=>n.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:n=>n.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:n}){return["em",V(this.options.HTMLAttributes,n),0]},addCommands(){return{setItalic:()=>({commands:n})=>n.setMark(this.name),toggleItalic:()=>({commands:n})=>n.toggleMark(this.name),unsetItalic:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[Qe({find:Fm,type:this.type}),Qe({find:Vm,type:this.type})]},addPasteRules(){return[et({find:zm,type:this.type}),et({find:$m,type:this.type})]}});var Xc=J.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",V(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}});var Hm="listItem",Zc="textStyle",Qc=/^(\d+)\.\s$/,ef=J.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:n=>n.hasAttribute("start")?parseInt(n.getAttribute("start")||"",10):1},type:{default:void 0,parseHTML:n=>n.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:n}){let{start:e,...t}=n;return e===1?["ol",V(this.options.HTMLAttributes,t),0]:["ol",V(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleOrderedList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(Hm,this.editor.getAttributes(Zc)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let n=Nt({find:Qc,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(n=Nt({find:Qc,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(Zc)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[n]}});var tf=J.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:n}){return["p",V(this.options.HTMLAttributes,n),0]},addCommands(){return{setParagraph:()=>({commands:n})=>n.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}});var Wm=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,jm=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,nf=_e.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["s",V(this.options.HTMLAttributes,n),0]},addCommands(){return{setStrike:()=>({commands:n})=>n.setMark(this.name),toggleStrike:()=>({commands:n})=>n.toggleMark(this.name),unsetStrike:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[Qe({find:Wm,type:this.type})]},addPasteRules(){return[et({find:jm,type:this.type})]}});var rf=J.create({name:"text",group:"inline"});var of=te.create({name:"starterKit",addExtensions(){var n,e,t,r,i,o,s,l,a,c,f,u,d,p,h,m,g,b;let x=[];return this.options.bold!==!1&&x.push(Dc.configure((n=this.options)===null||n===void 0?void 0:n.bold)),this.options.blockquote!==!1&&x.push(Nc.configure((e=this.options)===null||e===void 0?void 0:e.blockquote)),this.options.bulletList!==!1&&x.push(Rc.configure((t=this.options)===null||t===void 0?void 0:t.bulletList)),this.options.code!==!1&&x.push(Bc.configure((r=this.options)===null||r===void 0?void 0:r.code)),this.options.codeBlock!==!1&&x.push(Lc.configure((i=this.options)===null||i===void 0?void 0:i.codeBlock)),this.options.document!==!1&&x.push(Fc.configure((o=this.options)===null||o===void 0?void 0:o.document)),this.options.dropcursor!==!1&&x.push(Vc.configure((s=this.options)===null||s===void 0?void 0:s.dropcursor)),this.options.gapcursor!==!1&&x.push(Hc.configure((l=this.options)===null||l===void 0?void 0:l.gapcursor)),this.options.hardBreak!==!1&&x.push(Wc.configure((a=this.options)===null||a===void 0?void 0:a.hardBreak)),this.options.heading!==!1&&x.push(jc.configure((c=this.options)===null||c===void 0?void 0:c.heading)),this.options.history!==!1&&x.push(Uc.configure((f=this.options)===null||f===void 0?void 0:f.history)),this.options.horizontalRule!==!1&&x.push(Gc.configure((u=this.options)===null||u===void 0?void 0:u.horizontalRule)),this.options.italic!==!1&&x.push(Yc.configure((d=this.options)===null||d===void 0?void 0:d.italic)),this.options.listItem!==!1&&x.push(Xc.configure((p=this.options)===null||p===void 0?void 0:p.listItem)),this.options.orderedList!==!1&&x.push(ef.configure((h=this.options)===null||h===void 0?void 0:h.orderedList)),this.options.paragraph!==!1&&x.push(tf.configure((m=this.options)===null||m===void 0?void 0:m.paragraph)),this.options.strike!==!1&&x.push(nf.configure((g=this.options)===null||g===void 0?void 0:g.strike)),this.options.text!==!1&&x.push(rf.configure((b=this.options)===null||b===void 0?void 0:b.text)),x}});function qm(n){var e;let{char:t,allowSpaces:r,allowToIncludeChar:i,allowedPrefixes:o,startOfLine:s,$position:l}=n,a=r&&!i,c=Ac(t),f=new RegExp(`\\s${c}$`),u=s?"^":"",d=i?"":c,p=a?new RegExp(`${u}${c}.*?(?=\\s${d}|$)`,"gm"):new RegExp(`${u}(?:^)?${c}[^\\s${d}]*`,"gm"),h=((e=l.nodeBefore)===null||e===void 0?void 0:e.isText)&&l.nodeBefore.text;if(!h)return null;let m=l.pos-h.length,g=Array.from(h.matchAll(p)).pop();if(!g||g.input===void 0||g.index===void 0)return null;let b=g.input.slice(Math.max(0,g.index-1),g.index),x=new RegExp(`^[${o?.join("")}\0]?$`).test(b);if(o!==null&&!x)return null;let w=m+g.index,y=w+g[0].length;return a&&f.test(h.slice(y-1,y+1))&&(g[0]+=" ",y+=1),w<l.pos&&y>=l.pos?{range:{from:w,to:y},query:g[0].slice(t.length),text:g[0]}:null}var Jm=new X("suggestion");function sf({pluginKey:n=Jm,editor:e,char:t="@",allowSpaces:r=!1,allowToIncludeChar:i=!1,allowedPrefixes:o=[" "],startOfLine:s=!1,decorationTag:l="span",decorationClass:a="suggestion",command:c=()=>null,items:f=()=>[],render:u=()=>({}),allow:d=()=>!0,findSuggestionMatch:p=qm}){let h,m=u?.(),g=new q({key:n,view(){return{update:async(b,x)=>{var w,y,C,k,I,B,T;let N=(w=this.key)===null||w===void 0?void 0:w.getState(x),F=(y=this.key)===null||y===void 0?void 0:y.getState(b.state),H=N.active&&F.active&&N.range.from!==F.range.from,j=!N.active&&F.active,ge=N.active&&!F.active,fe=!j&&!ge&&N.query!==F.query,K=j||H&&fe,G=fe||H,Y=ge||H&&fe;if(!K&&!G&&!Y)return;let ue=Y&&!K?N:F,Ne=b.dom.querySelector(`[data-decoration-id="${ue.decorationId}"]`);h={editor:e,range:ue.range,query:ue.query,text:ue.text,items:[],command:De=>c({editor:e,range:ue.range,props:De}),decorationNode:Ne,clientRect:Ne?()=>{var De;let{decorationId:Pe}=(De=this.key)===null||De===void 0?void 0:De.getState(e.state),We=b.dom.querySelector(`[data-decoration-id="${Pe}"]`);return We?.getBoundingClientRect()||null}:null},K&&((C=m?.onBeforeStart)===null||C===void 0||C.call(m,h)),G&&((k=m?.onBeforeUpdate)===null||k===void 0||k.call(m,h)),(G||K)&&(h.items=await f({editor:e,query:ue.query})),Y&&((I=m?.onExit)===null||I===void 0||I.call(m,h)),G&&((B=m?.onUpdate)===null||B===void 0||B.call(m,h)),K&&((T=m?.onStart)===null||T===void 0||T.call(m,h))},destroy:()=>{var b;h&&((b=m?.onExit)===null||b===void 0||b.call(m,h))}}},state:{init(){return{active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}},apply(b,x,w,y){let{isEditable:C}=e,{composing:k}=e.view,{selection:I}=b,{empty:B,from:T}=I,N={...x};if(N.composing=k,C&&(B||e.view.composing)){(T<x.range.from||T>x.range.to)&&!k&&!x.composing&&(N.active=!1);let F=p({char:t,allowSpaces:r,allowToIncludeChar:i,allowedPrefixes:o,startOfLine:s,$position:I.$from}),H=`id_${Math.floor(Math.random()*4294967295)}`;F&&d({editor:e,state:y,range:F.range,isActive:x.active})?(N.active=!0,N.decorationId=x.decorationId?x.decorationId:H,N.range=F.range,N.query=F.query,N.text=F.text):N.active=!1}else N.active=!1;return N.active||(N.decorationId=null,N.range={from:0,to:0},N.query=null,N.text=null),N}},props:{handleKeyDown(b,x){var w;let{active:y,range:C}=g.getState(b.state);return y&&((w=m?.onKeyDown)===null||w===void 0?void 0:w.call(m,{view:b,event:x,range:C}))||!1},decorations(b){let{active:x,range:w,decorationId:y}=g.getState(b);return x?se.create(b.doc,[Ae.inline(w.from,w.to,{nodeName:l,class:a,"data-decoration-id":y})]):null}}});return g}var _m=new X("mention"),lf=J.create({name:"mention",priority:101,addOptions(){return{HTMLAttributes:{},renderText({options:n,node:e}){var t;return`${n.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`},deleteTriggerWithBackspace:!1,renderHTML({options:n,node:e}){var t;return["span",V(this.HTMLAttributes,n.HTMLAttributes),`${n.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`]},suggestion:{char:"@",pluginKey:_m,command:({editor:n,range:e,props:t})=>{var r,i,o;let s=n.view.state.selection.$to.nodeAfter;((r=s?.text)===null||r===void 0?void 0:r.startsWith(" "))&&(e.to+=1),n.chain().focus().insertContentAt(e,[{type:this.name,attrs:t},{type:"text",text:" "}]).run(),(o=(i=n.view.dom.ownerDocument.defaultView)===null||i===void 0?void 0:i.getSelection())===null||o===void 0||o.collapseToEnd()},allow:({state:n,range:e})=>{let t=n.doc.resolve(e.from),r=n.schema.nodes[this.name];return!!t.parent.type.contentMatch.matchType(r)}}}},group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes(){return{id:{default:null,parseHTML:n=>n.getAttribute("data-id"),renderHTML:n=>n.id?{"data-id":n.id}:{}},label:{default:null,parseHTML:n=>n.getAttribute("data-label"),renderHTML:n=>n.label?{"data-label":n.label}:{}}}},parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:n,HTMLAttributes:e}){if(this.options.renderLabel!==void 0)return console.warn("renderLabel is deprecated use renderText and renderHTML instead"),["span",V({"data-type":this.name},this.options.HTMLAttributes,e),this.options.renderLabel({options:this.options,node:n})];let t={...this.options};t.HTMLAttributes=V({"data-type":this.name},this.options.HTMLAttributes,e);let r=this.options.renderHTML({options:t,node:n});return typeof r=="string"?["span",V({"data-type":this.name},this.options.HTMLAttributes,e),r]:r},renderText({node:n}){return this.options.renderLabel!==void 0?(console.warn("renderLabel is deprecated use renderText and renderHTML instead"),this.options.renderLabel({options:this.options,node:n})):this.options.renderText({options:this.options,node:n})},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:n,state:e})=>{let t=!1,{selection:r}=e,{empty:i,anchor:o}=r;return i?(e.doc.nodesBetween(o-1,o,(s,l)=>{if(s.type.name===this.name)return t=!0,n.insertText(this.options.deleteTriggerWithBackspace?"":this.options.suggestion.char||"",l,l+s.nodeSize),!1}),t):!1})}},addProseMirrorPlugins(){return[sf({editor:this.editor,...this.options.suggestion})]}});var af=te.create({name:"placeholder",addOptions(){return{emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something \u2026",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}},addProseMirrorPlugins(){return[new q({key:new X("placeholder"),props:{decorations:({doc:n,selection:e})=>{let t=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=e,i=[];if(!t)return null;let o=this.editor.isEmpty;return n.descendants((s,l)=>{let a=r>=l&&r<=l+s.nodeSize,c=!s.isLeaf&&fr(s);if((a||!this.options.showOnlyCurrent)&&c){let f=[this.options.emptyNodeClass];o&&f.push(this.options.emptyEditorClass);let u=Ae.node(l,l+s.nodeSize,{class:f.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:s,pos:l,hasAnchor:a}):this.options.placeholder});i.push(u)}return this.options.includeChildren}),se.create(n,i)}}})]}});var U="top",re="bottom",ne="right",Z="left",Ci="auto",Dt=[U,re,ne,Z],yt="start",Qt="end",cf="clippingParents",Oi="viewport",En="popper",ff="reference",vs=Dt.reduce(function(n,e){return n.concat([e+"-"+yt,e+"-"+Qt])},[]),Ti=[].concat(Dt,[Ci]).reduce(function(n,e){return n.concat([e,e+"-"+yt,e+"-"+Qt])},[]),Km="beforeRead",Um="read",Gm="afterRead",Ym="beforeMain",Xm="main",Zm="afterMain",Qm="beforeWrite",eg="write",tg="afterWrite",uf=[Km,Um,Gm,Ym,Xm,Zm,Qm,eg,tg];function ae(n){return n?(n.nodeName||"").toLowerCase():null}function _(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var e=n.ownerDocument;return e&&e.defaultView||window}return n}function Ve(n){var e=_(n).Element;return n instanceof e||n instanceof Element}function ie(n){var e=_(n).HTMLElement;return n instanceof e||n instanceof HTMLElement}function An(n){if(typeof ShadowRoot>"u")return!1;var e=_(n).ShadowRoot;return n instanceof e||n instanceof ShadowRoot}function ng(n){var e=n.state;Object.keys(e.elements).forEach(function(t){var r=e.styles[t]||{},i=e.attributes[t]||{},o=e.elements[t];!ie(o)||!ae(o)||(Object.assign(o.style,r),Object.keys(i).forEach(function(s){var l=i[s];l===!1?o.removeAttribute(s):o.setAttribute(s,l===!0?"":l)}))})}function rg(n){var e=n.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow),function(){Object.keys(e.elements).forEach(function(r){var i=e.elements[r],o=e.attributes[r]||{},s=Object.keys(e.styles.hasOwnProperty(r)?e.styles[r]:t[r]),l=s.reduce(function(a,c){return a[c]="",a},{});!ie(i)||!ae(i)||(Object.assign(i.style,l),Object.keys(o).forEach(function(a){i.removeAttribute(a)}))})}}var dr={name:"applyStyles",enabled:!0,phase:"write",fn:ng,effect:rg,requires:["computeStyles"]};function ce(n){return n.split("-")[0]}var Ke=Math.max,en=Math.min,bt=Math.round;function Nn(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function pr(){return!/^((?!chrome|android).)*safari/i.test(Nn())}function $e(n,e,t){e===void 0&&(e=!1),t===void 0&&(t=!1);var r=n.getBoundingClientRect(),i=1,o=1;e&&ie(n)&&(i=n.offsetWidth>0&&bt(r.width)/n.offsetWidth||1,o=n.offsetHeight>0&&bt(r.height)/n.offsetHeight||1);var s=Ve(n)?_(n):window,l=s.visualViewport,a=!pr()&&t,c=(r.left+(a&&l?l.offsetLeft:0))/i,f=(r.top+(a&&l?l.offsetTop:0))/o,u=r.width/i,d=r.height/o;return{width:u,height:d,top:f,right:c+u,bottom:f+d,left:c,x:c,y:f}}function tn(n){var e=$e(n),t=n.offsetWidth,r=n.offsetHeight;return Math.abs(e.width-t)<=1&&(t=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:n.offsetLeft,y:n.offsetTop,width:t,height:r}}function hr(n,e){var t=e.getRootNode&&e.getRootNode();if(n.contains(e))return!0;if(t&&An(t)){var r=e;do{if(r&&n.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Te(n){return _(n).getComputedStyle(n)}function xs(n){return["table","td","th"].indexOf(ae(n))>=0}function me(n){return((Ve(n)?n.ownerDocument:n.document)||window.document).documentElement}function vt(n){return ae(n)==="html"?n:n.assignedSlot||n.parentNode||(An(n)?n.host:null)||me(n)}function df(n){return!ie(n)||Te(n).position==="fixed"?null:n.offsetParent}function ig(n){var e=/firefox/i.test(Nn()),t=/Trident/i.test(Nn());if(t&&ie(n)){var r=Te(n);if(r.position==="fixed")return null}var i=vt(n);for(An(i)&&(i=i.host);ie(i)&&["html","body"].indexOf(ae(i))<0;){var o=Te(i);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||e&&o.willChange==="filter"||e&&o.filter&&o.filter!=="none")return i;i=i.parentNode}return null}function Ue(n){for(var e=_(n),t=df(n);t&&xs(t)&&Te(t).position==="static";)t=df(t);return t&&(ae(t)==="html"||ae(t)==="body"&&Te(t).position==="static")?e:t||ig(n)||e}function nn(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function rn(n,e,t){return Ke(n,en(e,t))}function pf(n,e,t){var r=rn(n,e,t);return r>t?t:r}function mr(){return{top:0,right:0,bottom:0,left:0}}function gr(n){return Object.assign({},mr(),n)}function yr(n,e){return e.reduce(function(t,r){return t[r]=n,t},{})}var og=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,gr(typeof e!="number"?e:yr(e,Dt))};function sg(n){var e,t=n.state,r=n.name,i=n.options,o=t.elements.arrow,s=t.modifiersData.popperOffsets,l=ce(t.placement),a=nn(l),c=[Z,ne].indexOf(l)>=0,f=c?"height":"width";if(!(!o||!s)){var u=og(i.padding,t),d=tn(o),p=a==="y"?U:Z,h=a==="y"?re:ne,m=t.rects.reference[f]+t.rects.reference[a]-s[a]-t.rects.popper[f],g=s[a]-t.rects.reference[a],b=Ue(o),x=b?a==="y"?b.clientHeight||0:b.clientWidth||0:0,w=m/2-g/2,y=u[p],C=x-d[f]-u[h],k=x/2-d[f]/2+w,I=rn(y,k,C),B=a;t.modifiersData[r]=(e={},e[B]=I,e.centerOffset=I-k,e)}}function lg(n){var e=n.state,t=n.options,r=t.element,i=r===void 0?"[data-popper-arrow]":r;i!=null&&(typeof i=="string"&&(i=e.elements.popper.querySelector(i),!i)||hr(e.elements.popper,i)&&(e.elements.arrow=i))}var hf={name:"arrow",enabled:!0,phase:"main",fn:sg,effect:lg,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function He(n){return n.split("-")[1]}var ag={top:"auto",right:"auto",bottom:"auto",left:"auto"};function cg(n,e){var t=n.x,r=n.y,i=e.devicePixelRatio||1;return{x:bt(t*i)/i||0,y:bt(r*i)/i||0}}function mf(n){var e,t=n.popper,r=n.popperRect,i=n.placement,o=n.variation,s=n.offsets,l=n.position,a=n.gpuAcceleration,c=n.adaptive,f=n.roundOffsets,u=n.isFixed,d=s.x,p=d===void 0?0:d,h=s.y,m=h===void 0?0:h,g=typeof f=="function"?f({x:p,y:m}):{x:p,y:m};p=g.x,m=g.y;var b=s.hasOwnProperty("x"),x=s.hasOwnProperty("y"),w=Z,y=U,C=window;if(c){var k=Ue(t),I="clientHeight",B="clientWidth";if(k===_(t)&&(k=me(t),Te(k).position!=="static"&&l==="absolute"&&(I="scrollHeight",B="scrollWidth")),k=k,i===U||(i===Z||i===ne)&&o===Qt){y=re;var T=u&&k===C&&C.visualViewport?C.visualViewport.height:k[I];m-=T-r.height,m*=a?1:-1}if(i===Z||(i===U||i===re)&&o===Qt){w=ne;var N=u&&k===C&&C.visualViewport?C.visualViewport.width:k[B];p-=N-r.width,p*=a?1:-1}}var F=Object.assign({position:l},c&&ag),H=f===!0?cg({x:p,y:m},_(t)):{x:p,y:m};if(p=H.x,m=H.y,a){var j;return Object.assign({},F,(j={},j[y]=x?"0":"",j[w]=b?"0":"",j.transform=(C.devicePixelRatio||1)<=1?"translate("+p+"px, "+m+"px)":"translate3d("+p+"px, "+m+"px, 0)",j))}return Object.assign({},F,(e={},e[y]=x?m+"px":"",e[w]=b?p+"px":"",e.transform="",e))}function fg(n){var e=n.state,t=n.options,r=t.gpuAcceleration,i=r===void 0?!0:r,o=t.adaptive,s=o===void 0?!0:o,l=t.roundOffsets,a=l===void 0?!0:l,c={placement:ce(e.placement),variation:He(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,mf(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:s,roundOffsets:a})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,mf(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var gf={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:fg,data:{}};var Ei={passive:!0};function ug(n){var e=n.state,t=n.instance,r=n.options,i=r.scroll,o=i===void 0?!0:i,s=r.resize,l=s===void 0?!0:s,a=_(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach(function(f){f.addEventListener("scroll",t.update,Ei)}),l&&a.addEventListener("resize",t.update,Ei),function(){o&&c.forEach(function(f){f.removeEventListener("scroll",t.update,Ei)}),l&&a.removeEventListener("resize",t.update,Ei)}}var yf={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ug,data:{}};var dg={left:"right",right:"left",bottom:"top",top:"bottom"};function Dn(n){return n.replace(/left|right|bottom|top/g,function(e){return dg[e]})}var pg={start:"end",end:"start"};function Ai(n){return n.replace(/start|end/g,function(e){return pg[e]})}function on(n){var e=_(n),t=e.pageXOffset,r=e.pageYOffset;return{scrollLeft:t,scrollTop:r}}function sn(n){return $e(me(n)).left+on(n).scrollLeft}function ks(n,e){var t=_(n),r=me(n),i=t.visualViewport,o=r.clientWidth,s=r.clientHeight,l=0,a=0;if(i){o=i.width,s=i.height;var c=pr();(c||!c&&e==="fixed")&&(l=i.offsetLeft,a=i.offsetTop)}return{width:o,height:s,x:l+sn(n),y:a}}function Ss(n){var e,t=me(n),r=on(n),i=(e=n.ownerDocument)==null?void 0:e.body,o=Ke(t.scrollWidth,t.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=Ke(t.scrollHeight,t.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),l=-r.scrollLeft+sn(n),a=-r.scrollTop;return Te(i||t).direction==="rtl"&&(l+=Ke(t.clientWidth,i?i.clientWidth:0)-o),{width:o,height:s,x:l,y:a}}function ln(n){var e=Te(n),t=e.overflow,r=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+i+r)}function Ni(n){return["html","body","#document"].indexOf(ae(n))>=0?n.ownerDocument.body:ie(n)&&ln(n)?n:Ni(vt(n))}function Pt(n,e){var t;e===void 0&&(e=[]);var r=Ni(n),i=r===((t=n.ownerDocument)==null?void 0:t.body),o=_(r),s=i?[o].concat(o.visualViewport||[],ln(r)?r:[]):r,l=e.concat(s);return i?l:l.concat(Pt(vt(s)))}function Pn(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function hg(n,e){var t=$e(n,!1,e==="fixed");return t.top=t.top+n.clientTop,t.left=t.left+n.clientLeft,t.bottom=t.top+n.clientHeight,t.right=t.left+n.clientWidth,t.width=n.clientWidth,t.height=n.clientHeight,t.x=t.left,t.y=t.top,t}function bf(n,e,t){return e===Oi?Pn(ks(n,t)):Ve(e)?hg(e,t):Pn(Ss(me(n)))}function mg(n){var e=Pt(vt(n)),t=["absolute","fixed"].indexOf(Te(n).position)>=0,r=t&&ie(n)?Ue(n):n;return Ve(r)?e.filter(function(i){return Ve(i)&&hr(i,r)&&ae(i)!=="body"}):[]}function ws(n,e,t,r){var i=e==="clippingParents"?mg(n):[].concat(e),o=[].concat(i,[t]),s=o[0],l=o.reduce(function(a,c){var f=bf(n,c,r);return a.top=Ke(f.top,a.top),a.right=en(f.right,a.right),a.bottom=en(f.bottom,a.bottom),a.left=Ke(f.left,a.left),a},bf(n,s,r));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}function br(n){var e=n.reference,t=n.element,r=n.placement,i=r?ce(r):null,o=r?He(r):null,s=e.x+e.width/2-t.width/2,l=e.y+e.height/2-t.height/2,a;switch(i){case U:a={x:s,y:e.y-t.height};break;case re:a={x:s,y:e.y+e.height};break;case ne:a={x:e.x+e.width,y:l};break;case Z:a={x:e.x-t.width,y:l};break;default:a={x:e.x,y:e.y}}var c=i?nn(i):null;if(c!=null){var f=c==="y"?"height":"width";switch(o){case yt:a[c]=a[c]-(e[f]/2-t[f]/2);break;case Qt:a[c]=a[c]+(e[f]/2-t[f]/2);break;default:}}return a}function Ge(n,e){e===void 0&&(e={});var t=e,r=t.placement,i=r===void 0?n.placement:r,o=t.strategy,s=o===void 0?n.strategy:o,l=t.boundary,a=l===void 0?cf:l,c=t.rootBoundary,f=c===void 0?Oi:c,u=t.elementContext,d=u===void 0?En:u,p=t.altBoundary,h=p===void 0?!1:p,m=t.padding,g=m===void 0?0:m,b=gr(typeof g!="number"?g:yr(g,Dt)),x=d===En?ff:En,w=n.rects.popper,y=n.elements[h?x:d],C=ws(Ve(y)?y:y.contextElement||me(n.elements.popper),a,f,s),k=$e(n.elements.reference),I=br({reference:k,element:w,strategy:"absolute",placement:i}),B=Pn(Object.assign({},w,I)),T=d===En?B:k,N={top:C.top-T.top+b.top,bottom:T.bottom-C.bottom+b.bottom,left:C.left-T.left+b.left,right:T.right-C.right+b.right},F=n.modifiersData.offset;if(d===En&&F){var H=F[i];Object.keys(N).forEach(function(j){var ge=[ne,re].indexOf(j)>=0?1:-1,fe=[U,re].indexOf(j)>=0?"y":"x";N[j]+=H[fe]*ge})}return N}function Ms(n,e){e===void 0&&(e={});var t=e,r=t.placement,i=t.boundary,o=t.rootBoundary,s=t.padding,l=t.flipVariations,a=t.allowedAutoPlacements,c=a===void 0?Ti:a,f=He(r),u=f?l?vs:vs.filter(function(h){return He(h)===f}):Dt,d=u.filter(function(h){return c.indexOf(h)>=0});d.length===0&&(d=u);var p=d.reduce(function(h,m){return h[m]=Ge(n,{placement:m,boundary:i,rootBoundary:o,padding:s})[ce(m)],h},{});return Object.keys(p).sort(function(h,m){return p[h]-p[m]})}function gg(n){if(ce(n)===Ci)return[];var e=Dn(n);return[Ai(n),e,Ai(e)]}function yg(n){var e=n.state,t=n.options,r=n.name;if(!e.modifiersData[r]._skip){for(var i=t.mainAxis,o=i===void 0?!0:i,s=t.altAxis,l=s===void 0?!0:s,a=t.fallbackPlacements,c=t.padding,f=t.boundary,u=t.rootBoundary,d=t.altBoundary,p=t.flipVariations,h=p===void 0?!0:p,m=t.allowedAutoPlacements,g=e.options.placement,b=ce(g),x=b===g,w=a||(x||!h?[Dn(g)]:gg(g)),y=[g].concat(w).reduce(function(it,je){return it.concat(ce(je)===Ci?Ms(e,{placement:je,boundary:f,rootBoundary:u,padding:c,flipVariations:h,allowedAutoPlacements:m}):je)},[]),C=e.rects.reference,k=e.rects.popper,I=new Map,B=!0,T=y[0],N=0;N<y.length;N++){var F=y[N],H=ce(F),j=He(F)===yt,ge=[U,re].indexOf(H)>=0,fe=ge?"width":"height",K=Ge(e,{placement:F,boundary:f,rootBoundary:u,altBoundary:d,padding:c}),G=ge?j?ne:Z:j?re:U;C[fe]>k[fe]&&(G=Dn(G));var Y=Dn(G),ue=[];if(o&&ue.push(K[H]<=0),l&&ue.push(K[G]<=0,K[Y]<=0),ue.every(function(it){return it})){T=F,B=!1;break}I.set(F,ue)}if(B)for(var Ne=h?3:1,De=function(je){var ot=y.find(function(cn){var st=I.get(cn);if(st)return st.slice(0,je).every(function(fn){return fn})});if(ot)return T=ot,"break"},Pe=Ne;Pe>0;Pe--){var We=De(Pe);if(We==="break")break}e.placement!==T&&(e.modifiersData[r]._skip=!0,e.placement=T,e.reset=!0)}}var vf={name:"flip",enabled:!0,phase:"main",fn:yg,requiresIfExists:["offset"],data:{_skip:!1}};function xf(n,e,t){return t===void 0&&(t={x:0,y:0}),{top:n.top-e.height-t.y,right:n.right-e.width+t.x,bottom:n.bottom-e.height+t.y,left:n.left-e.width-t.x}}function kf(n){return[U,ne,re,Z].some(function(e){return n[e]>=0})}function bg(n){var e=n.state,t=n.name,r=e.rects.reference,i=e.rects.popper,o=e.modifiersData.preventOverflow,s=Ge(e,{elementContext:"reference"}),l=Ge(e,{altBoundary:!0}),a=xf(s,r),c=xf(l,i,o),f=kf(a),u=kf(c);e.modifiersData[t]={referenceClippingOffsets:a,popperEscapeOffsets:c,isReferenceHidden:f,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":u})}var Sf={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:bg};function vg(n,e,t){var r=ce(n),i=[Z,U].indexOf(r)>=0?-1:1,o=typeof t=="function"?t(Object.assign({},e,{placement:n})):t,s=o[0],l=o[1];return s=s||0,l=(l||0)*i,[Z,ne].indexOf(r)>=0?{x:l,y:s}:{x:s,y:l}}function xg(n){var e=n.state,t=n.options,r=n.name,i=t.offset,o=i===void 0?[0,0]:i,s=Ti.reduce(function(f,u){return f[u]=vg(u,e.rects,o),f},{}),l=s[e.placement],a=l.x,c=l.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=a,e.modifiersData.popperOffsets.y+=c),e.modifiersData[r]=s}var wf={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:xg};function kg(n){var e=n.state,t=n.name;e.modifiersData[t]=br({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}var Mf={name:"popperOffsets",enabled:!0,phase:"read",fn:kg,data:{}};function Cs(n){return n==="x"?"y":"x"}function Sg(n){var e=n.state,t=n.options,r=n.name,i=t.mainAxis,o=i===void 0?!0:i,s=t.altAxis,l=s===void 0?!1:s,a=t.boundary,c=t.rootBoundary,f=t.altBoundary,u=t.padding,d=t.tether,p=d===void 0?!0:d,h=t.tetherOffset,m=h===void 0?0:h,g=Ge(e,{boundary:a,rootBoundary:c,padding:u,altBoundary:f}),b=ce(e.placement),x=He(e.placement),w=!x,y=nn(b),C=Cs(y),k=e.modifiersData.popperOffsets,I=e.rects.reference,B=e.rects.popper,T=typeof m=="function"?m(Object.assign({},e.rects,{placement:e.placement})):m,N=typeof T=="number"?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),F=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,H={x:0,y:0};if(k){if(o){var j,ge=y==="y"?U:Z,fe=y==="y"?re:ne,K=y==="y"?"height":"width",G=k[y],Y=G+g[ge],ue=G-g[fe],Ne=p?-B[K]/2:0,De=x===yt?I[K]:B[K],Pe=x===yt?-B[K]:-I[K],We=e.elements.arrow,it=p&&We?tn(We):{width:0,height:0},je=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:mr(),ot=je[ge],cn=je[fe],st=rn(0,I[K],it[K]),fn=w?I[K]/2-Ne-st-ot-N.mainAxis:De-st-ot-N.mainAxis,xt=w?-I[K]/2+Ne+st+cn+N.mainAxis:Pe+st+cn+N.mainAxis,un=e.elements.arrow&&Ue(e.elements.arrow),kr=un?y==="y"?un.clientTop||0:un.clientLeft||0:0,Rn=(j=F?.[y])!=null?j:0,Sr=G+fn-Rn-kr,wr=G+xt-Rn,Bn=rn(p?en(Y,Sr):Y,G,p?Ke(ue,wr):ue);k[y]=Bn,H[y]=Bn-G}if(l){var Ln,Mr=y==="x"?U:Z,Cr=y==="x"?re:ne,lt=k[C],kt=C==="y"?"height":"width",Fn=lt+g[Mr],It=lt-g[Cr],zn=[U,Z].indexOf(b)!==-1,Or=(Ln=F?.[C])!=null?Ln:0,Tr=zn?Fn:lt-I[kt]-B[kt]-Or+N.altAxis,Er=zn?lt+I[kt]+B[kt]-Or-N.altAxis:It,Ar=p&&zn?pf(Tr,lt,Er):rn(p?Tr:Fn,lt,p?Er:It);k[C]=Ar,H[C]=Ar-lt}e.modifiersData[r]=H}}var Cf={name:"preventOverflow",enabled:!0,phase:"main",fn:Sg,requiresIfExists:["offset"]};function Os(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function Ts(n){return n===_(n)||!ie(n)?on(n):Os(n)}function wg(n){var e=n.getBoundingClientRect(),t=bt(e.width)/n.offsetWidth||1,r=bt(e.height)/n.offsetHeight||1;return t!==1||r!==1}function Es(n,e,t){t===void 0&&(t=!1);var r=ie(e),i=ie(e)&&wg(e),o=me(e),s=$e(n,i,t),l={scrollLeft:0,scrollTop:0},a={x:0,y:0};return(r||!r&&!t)&&((ae(e)!=="body"||ln(o))&&(l=Ts(e)),ie(e)?(a=$e(e,!0),a.x+=e.clientLeft,a.y+=e.clientTop):o&&(a.x=sn(o))),{x:s.left+l.scrollLeft-a.x,y:s.top+l.scrollTop-a.y,width:s.width,height:s.height}}function Mg(n){var e=new Map,t=new Set,r=[];n.forEach(function(o){e.set(o.name,o)});function i(o){t.add(o.name);var s=[].concat(o.requires||[],o.requiresIfExists||[]);s.forEach(function(l){if(!t.has(l)){var a=e.get(l);a&&i(a)}}),r.push(o)}return n.forEach(function(o){t.has(o.name)||i(o)}),r}function As(n){var e=Mg(n);return uf.reduce(function(t,r){return t.concat(e.filter(function(i){return i.phase===r}))},[])}function Ns(n){var e;return function(){return e||(e=new Promise(function(t){Promise.resolve().then(function(){e=void 0,t(n())})})),e}}function Ds(n){var e=n.reduce(function(t,r){var i=t[r.name];return t[r.name]=i?Object.assign({},i,r,{options:Object.assign({},i.options,r.options),data:Object.assign({},i.data,r.data)}):r,t},{});return Object.keys(e).map(function(t){return e[t]})}var Of={placement:"bottom",modifiers:[],strategy:"absolute"};function Tf(){for(var n=arguments.length,e=new Array(n),t=0;t<n;t++)e[t]=arguments[t];return!e.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function Ef(n){n===void 0&&(n={});var e=n,t=e.defaultModifiers,r=t===void 0?[]:t,i=e.defaultOptions,o=i===void 0?Of:i;return function(l,a,c){c===void 0&&(c=o);var f={placement:"bottom",orderedModifiers:[],options:Object.assign({},Of,o),modifiersData:{},elements:{reference:l,popper:a},attributes:{},styles:{}},u=[],d=!1,p={state:f,setOptions:function(b){var x=typeof b=="function"?b(f.options):b;m(),f.options=Object.assign({},o,f.options,x),f.scrollParents={reference:Ve(l)?Pt(l):l.contextElement?Pt(l.contextElement):[],popper:Pt(a)};var w=As(Ds([].concat(r,f.options.modifiers)));return f.orderedModifiers=w.filter(function(y){return y.enabled}),h(),p.update()},forceUpdate:function(){if(!d){var b=f.elements,x=b.reference,w=b.popper;if(Tf(x,w)){f.rects={reference:Es(x,Ue(w),f.options.strategy==="fixed"),popper:tn(w)},f.reset=!1,f.placement=f.options.placement,f.orderedModifiers.forEach(function(N){return f.modifiersData[N.name]=Object.assign({},N.data)});for(var y=0;y<f.orderedModifiers.length;y++){if(f.reset===!0){f.reset=!1,y=-1;continue}var C=f.orderedModifiers[y],k=C.fn,I=C.options,B=I===void 0?{}:I,T=C.name;typeof k=="function"&&(f=k({state:f,options:B,name:T,instance:p})||f)}}}},update:Ns(function(){return new Promise(function(g){p.forceUpdate(),g(f)})}),destroy:function(){m(),d=!0}};if(!Tf(l,a))return p;p.setOptions(c).then(function(g){!d&&c.onFirstUpdate&&c.onFirstUpdate(g)});function h(){f.orderedModifiers.forEach(function(g){var b=g.name,x=g.options,w=x===void 0?{}:x,y=g.effect;if(typeof y=="function"){var C=y({state:f,name:b,instance:p,options:w}),k=function(){};u.push(C||k)}})}function m(){u.forEach(function(g){return g()}),u=[]}return p}}var Cg=[yf,Mf,gf,dr,wf,vf,Cf,hf,Sf],Ps=Ef({defaultModifiers:Cg});var Og="tippy-box",zf="tippy-content",Tg="tippy-backdrop",Vf="tippy-arrow",$f="tippy-svg-arrow",an={passive:!0,capture:!0},Hf=function(){return document.body};function Is(n,e,t){if(Array.isArray(n)){var r=n[e];return r??(Array.isArray(t)?t[e]:t)}return n}function Vs(n,e){var t={}.toString.call(n);return t.indexOf("[object")===0&&t.indexOf(e+"]")>-1}function Wf(n,e){return typeof n=="function"?n.apply(void 0,e):n}function Af(n,e){if(e===0)return n;var t;return function(r){clearTimeout(t),t=setTimeout(function(){n(r)},e)}}function Eg(n){return n.split(/\s+/).filter(Boolean)}function In(n){return[].concat(n)}function Nf(n,e){n.indexOf(e)===-1&&n.push(e)}function Ag(n){return n.filter(function(e,t){return n.indexOf(e)===t})}function Ng(n){return n.split("-")[0]}function Pi(n){return[].slice.call(n)}function Df(n){return Object.keys(n).reduce(function(e,t){return n[t]!==void 0&&(e[t]=n[t]),e},{})}function vr(){return document.createElement("div")}function Ii(n){return["Element","Fragment"].some(function(e){return Vs(n,e)})}function Dg(n){return Vs(n,"NodeList")}function Pg(n){return Vs(n,"MouseEvent")}function Ig(n){return!!(n&&n._tippy&&n._tippy.reference===n)}function Rg(n){return Ii(n)?[n]:Dg(n)?Pi(n):Array.isArray(n)?n:Pi(document.querySelectorAll(n))}function Rs(n,e){n.forEach(function(t){t&&(t.style.transitionDuration=e+"ms")})}function Pf(n,e){n.forEach(function(t){t&&t.setAttribute("data-state",e)})}function Bg(n){var e,t=In(n),r=t[0];return r!=null&&(e=r.ownerDocument)!=null&&e.body?r.ownerDocument:document}function Lg(n,e){var t=e.clientX,r=e.clientY;return n.every(function(i){var o=i.popperRect,s=i.popperState,l=i.props,a=l.interactiveBorder,c=Ng(s.placement),f=s.modifiersData.offset;if(!f)return!0;var u=c==="bottom"?f.top.y:0,d=c==="top"?f.bottom.y:0,p=c==="right"?f.left.x:0,h=c==="left"?f.right.x:0,m=o.top-r+u>a,g=r-o.bottom-d>a,b=o.left-t+p>a,x=t-o.right-h>a;return m||g||b||x})}function Bs(n,e,t){var r=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(i){n[r](i,t)})}function If(n,e){for(var t=e;t;){var r;if(n.contains(t))return!0;t=t.getRootNode==null||(r=t.getRootNode())==null?void 0:r.host}return!1}var rt={isTouch:!1},Rf=0;function Fg(){rt.isTouch||(rt.isTouch=!0,window.performance&&document.addEventListener("mousemove",jf))}function jf(){var n=performance.now();n-Rf<20&&(rt.isTouch=!1,document.removeEventListener("mousemove",jf)),Rf=n}function zg(){var n=document.activeElement;if(Ig(n)){var e=n._tippy;n.blur&&!e.state.isVisible&&n.blur()}}function Vg(){document.addEventListener("touchstart",Fg,an),window.addEventListener("blur",zg)}var $g=typeof window<"u"&&typeof document<"u",Hg=$g?!!window.msCrypto:!1;var Wg={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},jg={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Ye=Object.assign({appendTo:Hf,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Wg,jg),qg=Object.keys(Ye),Jg=function(e){var t=Object.keys(e);t.forEach(function(r){Ye[r]=e[r]})};function qf(n){var e=n.plugins||[],t=e.reduce(function(r,i){var o=i.name,s=i.defaultValue;if(o){var l;r[o]=n[o]!==void 0?n[o]:(l=Ye[o])!=null?l:s}return r},{});return Object.assign({},n,t)}function _g(n,e){var t=e?Object.keys(qf(Object.assign({},Ye,{plugins:e}))):qg,r=t.reduce(function(i,o){var s=(n.getAttribute("data-tippy-"+o)||"").trim();if(!s)return i;if(o==="content")i[o]=s;else try{i[o]=JSON.parse(s)}catch{i[o]=s}return i},{});return r}function Bf(n,e){var t=Object.assign({},e,{content:Wf(e.content,[n])},e.ignoreAttributes?{}:_g(n,e.plugins));return t.aria=Object.assign({},Ye.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?e.interactive:t.aria.expanded,content:t.aria.content==="auto"?e.interactive?null:"describedby":t.aria.content},t}var Kg=function(){return"innerHTML"};function Fs(n,e){n[Kg()]=e}function Lf(n){var e=vr();return n===!0?e.className=Vf:(e.className=$f,Ii(n)?e.appendChild(n):Fs(e,n)),e}function Ff(n,e){Ii(e.content)?(Fs(n,""),n.appendChild(e.content)):typeof e.content!="function"&&(e.allowHTML?Fs(n,e.content):n.textContent=e.content)}function zs(n){var e=n.firstElementChild,t=Pi(e.children);return{box:e,content:t.find(function(r){return r.classList.contains(zf)}),arrow:t.find(function(r){return r.classList.contains(Vf)||r.classList.contains($f)}),backdrop:t.find(function(r){return r.classList.contains(Tg)})}}function Jf(n){var e=vr(),t=vr();t.className=Og,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var r=vr();r.className=zf,r.setAttribute("data-state","hidden"),Ff(r,n.props),e.appendChild(t),t.appendChild(r),i(n.props,n.props);function i(o,s){var l=zs(e),a=l.box,c=l.content,f=l.arrow;s.theme?a.setAttribute("data-theme",s.theme):a.removeAttribute("data-theme"),typeof s.animation=="string"?a.setAttribute("data-animation",s.animation):a.removeAttribute("data-animation"),s.inertia?a.setAttribute("data-inertia",""):a.removeAttribute("data-inertia"),a.style.maxWidth=typeof s.maxWidth=="number"?s.maxWidth+"px":s.maxWidth,s.role?a.setAttribute("role",s.role):a.removeAttribute("role"),(o.content!==s.content||o.allowHTML!==s.allowHTML)&&Ff(c,n.props),s.arrow?f?o.arrow!==s.arrow&&(a.removeChild(f),a.appendChild(Lf(s.arrow))):a.appendChild(Lf(s.arrow)):f&&a.removeChild(f)}return{popper:e,onUpdate:i}}Jf.$$tippy=!0;var Ug=1,Di=[],Ls=[];function Gg(n,e){var t=Bf(n,Object.assign({},Ye,qf(Df(e)))),r,i,o,s=!1,l=!1,a=!1,c=!1,f,u,d,p=[],h=Af(Sr,t.interactiveDebounce),m,g=Ug++,b=null,x=Ag(t.plugins),w={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},y={id:g,reference:n,popper:vr(),popperInstance:b,props:t,state:w,plugins:x,clearDelayTimeouts:Tr,setProps:Er,setContent:Ar,show:Uf,hide:Gf,hideWithInteractivity:Yf,enable:zn,disable:Or,unmount:Xf,destroy:Zf};if(!t.render)return y;var C=t.render(y),k=C.popper,I=C.onUpdate;k.setAttribute("data-tippy-root",""),k.id="tippy-"+y.id,y.popper=k,n._tippy=y,k._tippy=y;var B=x.map(function(v){return v.fn(y)}),T=n.hasAttribute("aria-expanded");return un(),Ne(),G(),Y("onCreate",[y]),t.showOnCreate&&Fn(),k.addEventListener("mouseenter",function(){y.props.interactive&&y.state.isVisible&&y.clearDelayTimeouts()}),k.addEventListener("mouseleave",function(){y.props.interactive&&y.props.trigger.indexOf("mouseenter")>=0&&ge().addEventListener("mousemove",h)}),y;function N(){var v=y.props.touch;return Array.isArray(v)?v:[v,0]}function F(){return N()[0]==="hold"}function H(){var v;return!!((v=y.props.render)!=null&&v.$$tippy)}function j(){return m||n}function ge(){var v=j().parentNode;return v?Bg(v):document}function fe(){return zs(k)}function K(v){return y.state.isMounted&&!y.state.isVisible||rt.isTouch||f&&f.type==="focus"?0:Is(y.props.delay,v?0:1,Ye.delay)}function G(v){v===void 0&&(v=!1),k.style.pointerEvents=y.props.interactive&&!v?"":"none",k.style.zIndex=""+y.props.zIndex}function Y(v,E,R){if(R===void 0&&(R=!0),B.forEach(function(z){z[v]&&z[v].apply(z,E)}),R){var W;(W=y.props)[v].apply(W,E)}}function ue(){var v=y.props.aria;if(v.content){var E="aria-"+v.content,R=k.id,W=In(y.props.triggerTarget||n);W.forEach(function(z){var Se=z.getAttribute(E);if(y.state.isVisible)z.setAttribute(E,Se?Se+" "+R:R);else{var Ie=Se&&Se.replace(R,"").trim();Ie?z.setAttribute(E,Ie):z.removeAttribute(E)}})}}function Ne(){if(!(T||!y.props.aria.expanded)){var v=In(y.props.triggerTarget||n);v.forEach(function(E){y.props.interactive?E.setAttribute("aria-expanded",y.state.isVisible&&E===j()?"true":"false"):E.removeAttribute("aria-expanded")})}}function De(){ge().removeEventListener("mousemove",h),Di=Di.filter(function(v){return v!==h})}function Pe(v){if(!(rt.isTouch&&(a||v.type==="mousedown"))){var E=v.composedPath&&v.composedPath()[0]||v.target;if(!(y.props.interactive&&If(k,E))){if(In(y.props.triggerTarget||n).some(function(R){return If(R,E)})){if(rt.isTouch||y.state.isVisible&&y.props.trigger.indexOf("click")>=0)return}else Y("onClickOutside",[y,v]);y.props.hideOnClick===!0&&(y.clearDelayTimeouts(),y.hide(),l=!0,setTimeout(function(){l=!1}),y.state.isMounted||ot())}}}function We(){a=!0}function it(){a=!1}function je(){var v=ge();v.addEventListener("mousedown",Pe,!0),v.addEventListener("touchend",Pe,an),v.addEventListener("touchstart",it,an),v.addEventListener("touchmove",We,an)}function ot(){var v=ge();v.removeEventListener("mousedown",Pe,!0),v.removeEventListener("touchend",Pe,an),v.removeEventListener("touchstart",it,an),v.removeEventListener("touchmove",We,an)}function cn(v,E){fn(v,function(){!y.state.isVisible&&k.parentNode&&k.parentNode.contains(k)&&E()})}function st(v,E){fn(v,E)}function fn(v,E){var R=fe().box;function W(z){z.target===R&&(Bs(R,"remove",W),E())}if(v===0)return E();Bs(R,"remove",u),Bs(R,"add",W),u=W}function xt(v,E,R){R===void 0&&(R=!1);var W=In(y.props.triggerTarget||n);W.forEach(function(z){z.addEventListener(v,E,R),p.push({node:z,eventType:v,handler:E,options:R})})}function un(){F()&&(xt("touchstart",Rn,{passive:!0}),xt("touchend",wr,{passive:!0})),Eg(y.props.trigger).forEach(function(v){if(v!=="manual")switch(xt(v,Rn),v){case"mouseenter":xt("mouseleave",wr);break;case"focus":xt(Hg?"focusout":"blur",Bn);break;case"focusin":xt("focusout",Bn);break}})}function kr(){p.forEach(function(v){var E=v.node,R=v.eventType,W=v.handler,z=v.options;E.removeEventListener(R,W,z)}),p=[]}function Rn(v){var E,R=!1;if(!(!y.state.isEnabled||Ln(v)||l)){var W=((E=f)==null?void 0:E.type)==="focus";f=v,m=v.currentTarget,Ne(),!y.state.isVisible&&Pg(v)&&Di.forEach(function(z){return z(v)}),v.type==="click"&&(y.props.trigger.indexOf("mouseenter")<0||s)&&y.props.hideOnClick!==!1&&y.state.isVisible?R=!0:Fn(v),v.type==="click"&&(s=!R),R&&!W&&It(v)}}function Sr(v){var E=v.target,R=j().contains(E)||k.contains(E);if(!(v.type==="mousemove"&&R)){var W=kt().concat(k).map(function(z){var Se,Ie=z._tippy,dn=(Se=Ie.popperInstance)==null?void 0:Se.state;return dn?{popperRect:z.getBoundingClientRect(),popperState:dn,props:t}:null}).filter(Boolean);Lg(W,v)&&(De(),It(v))}}function wr(v){var E=Ln(v)||y.props.trigger.indexOf("click")>=0&&s;if(!E){if(y.props.interactive){y.hideWithInteractivity(v);return}It(v)}}function Bn(v){y.props.trigger.indexOf("focusin")<0&&v.target!==j()||y.props.interactive&&v.relatedTarget&&k.contains(v.relatedTarget)||It(v)}function Ln(v){return rt.isTouch?F()!==v.type.indexOf("touch")>=0:!1}function Mr(){Cr();var v=y.props,E=v.popperOptions,R=v.placement,W=v.offset,z=v.getReferenceClientRect,Se=v.moveTransition,Ie=H()?zs(k).arrow:null,dn=z?{getBoundingClientRect:z,contextElement:z.contextElement||j()}:n,$s={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(Nr){var pn=Nr.state;if(H()){var Qf=fe(),Bi=Qf.box;["placement","reference-hidden","escaped"].forEach(function(Dr){Dr==="placement"?Bi.setAttribute("data-placement",pn.placement):pn.attributes.popper["data-popper-"+Dr]?Bi.setAttribute("data-"+Dr,""):Bi.removeAttribute("data-"+Dr)}),pn.attributes.popper={}}}},Rt=[{name:"offset",options:{offset:W}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!Se}},$s];H()&&Ie&&Rt.push({name:"arrow",options:{element:Ie,padding:3}}),Rt.push.apply(Rt,E?.modifiers||[]),y.popperInstance=Ps(dn,k,Object.assign({},E,{placement:R,onFirstUpdate:d,modifiers:Rt}))}function Cr(){y.popperInstance&&(y.popperInstance.destroy(),y.popperInstance=null)}function lt(){var v=y.props.appendTo,E,R=j();y.props.interactive&&v===Hf||v==="parent"?E=R.parentNode:E=Wf(v,[R]),E.contains(k)||E.appendChild(k),y.state.isMounted=!0,Mr()}function kt(){return Pi(k.querySelectorAll("[data-tippy-root]"))}function Fn(v){y.clearDelayTimeouts(),v&&Y("onTrigger",[y,v]),je();var E=K(!0),R=N(),W=R[0],z=R[1];rt.isTouch&&W==="hold"&&z&&(E=z),E?r=setTimeout(function(){y.show()},E):y.show()}function It(v){if(y.clearDelayTimeouts(),Y("onUntrigger",[y,v]),!y.state.isVisible){ot();return}if(!(y.props.trigger.indexOf("mouseenter")>=0&&y.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(v.type)>=0&&s)){var E=K(!1);E?i=setTimeout(function(){y.state.isVisible&&y.hide()},E):o=requestAnimationFrame(function(){y.hide()})}}function zn(){y.state.isEnabled=!0}function Or(){y.hide(),y.state.isEnabled=!1}function Tr(){clearTimeout(r),clearTimeout(i),cancelAnimationFrame(o)}function Er(v){if(!y.state.isDestroyed){Y("onBeforeUpdate",[y,v]),kr();var E=y.props,R=Bf(n,Object.assign({},E,Df(v),{ignoreAttributes:!0}));y.props=R,un(),E.interactiveDebounce!==R.interactiveDebounce&&(De(),h=Af(Sr,R.interactiveDebounce)),E.triggerTarget&&!R.triggerTarget?In(E.triggerTarget).forEach(function(W){W.removeAttribute("aria-expanded")}):R.triggerTarget&&n.removeAttribute("aria-expanded"),Ne(),G(),I&&I(E,R),y.popperInstance&&(Mr(),kt().forEach(function(W){requestAnimationFrame(W._tippy.popperInstance.forceUpdate)})),Y("onAfterUpdate",[y,v])}}function Ar(v){y.setProps({content:v})}function Uf(){var v=y.state.isVisible,E=y.state.isDestroyed,R=!y.state.isEnabled,W=rt.isTouch&&!y.props.touch,z=Is(y.props.duration,0,Ye.duration);if(!(v||E||R||W)&&!j().hasAttribute("disabled")&&(Y("onShow",[y],!1),y.props.onShow(y)!==!1)){if(y.state.isVisible=!0,H()&&(k.style.visibility="visible"),G(),je(),y.state.isMounted||(k.style.transition="none"),H()){var Se=fe(),Ie=Se.box,dn=Se.content;Rs([Ie,dn],0)}d=function(){var Rt;if(!(!y.state.isVisible||c)){if(c=!0,k.offsetHeight,k.style.transition=y.props.moveTransition,H()&&y.props.animation){var Ri=fe(),Nr=Ri.box,pn=Ri.content;Rs([Nr,pn],z),Pf([Nr,pn],"visible")}ue(),Ne(),Nf(Ls,y),(Rt=y.popperInstance)==null||Rt.forceUpdate(),Y("onMount",[y]),y.props.animation&&H()&&st(z,function(){y.state.isShown=!0,Y("onShown",[y])})}},lt()}}function Gf(){var v=!y.state.isVisible,E=y.state.isDestroyed,R=!y.state.isEnabled,W=Is(y.props.duration,1,Ye.duration);if(!(v||E||R)&&(Y("onHide",[y],!1),y.props.onHide(y)!==!1)){if(y.state.isVisible=!1,y.state.isShown=!1,c=!1,s=!1,H()&&(k.style.visibility="hidden"),De(),ot(),G(!0),H()){var z=fe(),Se=z.box,Ie=z.content;y.props.animation&&(Rs([Se,Ie],W),Pf([Se,Ie],"hidden"))}ue(),Ne(),y.props.animation?H()&&cn(W,y.unmount):y.unmount()}}function Yf(v){ge().addEventListener("mousemove",h),Nf(Di,h),h(v)}function Xf(){y.state.isVisible&&y.hide(),y.state.isMounted&&(Cr(),kt().forEach(function(v){v._tippy.unmount()}),k.parentNode&&k.parentNode.removeChild(k),Ls=Ls.filter(function(v){return v!==y}),y.state.isMounted=!1,Y("onHidden",[y]))}function Zf(){y.state.isDestroyed||(y.clearDelayTimeouts(),y.unmount(),kr(),delete n._tippy,y.state.isDestroyed=!0,Y("onDestroy",[y]))}}function xr(n,e){e===void 0&&(e={});var t=Ye.plugins.concat(e.plugins||[]);Vg();var r=Object.assign({},e,{plugins:t}),i=Rg(n);if(0)var o,s;var l=i.reduce(function(a,c){var f=c&&Gg(c,r);return f&&a.push(f),a},[]);return Ii(n)?l[0]:l}xr.defaultProps=Ye;xr.setDefaultProps=Jg;xr.currentInput=rt;var TS=Object.assign({},dr,{effect:function(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow)}});xr.setDefaultProps({render:Jf});var _f=xr;var Yg=(n,e,t)=>{n.chain().focus().deleteRange(e).insertContentAt(e,[{type:"mention",attrs:t},{type:"text",text:" "}]).run(),n.view.dom.ownerDocument.defaultView?.getSelection()?.collapseToEnd()},Xg=n=>{let e=[];return Alpine.store("filamentCommentsMentionsFiltered",{items:[],selectedIndex:0}),{items:({query:t})=>(e=n.filter(r=>r.name.toLowerCase().startsWith(t.toLowerCase())).slice(0,5),console.log("filteredItems",n,e,t),Alpine.store("filamentCommentsMentionsFiltered").items=e,Alpine.store("filamentCommentsMentionsFiltered").selectedIndex=0,e),command:({editor:t,range:r,props:i})=>{let s=t.view.state.selection.$to.nodeAfter?.text?.startsWith(" ");t.view.state.mention$.text.length>1&&(r.to=r.from+(t.view.state.mention$.text.length-1)),s&&(r.to+=1);let l=3,a=!1;for(;l>0&&!a;)try{Yg(t,r,i),a=!0}catch{l--,r.to-=1}},render:()=>{let t,r,i;return{onStart:o=>{i=o.command,t=_f("body",{getReferenceClientRect:o.clientRect,content:(()=>{r=Alpine.data("filamentCommentsMentions",()=>({add(l){o.command({id:l.id,label:l.name})}}));let s=document.createElement("div");return s.setAttribute("x-data","filamentCommentsMentions"),s.innerHTML=`
                                <template x-for='(item, index) in $store.filamentCommentsMentionsFiltered.items' :key='item.id'>
                                    <div
                                        class="mention-item"
                                        x-text="item.name"
                                        @click="add(item)"
                                        :class="{ 'comm:bg-gray-100': $store.filamentCommentsMentionsFiltered.selectedIndex === index }"
                                    ></div>
                                </template>
                            `,s})(),showOnCreate:!0,interactive:!0,trigger:"manual",placement:"bottom-start",theme:"light",arrow:!0})},onUpdate:o=>{o.clientRect&&t[0].setProps({getReferenceClientRect:o.clientRect})},onKeyDown:o=>{let s=Alpine.store("filamentCommentsMentionsFiltered").items,l=Alpine.store("filamentCommentsMentionsFiltered").selectedIndex;if(o.event.key==="ArrowDown")return Alpine.store("filamentCommentsMentionsFiltered").selectedIndex=(l+1)%s.length,!0;if(o.event.key==="ArrowUp")return Alpine.store("filamentCommentsMentionsFiltered").selectedIndex=(l-1+s.length)%s.length,!0;if(o.event.key==="Enter"){let a=s[l];return a&&i({id:a.id,label:a.name}),!0}return o.event.key==="Escape"?(t[0].hide(),!0):!1},onExit:()=>{t[0].hide()}}}}},Kf=Xg;document.addEventListener("alpine:init",()=>{Alpine.data("editor",(n,e,t)=>{let r;return{updatedAt:Date.now(),init(){let i=this;r=new gi({element:this.$refs.element,extensions:[of,lf.configure({HTMLAttributes:{class:"mention"},suggestion:Kf(e)}),af.configure({placeholder:"Type your comment\u2026"})],editorProps:{attributes:{class:"comm:prose comm:dark:prose-invert comm:prose-sm comm:sm:prose-base comm:lg:prose-lg comm:xl:prose-2xl comm:focus:outline-none comm:p-4 comm:min-w-full comm:w-full comm:rounded-lg comm:border comm:border-gray-300 comm:dark:border-gray-700"}},placeholder:"Type something...",content:n,onCreate({editor:o}){i.updatedAt=Date.now()},onUpdate({editor:o}){Livewire.dispatchTo(`commentions::${t}`,"body:updated",{value:o.getHTML()}),i.updatedAt=Date.now()},onSelectionUpdate({editor:o}){i.updatedAt=Date.now()}}),Livewire.on(`${t}:content:cleared`,()=>{r.commands.setContent("")})},isLoaded(){return r},isActive(i,o={}){return r.isActive(i,o)}}})});

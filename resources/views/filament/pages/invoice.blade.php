@php
    $company = \Filament\Facades\Filament::getTenant();
    $invoiceable = $invoice->invoiceable;
    $billTo = $invoice->invoiceable->getBillToInfo();
@endphp

<x-filament-panels::page>
    <div style="direction: ltr">
        <div class="mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-blue-600 text-white p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">{{ $invoiceable->getInvoiceTitle() }}</h1>
                        <p class="text-blue-200">Invoice #{{ $invoiceable->getInvoiceNumber() }}</p>
                        <p class="text-blue-200">Date: {{ $invoiceable->created_at?->format('Y-m-d') }}</p>
                    </div>
                    <div class="text-right">
                        <h2 class="text-xl font-semibold">{{ $company->name }}</h2>
                        <p class="text-blue-200">{{ $company->address }}</p>
                        <p class="text-blue-200">{{ $company->city?->name }}, {{ $company->country?->name }}</p>
                        <p class="text-blue-200">{{ $company->email }}</p>
                        <p class="text-blue-200">{{ $company->dial_code . $company->phone }}</p>
                    </div>
                </div>
            </div>

            <!-- Bill To Section -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Bill To:</h3>
                        <div class="text-gray-600">
                            <p class="font-medium text-gray-800">{{ data_get($billTo, 'company_name') }}</p>
                            <p>{{ data_get($billTo, 'address') }}</p>
                            <p>
                                {{ data_get($billTo, 'city') }}, {{ data_get($billTo, 'country') }}
                            </p>
                            <p>
                                {{ data_get($billTo, 'email') }}
                            </p>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Payment Details:</h3>
                        <div class="text-gray-600">
                            <p><span class="font-medium">Due Date:</span> {{ $invoiceable->created_at?->format('Y-m-d') }}</p>
                            <p><span class="font-medium">Payment Terms:</span>{{ $invoiceable->getPaymentTerm() }}</p>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="overflow-x-auto mb-8">
                    <table class="w-full border-collapse">
                        <thead>
                        <tr class="bg-gray-50">
                            <th class="border border-gray-200 px-4 py-3 text-left font-semibold text-gray-800">Description</th>
                            <th class="border border-gray-200 px-4 py-3 text-center font-semibold text-gray-800">Qty</th>
                            <th class="border border-gray-200 px-4 py-3 text-right font-semibold text-gray-800">Rate</th>
                            <th class="border border-gray-200 px-4 py-3 text-right font-semibold text-gray-800">Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($invoice->invoiceItems as $item)
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-200 px-4 py-3">
                                    <div class="font-medium text-gray-800">{{ $item->itemable->getInvoiceItemName() }}</div>
                                </td>
                                <td class="border border-gray-200 px-4 py-3 text-center">{{ $item->itemable->getInvoiceableQuantity() }}</td>
                                <td class="border border-gray-200 px-4 py-3 text-right">
                                    {{ \Illuminate\Support\Number::currency($item->itemable->getInvoiceableAmount(), $item->itemable->getInvoiceableCurrency()) }}
                                </td>
                                <td class="border border-gray-200 px-4 py-3 text-right font-medium">
                                    {{ \Illuminate\Support\Number::currency($item->total_amount, $item->itemable->getInvoiceableCurrency()) }}
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Totals Section -->
                <div class="flex justify-end">
                    <div class="w-full max-w-md">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Invoice Summary</h3>

                            @foreach(distinct_currencies() as $currency)
                                @if($invoice->{"total_amount_{$currency}"} > 0)
                                    <div class="mb-4 pb-4 border-b border-gray-200">
                                        <h4 class="font-medium text-gray-700 mb-2">{{ $currency }} Amounts:</h4>
                                        <div class="flex justify-between text-gray-600 mb-1">
                                            <span>Subtotal ({{ $currency }}):</span>
                                            <span>
                                            {{ Number::currency($invoice->{"total_amount_{$currency}"}, $currency) }}
                                        </span>
                                        </div>
                                        {{--                                    <div class="flex justify-between text-gray-600 mb-1">--}}
                                        {{--                                        <span>Tax (10%):</span>--}}
                                        {{--                                        <span>$310.00</span>--}}
                                        {{--                                    </div>--}}
                                        <div class="flex justify-between font-medium text-gray-800 text-lg border-t border-gray-300 pt-2">
                                            <span>Total {{ $currency }}:</span>
                                            <span>
                                            {{ Number::currency($invoice->{"total_amount_{$currency}"}, $currency) }}
                                        </span>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Notes and Terms -->
{{--                <div class="mt-8 pt-8 border-t border-gray-200">--}}
{{--                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">--}}
{{--                        <div>--}}
{{--                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Notes:</h3>--}}
{{--                            <p class="text-gray-600 text-sm leading-relaxed">--}}
{{--                                Thank you for your business! Please remit payment within 30 days of receiving this invoice.--}}
{{--                                For USD payments, please use our international account. For EGP payments, use our local Egyptian account.--}}
{{--                            </p>--}}
{{--                        </div>--}}
{{--                        <div>--}}
{{--                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Payment Methods:</h3>--}}
{{--                            <div class="text-sm text-gray-600 space-y-1">--}}
{{--                                <p><span class="font-medium">Bank Transfer:</span> Available for both currencies</p>--}}
{{--                                <p><span class="font-medium">PayPal:</span> USD payments only</p>--}}
{{--                                <p><span class="font-medium">Local Transfer:</span> EGP payments</p>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
            </div>

            <!-- Footer -->
{{--            <div class="bg-gray-50 px-6 py-4 text-center text-sm text-gray-600">--}}
{{--                <p>This invoice was generated electronically and is valid without signature.</p>--}}
{{--            </div>--}}
        </div>
    </div>
</x-filament-panels::page>

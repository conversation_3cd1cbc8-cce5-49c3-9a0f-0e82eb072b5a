<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cities', function (Blueprint $table) {
            $table->id();
            $table->json('name')->nullable(); // Translatable field
            $table->string('code')->nullable();
            $table->foreignId('country_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Country::query()
            ->create([
                'name' => 'Egypt',
                'code' => 'EG',
                'company_id' => 1,
                'incremental_id' => 1,
            ]);

        \App\Models\Country::query()
            ->create([
                'name' => 'United States',
                'code' => 'US',
                'company_id' => 1,
                'incremental_id' => 2,
            ]);

        // china
        \App\Models\Country::query()
            ->create([
                'name' => 'China',
                'code' => 'CN',
                'company_id' => 1,
                'incremental_id' => 3,
            ]);

        \App\Models\City::query()
            ->create([
                'name' => 'New York',
                'code' => 'NYC',
                'country_id' => 2,
                'incremental_id' => 2,
                'company_id' => 1,
            ]);

        \App\Models\City::query()
            ->create([
                'name' => 'Shanghai',
                'code' => 'SHA',
                'country_id' => 3,
                'incremental_id' => 3,
                'company_id' => 1,
            ]);

        \App\Models\City::query()
            ->create([
                'name' => 'Beijing',
                'code' => 'BJS',
                'country_id' => 3,
                'incremental_id' => 4,
                'company_id' => 1,
            ]);

        \App\Models\City::query()
            ->create([
                'name' => 'Guangzhou',
                'code' => 'CAN',
                'country_id' => 3,
                'incremental_id' => 5,
                'company_id' => 1,
            ]);

        \App\Models\City::query()
            ->create([
                'name' => 'Cairo',
                'code' => 'CAI',
                'country_id' => 1,
                'incremental_id' => 1,
                'company_id' => 1,
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cities');
    }
};

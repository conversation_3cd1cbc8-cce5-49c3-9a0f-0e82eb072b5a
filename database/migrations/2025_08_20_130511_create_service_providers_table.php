<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->text('email')->nullable();
            $table->string('dial_code')->nullable();
            $table->string('phone')->nullable();
            $table->string('company_name')->nullable();
            $table->text('address')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('bank_account_number')->nullable();
            $table->string('iban_number')->nullable();
            $table->string('swift_code')->nullable();
            $table->string('payment_term_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('country_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('city_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('service_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\ServiceProvider::query()->create([
            'name' => 'Clearance Provider',
            'email' => '<EMAIL>',
            'dial_code' => '+20',
            'phone' => '**********',
            'company_name' => 'Clearance Company',
            'address' => 'Clearance Address',
            'bank_name' => 'Clearance Bank',
            'bank_account_number' => '**********',
            'iban_number' => '**********',
            'swift_code' => '**********',
            'payment_term_id' => 1,
            'country_id' => 1,
            'city_id' => 1,
            'service_id' => 1,
            'incremental_id' => 1,
            'company_id' => 1,
        ]);

        \App\Models\ServiceProvider::query()->create([
            'name' => 'Freight Provider',
            'email' => '<EMAIL>',
            'dial_code' => '+20',
            'phone' => '**********',
            'company_name' => 'Freight Company',
            'address' => 'Freight Address',
            'bank_name' => 'Freight Bank',
            'bank_account_number' => '**********',
            'iban_number' => '**********',
            'swift_code' => '**********',
            'payment_term_id' => 1,
            'country_id' => 1,
            'city_id' => 1,
            'service_id' => 2,
            'incremental_id' => 2,
            'company_id' => 1,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_providers');
    }
};

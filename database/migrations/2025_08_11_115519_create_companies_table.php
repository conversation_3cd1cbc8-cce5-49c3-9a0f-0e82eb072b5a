<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('dial_code')->nullable();
            $table->string('phone')->nullable();
//            $table->foreignId('country_id')->nullable()->constrained()->nullOnDelete();
//            $table->foreignId('city_id')->nullable()->constrained()->nullOnDelete();
            $table->text('address')->nullable();
            $table->text('logo')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Company::query()->create([
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'dial_code' => '+20',
            'phone' => '1234567890',
            'address' => 'Test Address',
        ]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_terms', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('handle')->nullable(); // Note: typo in JSON, keeping as is
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\PaymentTerm::query()->create([
            'name' => 'Cash on advance',
            'handle' => 'cash_on_advance',
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\PaymentTerm::query()->create([
            'name' => '100% cash on advance',
            'handle' => '100_cash_on_advance',
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        \App\Models\PaymentTerm::query()->create([
            'name' => '50% cash on advance, 50% on delivery',
            'handle' => '50_cash_on_advance_50_on_delivery',
            'company_id' => 1,
            'incremental_id' => 3,
        ]);

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_terms');
    }
};

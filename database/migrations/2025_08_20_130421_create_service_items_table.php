<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_items', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable();
            $table->foreignId('service_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\ServiceItem::query()->create([
            'name' => 'NTRA Clearance',
            'service_id' => 1,
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\ServiceItem::query()->create([
            'name' => 'DTHC',
            'service_id' => 2,
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        \App\Models\ServiceItem::query()->create([
            'name' => 'Bills of Lading Fee',
            'service_id' => 2,
            'company_id' => 1,
            'incremental_id' => 3,
        ]);

        \App\Models\ServiceItem::query()->create([
            'name' => 'Trucking',
            'service_id' => 3,
            'company_id' => 1,
            'incremental_id' => 4,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_items');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('airports', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('iata_code')->nullable();
            $table->string('icao_code')->nullable();
            $table->string('un_locode')->nullable();
            $table->foreignId('country_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('city_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Airport::query()->create([
            'name' => 'Cairo International Airport',
            'iata_code' => 'CAI',
            'icao_code' => 'HECA',
            'un_locode' => 'EGCAI',
            'country_id' => 1,
            'city_id' => 1,
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\Airport::query()->create([
            'name' => 'New York JFK Airport',
            'iata_code' => 'JFK',
            'icao_code' => 'KJFK',
            'un_locode' => 'USNYC',
            'country_id' => 2,
            'city_id' => 2,
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        \App\Models\Airport::query()->create([
            'name' => 'Shanghai Pudong International Airport',
            'iata_code' => 'PVG',
            'icao_code' => 'ZSPD',
            'un_locode' => 'CNSHA',
            'country_id' => 3,
            'city_id' => 3,
            'company_id' => 1,
            'incremental_id' => 3,
        ]);

        \App\Models\Airport::query()->create([
            'name' => 'Beijing Capital International Airport',
            'iata_code' => 'PEK',
            'icao_code' => 'ZBAA',
            'un_locode' => 'CNBJS',
            'country_id' => 3,
            'city_id' => 4,
            'company_id' => 1,
            'incremental_id' => 4,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('airports');
    }
};

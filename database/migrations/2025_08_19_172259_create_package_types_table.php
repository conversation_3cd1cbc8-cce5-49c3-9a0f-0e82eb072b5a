<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('package_types', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('handle')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // add default package types from logistics like BOX, PALLET, DRUM, etc
        \App\Models\PackageType::query()->create([
            'name' => 'Box',
            'handle' => 'box',
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\PackageType::query()->create([
            'name' => 'Pallet',
            'handle' => 'pallet',
            'company_id' => 1,
            'incremental_id' => 2,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('package_types');
    }
};

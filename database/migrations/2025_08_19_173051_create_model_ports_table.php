<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('model_ports', function (Blueprint $table) {
            $table->id();
            $table->nullableMorphs('model');
            $table->nullableMorphs('port');
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\ModelPort::query()->create([
            'model_id' => 1,
            'model_type' => 'App\Models\Client',
            'port_id' => 1,
            'port_type' => 'App\Models\Seaport',
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\ModelPort::query()->create([
            'model_id' => 1,
            'model_type' => 'App\Models\Client',
            'port_id' => 1,
            'port_type' => 'App\Models\Airport',
            'company_id' => 1,
            'incremental_id' => 2,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('model_ports');
    }
};

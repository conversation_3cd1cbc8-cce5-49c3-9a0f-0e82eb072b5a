<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seaports', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('un_locode')->nullable();
            $table->foreignId('country_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('city_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Seaport::query()->create([
            'name' => 'Cairo Port',
            'un_locode' => 'EGCAI',
            'country_id' => 1,
            'city_id' => 1,
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\Seaport::query()->create([
            'name' => 'New York Port',
            'un_locode' => 'USNYC',
            'country_id' => 2,
            'city_id' => 2,
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        \App\Models\Seaport::query()->create([
            'name' => 'Shanghai Port',
            'un_locode' => 'CNSHA',
            'country_id' => 3,
            'city_id' => 3,
            'company_id' => 1,
            'incremental_id' => 3,
        ]);

        \App\Models\Seaport::query()->create([
            'name' => 'Beijing Port',
            'un_locode' => 'CNBJS',
            'country_id' => 3,
            'city_id' => 4,
            'company_id' => 1,
            'incremental_id' => 4,
        ]);

        \App\Models\Seaport::query()->create([
            'name' => 'Guangzhou Port',
            'un_locode' => 'CNCAN',
            'country_id' => 3,
            'city_id' => 5,
            'company_id' => 1,
            'incremental_id' => 5,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seaports');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sell_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->nullable()->constrained()->nullOnDelete();
            $table->string('status')->nullable();
            $table->string('origin')->nullable(); // Local or Foreign
            $table->string('incoterm')->nullable();
            $table->foreignId('payment_term_id')->nullable()->constrained()->nullOnDelete();
            $table->string('custom_payment_term')->nullable();
            $table->string('model_of_shipping')->nullable(); // Air, Sea, Courier
            $table->foreignId('pickup_country_id')->nullable()->constrained('countries')->nullOnDelete();
            $table->foreignId('pickup_city_id')->nullable()->constrained('cities')->nullOnDelete();
            $table->text('pickup_address')->nullable();
            $table->nullableMorphs('port_of_loading');
            $table->foreignId('delivery_country_id')->nullable()->constrained('countries')->nullOnDelete();
            $table->foreignId('delivery_city_id')->nullable()->constrained('cities')->nullOnDelete();
            $table->text('delivery_address')->nullable();
            $table->nullableMorphs('port_of_discharge');
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sell_orders');
    }
};

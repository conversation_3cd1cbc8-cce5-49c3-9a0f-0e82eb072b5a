<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->json('name')->nullable(); // Translatable field
            $table->string('code')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Country::query()
            ->create([
                'name' => 'Egypt',
                'code' => 'EG',
                'company_id' => 1,
                'incremental_id' => 1,
            ]);

        \App\Models\Country::query()
            ->create([
                'name' => 'United States',
                'code' => 'US',
                'company_id' => 1,
                'incremental_id' => 2,
            ]);

        // china
        \App\Models\Country::query()
            ->create([
                'name' => 'China',
                'code' => 'CN',
                'company_id' => 1,
                'incremental_id' => 3,
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('hs_code')->nullable();
            $table->string('commercial_name')->nullable();
            $table->double('buy_price')->nullable();
            $table->string('buy_price_currency')->nullable();
            $table->double('sell_price')->nullable();
            $table->string('sell_price_currency')->nullable();
            $table->text('image')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Product::query()->create([
            'name' => 'Test Product',
            'hs_code' => '123456',
            'commercial_name' => 'Test Commercial Name',
            'buy_price' => 150,
            'buy_price_currency' => 'EGP',
            'sell_price' => 300,
            'sell_price_currency' => 'EGP',
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\Product::query()->create([
            'name' => 'Test Product 2',
            'hs_code' => '1234',
            'commercial_name' => 'Test Commercial Name 2',
            'buy_price' => 100,
            'buy_price_currency' => 'EGP',
            'sell_price' => 200,
            'sell_price_currency' => 'EGP',
            'company_id' => 1,
            'incremental_id' => 2,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('hs_code')->nullable();
            $table->string('commercial_name')->nullable();
            $table->double('buy_price')->nullable();
            $table->string('buy_price_currency')->nullable();
            $table->double('sell_price')->nullable();
            $table->string('sell_price_currency')->nullable();
            $table->text('image')->nullable();
            $table->string('type')->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Product::query()->create([
            'name' => 'Raw 1',
            'hs_code' => '123456',
            'commercial_name' => 'Raw 1',
            'buy_price' => 150,
            'buy_price_currency' => 'EGP',
            'sell_price' => 300,
            'sell_price_currency' => 'EGP',
            'company_id' => 1,
            'incremental_id' => 1,
            'type' => \App\Enums\ProductType::RAW->value,
        ]);

        \App\Models\Product::query()->create([
            'name' => 'Raw 2',
            'hs_code' => '1234',
            'commercial_name' => 'Raw 2',
            'buy_price' => 100,
            'buy_price_currency' => 'EGP',
            'sell_price' => 200,
            'sell_price_currency' => 'EGP',
            'company_id' => 1,
            'incremental_id' => 2,
            'type' => \App\Enums\ProductType::RAW->value,
        ]);

        \App\Models\Product::query()->create([
            'name' => 'Finished 1',
            'hs_code' => '123456',
            'commercial_name' => 'Finished 1',
            'buy_price' => 150,
            'buy_price_currency' => 'EGP',
            'sell_price' => 300,
            'sell_price_currency' => 'EGP',
            'company_id' => 1,
            'incremental_id' => 3,
            'type' => \App\Enums\ProductType::FINISHED->value,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};

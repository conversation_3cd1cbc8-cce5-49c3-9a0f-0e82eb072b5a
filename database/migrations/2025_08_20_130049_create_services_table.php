<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->text('name')->nullable(); // Translatable field
            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        \App\Models\Service::query()->create([
            'name' => [
                'en' => 'Clearance',
                'ar' => 'تخليص',
            ],
            'company_id' => 1,
            'incremental_id' => 1,
        ]);

        \App\Models\Service::query()->create([
            'name' => [
                'en' => 'Freight',
                'ar' => 'شحن',
            ],
            'company_id' => 1,
            'incremental_id' => 2,
        ]);

        \App\Models\Service::query()->create([
            'name' => [
                'en' => 'Trucking',
                'ar' => 'نقل',
            ],
            'company_id' => 1,
            'incremental_id' => 3,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};

{"resources": [{"name": "Service", "navigation_group": "Services", "fields": [{"name": "name", "type": "text", "field": "Translatable", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "serviceProviders", "type": "hasMany", "related_model": "ServiceProvider", "form_field": "relation_manager", "repeater_type": null, "instructions": null}, {"name": "serviceItems", "type": "hasMany", "related_model": "ServiceItem", "form_field": "repeater", "repeater_type": "normal_repeater", "instructions": null}]}, {"name": "ServiceItem", "navigation_group": "Services", "fields": [{"name": "name", "type": "text", "field": "Textarea", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "service_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "service", "type": "belongsTo", "related_model": "Service", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}]}, {"name": "ServiceProvider", "navigation_group": "Services", "fields": [{"name": "name", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "email", "type": "text", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "dial_code", "type": "string", "field": "Select", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "phone", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "company_name", "type": "string", "field": "TextInput", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "address", "type": "text", "field": "Textarea", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "service_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "service", "type": "belongsTo", "related_model": "Service", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}, {"name": "serviceProviderItems", "type": "hasMany", "related_model": "ServiceProviderItem", "form_field": "repeater", "repeater_type": "normal_repeater", "instructions": null}]}, {"name": "ServiceProviderItem", "navigation_group": "Services", "fields": [{"name": "service_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "service_item_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "service_provider_id", "type": "foreignId", "field": "BelongsToSelect", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}, {"name": "price", "type": "double", "field": "PriceField", "repeater_type": null, "required": true, "use_in_table": true, "multiple": false, "searchable": false, "preload": false, "translateable": false, "priceable": false, "instructions": null}], "relations": [{"name": "service", "type": "belongsTo", "related_model": "Service", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}, {"name": "serviceItem", "type": "belongsTo", "related_model": "ServiceItem", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}, {"name": "serviceProvider", "type": "belongsTo", "related_model": "ServiceProvider", "form_field": "belongs_to_select", "repeater_type": null, "instructions": null}]}]}